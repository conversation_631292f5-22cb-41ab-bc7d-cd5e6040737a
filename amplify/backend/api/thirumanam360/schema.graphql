# This "input" configures a global authorization rule to enable public access to
# all models in this schema. Learn more about authorization rules here: https://docs.amplify.aws/cli/graphql/authorization-rules
input AMPLIFY { globalAuthRule: AuthRule = { allow: public } } # FOR TESTING ONLY!

type Vendor @model @auth(rules: [
  { allow: private, provider: userPools },
  { allow: public, provider: apiKey, operations: [read] }
]) {
  id: ID!
  userId: String! @index(name: "byUserId")
  name: String!
  description: String
  contact: String!
  email: String
  address: String
  city: String!
  state: String!
  pincode: String
  website: String
  category: String!
  profilePhoto: String
  gallery: [String]
  services: [Service]
  socialMedia: SocialMedia
  experience: String
  events: String
  responseTime: String
  rating: Float
  reviewCount: Int
  verified: Boolean
  featured: Boolean
  availability: String
  priceRange: String
  specializations: [String]
  awards: [String]
  languages: [String]
  coverage: [String]
  equipment: [String]
  status: String
}

type Service {
  name: String
  price: String
  description: String
}

type SocialMedia {
  facebook: String
  instagram: String
  youtube: String
}

type Venue @model @auth(rules: [
  { allow: private, provider: userPools },
  { allow: public, provider: apiKey, operations: [read] }
]) {
  id: ID!
  userId: String! @index(name: "byUserId")
  name: String!
  description: String
  type: String!
  capacity: String!
  location: String!
  city: String!
  state: String!
  fullAddress: String
  pincode: String
  contactPhone: String
  contactEmail: String
  website: String
  price: String!
  priceRange: String
  images: [String]
  amenities: [String]
  spaces: [VenueSpace]
  packages: [VenuePackage]
  socialMedia: SocialMedia
  rating: Float
  reviewCount: Int
  bookings: Int
  verified: Boolean
  featured: Boolean
  status: String!
  availability: String
  policies: VenuePolicies
  coordinates: VenueCoordinates
  operatingHours: VenueOperatingHours
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type VenueSpace {
  name: String!
  capacity: String!
  area: String
  price: String!
  description: String
  amenities: [String]
  images: [String]
}

type VenuePackage {
  name: String!
  price: String!
  duration: String
  description: String
  includes: [String]
  excludes: [String]
  terms: String
}

type VenuePolicies {
  cancellation: String
  advance: String
  catering: String
  decoration: String
  alcohol: String
  music: String
  parking: String
}

type VenueCoordinates {
  latitude: Float
  longitude: Float
}

type VenueOperatingHours {
  monday: String
  tuesday: String
  wednesday: String
  thursday: String
  friday: String
  saturday: String
  sunday: String
}

type Shop @model @auth(rules: [
  { allow: owner, ownerField: "userId", provider: userPools },
  { allow: private, provider: userPools, operations: [read] },
  { allow: public, provider: apiKey, operations: [read] }
]) {
  id: ID!
  userId: String! @index(name: "byUserId")
  name: String!
  category: String!
  price: String!
  originalPrice: String
  discount: Int
  stock: Int!
  sku: String
  brand: String
  featured: Boolean
  description: String
  features: [String]
  sizes: [String]
  colors: [String]
  images: [String]
  specifications: ShopSpecifications
  rating: Float
  reviewCount: Int
  inStock: Boolean!
  status: String!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type ShopSpecifications {
  fabric: String
  work: String
  occasion: String
  care: String
  delivery: String
  returnPolicy: String
}

type Review @model @auth(rules: [
  { allow: owner, ownerField: "userId", provider: userPools },
  { allow: private, provider: userPools, operations: [create, read, update] },
  { allow: public, provider: apiKey, operations: [create, read, update] }
]) {
  id: ID!
  userId: String! @index(name: "byUserId")
  name: String!
  email: String!
  location: String
  weddingDate: AWSDate
  category: ReviewCategory!
  rating: Int!
  title: String!
  review: String!
  wouldRecommend: Boolean!
  verified: Boolean
  status: ReviewStatus!
  # Entity-specific fields for shop/venue/vendor reviews
  entityType: EntityType
  entityId: String @index(name: "byEntityId", sortKeyFields: ["createdAt"])
  # Composite index for user-entity uniqueness
  userEntityComposite: String @index(name: "byUserEntity")
  serviceRating: Int # Rating for specific service quality
  valueRating: Int # Rating for value for money
  communicationRating: Int # Rating for communication
  professionalismRating: Int # Rating for professionalism
  images: [String] # Review images
  helpfulCount: Int # Number of users who found this review helpful
  # Additional metadata
  purchaseVerified: Boolean # Whether the purchase/booking is verified
  reviewHelpfulUsers: [String] # List of user IDs who marked this helpful
  # Vendor response fields
  vendorResponse: String # Vendor's response to the review
  responseDate: AWSDateTime # Date when vendor responded
  # Review routing fields
  reviewTarget: ReviewTarget! # Determines if review goes to admin or vendor
  adminNotes: String # Admin notes for moderation
  moderatedBy: String # Admin who moderated the review
  moderatedAt: AWSDateTime # When the review was moderated
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

enum ReviewCategory {
  PLATFORM      # Reviews about the overall platform experience - goes to admin
  PRODUCT       # Reviews about specific products/services - goes to vendor
  VENDOR        # Reviews about vendor services - goes to vendor
  VENUE         # Reviews about venue services - goes to vendor
  SHOP          # Reviews about shop products - goes to vendor
}

enum ReviewStatus {
  PENDING
  APPROVED
  REJECTED
}

enum ReviewTarget {
  ADMIN     # Platform reviews go to admin dashboard
  VENDOR    # Product/service reviews go to vendor dashboard
}

enum EntityType {
  SHOP
  VENUE
  VENDOR
  PLATFORM
}

type Contact @model @auth(rules: [
  { allow: private, provider: userPools },
  { allow: public, provider: apiKey, operations: [create] }
]) {
  id: ID!
  name: String!
  email: String!
  phone: String
  subject: String!
  message: String!
  inquiryType: ContactInquiryType!
  status: ContactStatus!
  priority: ContactPriority
  assignedTo: String
  responseMessage: String
  respondedAt: AWSDateTime
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

enum ContactInquiryType {
  GENERAL
  VENDOR
  SUPPORT
  FEEDBACK
  BUSINESS
}

enum ContactStatus {
  NEW
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum ContactPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

type Inquiry @model @auth(rules: [
  { allow: private, provider: userPools, operations: [create, read, update, delete] },
  { allow: public, provider: apiKey, operations: [create, read] }
]) {
  id: ID!
  vendorUserId: String! @index(name: "byVendorUserId")
  vendorId: String! @index(name: "byVendorId")
  vendorName: String!
  customerUserId: String @index(name: "byCustomerUserId")
  customerName: String!
  customerEmail: String!
  customerPhone: String
  eventDate: AWSDate
  message: String!
  inquiryType: InquiryType!
  status: InquiryStatus!
  priority: InquiryPriority
  budget: String
  guestCount: String
  venue: String
  additionalServices: [String]
  preferredContactTime: String
  responseMessage: String
  respondedAt: AWSDateTime
  assignedTo: String
  followUpDate: AWSDate
  notes: String
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type UserProfile @model @auth(rules: [
  { allow: owner, provider: userPools },
  { allow: private, provider: userPools, operations: [read] },
  { allow: public, provider: apiKey, operations: [read] }
]) {
  id: ID!
  userId: String! @index(name: "byUserId")
  firstName: String!
  lastName: String!
  email: String!
  phone: String
  dateOfBirth: AWSDate
  gender: Gender
  address: String
  city: String
  state: String
  pincode: String
  country: String
  profilePhoto: String
  bio: String
  website: String
  socialMedia: ProfileSocialMedia
  preferences: ProfilePreferences
  businessInfo: BusinessInfo
  isVendor: Boolean
  isAdmin: Boolean
  isSuperAdmin: Boolean
  role: UserRole
  permissions: [String]
  registrationSource: RegistrationSource
  accountType: AccountType
  userTypeForm: UserTypeFormData
  isVerified: Boolean
  lastLoginAt: AWSDateTime
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type ProfileSocialMedia {
  facebook: String
  instagram: String
  twitter: String
  linkedin: String
  youtube: String
}

type ProfilePreferences {
  language: String
  currency: String
  timezone: String
  notifications: NotificationSettings
  privacy: PrivacySettings
}

type NotificationSettings {
  email: Boolean
  sms: Boolean
  push: Boolean
  marketing: Boolean
}

type PrivacySettings {
  profileVisibility: ProfileVisibility
  contactVisibility: ContactVisibility
  showOnlineStatus: Boolean
}

type BusinessInfo {
  businessName: String
  businessType: String
  businessAddress: String
  businessPhone: String
  businessEmail: String
  businessWebsite: String
  gstNumber: String
  panNumber: String
  businessLicense: String
}

enum Gender {
  MALE
  FEMALE
  OTHER
  PREFER_NOT_TO_SAY
}

enum UserRole {
  CUSTOMER
  VENDOR
  ADMIN
  SUPER_ADMIN
}

enum FavoriteEntityType {
  VENDOR
  VENUE
  SHOP_ITEM
}

enum CartItemStatus {
  ACTIVE
  SAVED_FOR_LATER
  REMOVED
}

enum RegistrationSource {
  CUSTOMER_FORM
  VENDOR_FORM
  ADMIN_FORM
  BULK_IMPORT
  SOCIAL_LOGIN
  INVITATION
  MIGRATION
}

enum AccountType {
  PERSONAL
  BUSINESS
  ORGANIZATION
  ADMIN_ACCOUNT
}

type UserTypeFormData {
  formType: String
  submissionDate: AWSDateTime
  formVersion: String
  businessDetails: BusinessFormDetails
  personalDetails: PersonalFormDetails
  adminDetails: AdminFormDetails
  verificationStatus: String
  approvalStatus: String
  submittedBy: String
  reviewedBy: String
  reviewDate: AWSDateTime
  reviewNotes: String
}

type BusinessFormDetails {
  businessName: String
  businessType: String
  businessCategory: String
  businessDescription: String
  servicesOffered: [String]
  experienceYears: Int
  teamSize: Int
  businessLicense: String
  gstNumber: String
  panNumber: String
  businessAddress: String
  businessPhone: String
  businessEmail: String
  businessWebsite: String
  portfolioLinks: [String]
  socialMediaLinks: [String]
  operatingHours: String
  serviceAreas: [String]
  priceRange: String
  specializations: [String]
}

type PersonalFormDetails {
  firstName: String
  lastName: String
  dateOfBirth: AWSDate
  gender: String
  weddingDate: AWSDate
  partnerName: String
  weddingLocation: String
  budgetRange: String
  weddingStyle: String
  guestCount: Int
  interests: [String]
  preferences: [String]
  referralSource: String
}

type AdminFormDetails {
  adminLevel: String
  department: String
  responsibilities: [String]
  accessLevel: String
  reportingTo: String
  startDate: AWSDate
  employeeId: String
  designation: String
}

enum ProfileVisibility {
  PUBLIC
  PRIVATE
  FRIENDS_ONLY
}

enum ContactVisibility {
  PUBLIC
  PRIVATE
  VERIFIED_ONLY
}

enum InquiryType {
  VENDOR_INQUIRY
  VENUE_INQUIRY
  SERVICE_QUOTE
  AVAILABILITY_CHECK
  GENERAL_QUESTION
  BOOKING_REQUEST
}

enum InquiryStatus {
  NEW
  CONTACTED
  QUOTED
  NEGOTIATING
  CONFIRMED
  COMPLETED
  CANCELLED
  EXPIRED
}

enum InquiryPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

input ShopSpecificationsInput {
  fabric: String
  work: String
  occasion: String
  care: String
  delivery: String
  returnPolicy: String
}

type Blog @model @auth(rules: [
  { allow: owner, ownerField: "authorId", provider: userPools },
  { allow: private, provider: userPools, operations: [read] },
  { allow: public, provider: apiKey, operations: [read] }
]) {
  id: ID!
  title: String!
  content: String!
  excerpt: String
  category: BlogCategory!
  authorId: String! @index(name: "byAuthor")
  authorName: String!
  authorType: AuthorType!
  featuredImage: String
  tags: [String]
  status: BlogStatus!
  views: Int
  likes: Int
  comments: Int
  isPinned: Boolean
  isFeatured: Boolean
  publishedAt: AWSDateTime
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type Booking @model @auth(rules: [
  { allow: owner, provider: userPools },
  { allow: private, provider: userPools, operations: [read, create, update] },
  { allow: public, provider: apiKey, operations: [read] }
]) {
  id: ID!
  customerId: String! @index(name: "byCustomer")
  customerName: String!
  customerEmail: String!
  customerPhone: String
  entityId: String! @index(name: "byEntity")
  entityType: BookingEntityType!
  entityName: String!
  vendorId: String @index(name: "byVendor")
  eventDate: AWSDate!
  eventTime: String!
  guestCount: Int!
  eventType: String!
  duration: String
  specialRequests: String
  budget: String
  contactPreference: ContactPreference!
  status: BookingStatus!
  priority: BookingPriority
  notes: String
  vendorNotes: String
  estimatedCost: String
  finalCost: String
  advanceAmount: String
  balanceAmount: String
  paymentStatus: PaymentStatus
  paymentMethod: String
  transactionId: String
  contractSigned: Boolean
  contractUrl: String
  cancellationReason: String
  cancellationDate: AWSDateTime
  refundAmount: String
  refundStatus: RefundStatus
  followUpDate: AWSDate
  reminderSent: Boolean
  customerRating: Int
  customerReview: String
  vendorRating: Int
  vendorReview: String
  communicationLog: [CommunicationEntry]
  attachments: [String]
  metadata: AWSJSON
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type CommunicationEntry {
  timestamp: AWSDateTime!
  type: CommunicationType!
  from: String!
  to: String!
  subject: String
  message: String!
  attachments: [String]
  status: String
}

type NewsletterSubscription @model @auth(rules: [
  { allow: private, provider: userPools },
  { allow: public, provider: apiKey, operations: [create, read] }
]) {
  id: ID!
  email: String! @index(name: "byEmail")
  firstName: String
  lastName: String
  phone: String
  city: String
  state: String
  weddingDate: AWSDate
  interests: [NewsletterInterest]
  source: SubscriptionSource!
  status: SubscriptionStatus!
  preferences: NewsletterPreferences
  userId: String @index(name: "byUserId")
  subscribedAt: AWSDateTime!
  unsubscribedAt: AWSDateTime
  lastEmailSent: AWSDateTime
  emailsSent: Int
  emailsOpened: Int
  emailsClicked: Int
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type NewsletterPreferences {
  weddingTips: Boolean
  vendorRecommendations: Boolean
  specialOffers: Boolean
  eventUpdates: Boolean
  blogUpdates: Boolean
  frequency: EmailFrequency
}

enum NewsletterInterest {
  PHOTOGRAPHY
  VIDEOGRAPHY
  CATERING
  DECORATION
  MAKEUP
  VENUES
  SHOPPING
  PLANNING
  HONEYMOON
  JEWELRY
  INVITATIONS
  MUSIC
  TRANSPORTATION
}

enum SubscriptionSource {
  HOMEPAGE
  SIGNUP_FORM
  VENDOR_PAGE
  VENUE_PAGE
  SHOP_PAGE
  BLOG_PAGE
  OFFERS_PAGE
  CONTACT_FORM
  MOBILE_APP
  SOCIAL_MEDIA
  REFERRAL
  OTHER
}

enum SubscriptionStatus {
  ACTIVE
  UNSUBSCRIBED
  BOUNCED
  COMPLAINED
  PENDING_CONFIRMATION
}

enum EmailFrequency {
  DAILY
  WEEKLY
  BIWEEKLY
  MONTHLY
  SPECIAL_ONLY
}

enum BlogCategory {
  WEDDING_PLANNING
  VENUE_SELECTION
  PHOTOGRAPHY_VIDEOGRAPHY
  CATERING_FOOD
  DECORATIONS_THEMES
  BUDGET_FINANCE
  FASHION_STYLE
  REAL_WEDDINGS
  EXPERT_TIPS
  VENDOR_SPOTLIGHT
}

enum BlogStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum AuthorType {
  VENDOR
  ADMIN
  EXPERT
}

enum BookingEntityType {
  VENDOR
  VENUE
}

enum BookingStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  REJECTED
}

enum BookingPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum ContactPreference {
  PHONE
  EMAIL
  WHATSAPP
}

enum PaymentStatus {
  PENDING
  PARTIAL
  PAID
  REFUNDED
  FAILED
}

enum RefundStatus {
  NOT_APPLICABLE
  REQUESTED
  PROCESSING
  COMPLETED
  REJECTED
}

enum CommunicationType {
  EMAIL
  PHONE
  SMS
  WHATSAPP
  IN_PERSON
  VIDEO_CALL
  SYSTEM
}

type ChecklistItem @model @auth(rules: [
  { allow: owner, ownerField: "userId", provider: userPools },
  { allow: private, provider: userPools, operations: [read] },
  { allow: public, provider: apiKey, operations: [read] }
]) {
  id: ID!
  userId: String @index(name: "byUserId")
  categoryId: ID! @index(name: "byCategory")
  text: String!
  completed: Boolean!
  dueDate: AWSDate
  priority: ChecklistPriority!
  order: Int
  isDefault: Boolean!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type ChecklistCategory @model @auth(rules: [
  { allow: owner, ownerField: "userId", provider: userPools },
  { allow: private, provider: userPools, operations: [read] },
  { allow: public, provider: apiKey, operations: [read] }
]) {
  id: ID!
  userId: String @index(name: "byUserId")
  name: String!
  icon: String!
  expanded: Boolean!
  order: Int
  isDefault: Boolean!
  items: [ChecklistItem] @hasMany(indexName: "byCategory", fields: ["id"])
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

enum ChecklistPriority {
  LOW
  MEDIUM
  HIGH
}

type Favorite @model @auth(rules: [
  { allow: owner, provider: userPools },
  { allow: private, provider: userPools, operations: [read] }
]) {
  id: ID!
  userId: String! @index(name: "byUser")
  entityId: String! @index(name: "byEntity")
  entityType: FavoriteEntityType!
  entityName: String!
  entityImage: String
  entityPrice: String
  entityLocation: String
  entityCity: String
  entityState: String
  entityRating: Float
  entityReviewCount: Int
  entityDescription: String
  dateAdded: AWSDateTime!
  notes: String
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type CartItem @model @auth(rules: [
  { allow: owner, provider: userPools },
  { allow: private, provider: userPools, operations: [read] }
]) {
  id: ID!
  userId: String! @index(name: "byUser")
  productId: String! @index(name: "byProduct")
  productName: String!
  productImage: String
  productPrice: Float!
  originalPrice: Float
  discount: Float
  quantity: Int!
  selectedVariant: String
  selectedSize: String
  selectedColor: String
  productBrand: String
  productCategory: String
  productDescription: String
  status: CartItemStatus!
  dateAdded: AWSDateTime!
  dateUpdated: AWSDateTime!
  notes: String
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type Order @model @auth(rules: [
  { allow: owner, provider: userPools },
  { allow: private, provider: userPools, operations: [read, create, update] },
  { allow: public, provider: apiKey, operations: [read] }
]) {
  id: ID!
  userId: String! @index(name: "byUser")
  orderNumber: String! @index(name: "byOrderNumber")
  status: OrderStatus!
  paymentStatus: OrderPaymentStatus!
  paymentMethod: PaymentMethod!

  # Customer Information
  customerName: String!
  customerEmail: String!
  customerPhone: String!

  # Shipping Address
  shippingAddress: ShippingAddress!

  # Billing Address (optional, defaults to shipping)
  billingAddress: ShippingAddress

  # Order Items
  items: [OrderItem!]!

  # Pricing
  subtotal: Float!
  shippingCost: Float!
  tax: Float!
  discount: Float!
  total: Float!

  # Payment Information
  razorpayOrderId: String
  razorpayPaymentId: String
  razorpaySignature: String
  transactionId: String

  # Order Tracking
  estimatedDeliveryDate: AWSDate
  actualDeliveryDate: AWSDate
  trackingNumber: String
  courierPartner: String

  # Additional Information
  specialInstructions: String
  giftMessage: String
  isGift: Boolean!

  # Timestamps
  orderDate: AWSDateTime!
  shippedDate: AWSDateTime
  deliveredDate: AWSDateTime
  cancelledDate: AWSDateTime

  # Metadata
  metadata: AWSJSON
  notes: String

  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

type OrderItem {
  productId: String!
  productName: String!
  productImage: String
  productPrice: Float!
  originalPrice: Float
  discount: Float
  quantity: Int!
  selectedVariant: String
  selectedSize: String
  selectedColor: String
  productBrand: String
  productCategory: String
  subtotal: Float!
}

type ShippingAddress {
  fullName: String!
  addressLine1: String!
  addressLine2: String
  city: String!
  state: String!
  pincode: String!
  country: String!
  phone: String
  landmark: String
  addressType: AddressType
}

type Payment @model @auth(rules: [
  { allow: owner, provider: userPools },
  { allow: private, provider: userPools, operations: [read, create, update] }
]) {
  id: ID!
  orderId: String! @index(name: "byOrder")
  userId: String! @index(name: "byUser")

  # Payment Details
  amount: Float!
  currency: String!
  paymentMethod: PaymentMethod!
  status: PaymentTransactionStatus!

  # Razorpay Integration
  razorpayOrderId: String
  razorpayPaymentId: String
  razorpaySignature: String

  # COD Details
  codAmount: Float
  codCollected: Boolean
  codCollectedDate: AWSDateTime

  # Transaction Information
  transactionId: String
  gatewayResponse: AWSJSON
  failureReason: String

  # Refund Information
  refundAmount: Float
  refundStatus: RefundStatus
  refundDate: AWSDateTime
  refundTransactionId: String

  # Timestamps
  initiatedAt: AWSDateTime!
  completedAt: AWSDateTime

  # Metadata
  metadata: AWSJSON
  notes: String

  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

# Order and Payment Enums
enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  OUT_FOR_DELIVERY
  DELIVERED
  CANCELLED
  RETURNED
  REFUNDED
}

enum OrderPaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
  PARTIALLY_REFUNDED
  COD_PENDING
  COD_COLLECTED
}

enum PaymentMethod {
  RAZORPAY
  COD
  UPI
  CREDIT_CARD
  DEBIT_CARD
  NET_BANKING
  WALLET
}

enum PaymentTransactionStatus {
  INITIATED
  PENDING
  SUCCESS
  FAILED
  CANCELLED
  REFUNDED
  PARTIALLY_REFUNDED
}

enum AddressType {
  HOME
  OFFICE
  OTHER
}
