import { useState, useCallback, useEffect } from 'react';
import { ContactService } from '../services/contactService';

/**
 * Custom hook for contact form submission
 * @returns {Object} Contact form state and functions
 */
export function useContactSubmission() {
  const [submitting, setSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const submitContact = useCallback(async (contactData) => {
    setSubmitting(true);
    setSubmitError(null);
    setSubmitSuccess(false);

    try {
      // Client-side validation
      const requiredFields = [
        { field: 'name', label: 'Name' },
        { field: 'email', label: 'Email' },
        { field: 'subject', label: 'Subject' },
        { field: 'message', label: 'Message' }
      ];

      const missingFields = requiredFields.filter(({ field }) => 
        !contactData[field] || contactData[field].toString().trim() === ''
      );

      if (missingFields.length > 0) {
        throw new Error(`Please fill in the following required fields: ${missingFields.map(f => f.label).join(', ')}`);
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(contactData.email)) {
        throw new Error('Please enter a valid email address');
      }

      const result = await ContactService.submitContact(contactData);

      if (result.success) {
        setSubmitSuccess(true);
        // Clear success message after 5 seconds
        setTimeout(() => setSubmitSuccess(false), 5000);
      } else {
        setSubmitError(result.error);
      }

      return result;
    } catch (err) {
      console.error('Contact submission error:', err);
      const errorMessage = err.message || 'Failed to submit contact form';
      setSubmitError(errorMessage);
      return {
        success: false,
        error: errorMessage
      };
    } finally {
      setSubmitting(false);
    }
  }, []);

  const clearMessages = useCallback(() => {
    setSubmitError(null);
    setSubmitSuccess(false);
  }, []);

  return {
    submitting,
    submitError,
    submitSuccess,
    submitContact,
    clearMessages
  };
}

/**
 * Custom hook for contact management (admin)
 * @returns {Object} Contact management state and functions
 */
export function useContactManagement() {
  const [contacts, setContacts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    totalContacts: 0,
    statusDistribution: { NEW: 0, IN_PROGRESS: 0, RESOLVED: 0, CLOSED: 0 },
    inquiryTypeDistribution: { GENERAL: 0, VENDOR: 0, SUPPORT: 0, FEEDBACK: 0, BUSINESS: 0 },
    priorityDistribution: { LOW: 0, MEDIUM: 0, HIGH: 0, URGENT: 0 }
  });
  const [hasMore, setHasMore] = useState(false);
  const [nextToken, setNextToken] = useState(null);

  const loadContacts = useCallback(async (options = {}) => {
    setLoading(true);
    setError(null);

    try {
      const result = await ContactService.listContacts({
        limit: 20,
        ...options
      });

      if (result.success) {
        if (options.nextToken) {
          // Load more - append to existing contacts
          setContacts(prev => [...prev, ...result.data]);
        } else {
          // Fresh load - replace contacts
          setContacts(result.data);
        }
        setNextToken(result.nextToken);
        setHasMore(!!result.nextToken);
      } else {
        setError(result.error);
      }
    } catch (err) {
      console.error('Error loading contacts:', err);
      setError(err.message || 'Failed to load contacts');
    } finally {
      setLoading(false);
    }
  }, []);

  const loadMore = useCallback(() => {
    if (hasMore && !loading && nextToken) {
      loadContacts({ nextToken });
    }
  }, [hasMore, loading, nextToken, loadContacts]);

  const loadStats = useCallback(async () => {
    try {
      const result = await ContactService.getContactStats();
      if (result.success) {
        setStats(result.data);
      }
    } catch (err) {
      console.error('Error loading contact stats:', err);
    }
  }, []);

  const filterByStatus = useCallback(async (status) => {
    setLoading(true);
    setError(null);

    try {
      const result = await ContactService.getContactsByStatus(status);
      if (result.success) {
        setContacts(result.data);
        setNextToken(result.nextToken);
        setHasMore(!!result.nextToken);
      } else {
        setError(result.error);
      }
    } catch (err) {
      console.error('Error filtering contacts by status:', err);
      setError(err.message || 'Failed to filter contacts');
    } finally {
      setLoading(false);
    }
  }, []);

  const filterByInquiryType = useCallback(async (inquiryType) => {
    setLoading(true);
    setError(null);

    try {
      const result = await ContactService.getContactsByInquiryType(inquiryType);
      if (result.success) {
        setContacts(result.data);
        setNextToken(result.nextToken);
        setHasMore(!!result.nextToken);
      } else {
        setError(result.error);
      }
    } catch (err) {
      console.error('Error filtering contacts by inquiry type:', err);
      setError(err.message || 'Failed to filter contacts');
    } finally {
      setLoading(false);
    }
  }, []);

  const updateContact = useCallback(async (id, updateData) => {
    try {
      const result = await ContactService.updateContact(id, updateData);
      if (result.success) {
        // Update the contact in the local state
        setContacts(prev => prev.map(contact => 
          contact.id === id ? { ...contact, ...updateData } : contact
        ));
        // Reload stats
        loadStats();
      }
      return result;
    } catch (err) {
      console.error('Error updating contact:', err);
      return {
        success: false,
        error: err.message || 'Failed to update contact'
      };
    }
  }, [loadStats]);

  const deleteContact = useCallback(async (id) => {
    try {
      const result = await ContactService.deleteContact(id);
      if (result.success) {
        // Remove the contact from local state
        setContacts(prev => prev.filter(contact => contact.id !== id));
        // Reload stats
        loadStats();
      }
      return result;
    } catch (err) {
      console.error('Error deleting contact:', err);
      return {
        success: false,
        error: err.message || 'Failed to delete contact'
      };
    }
  }, [loadStats]);

  const refresh = useCallback(() => {
    loadContacts();
    loadStats();
  }, [loadContacts, loadStats]);

  // Load initial data
  useEffect(() => {
    loadContacts();
    loadStats();
  }, [loadContacts, loadStats]);

  return {
    contacts,
    loading,
    error,
    stats,
    hasMore,
    loadContacts,
    loadMore,
    filterByStatus,
    filterByInquiryType,
    updateContact,
    deleteContact,
    refresh
  };
}

/**
 * Custom hook for contact subscriptions (admin)
 * @param {Function} onContactCreated - Callback for new contacts
 * @param {Function} onContactUpdated - Callback for updated contacts
 * @param {Function} onContactDeleted - Callback for deleted contacts
 * @returns {Object} Subscription management
 */
export function useContactSubscriptions(onContactCreated, onContactUpdated, onContactDeleted) {
  const [subscriptions, setSubscriptions] = useState([]);

  useEffect(() => {
    const subs = [];

    if (onContactCreated) {
      const createSub = ContactService.subscribeToContactCreation(onContactCreated);
      subs.push(createSub);
    }

    if (onContactUpdated) {
      const updateSub = ContactService.subscribeToContactUpdates(onContactUpdated);
      subs.push(updateSub);
    }

    if (onContactDeleted) {
      const deleteSub = ContactService.subscribeToContactDeletion(onContactDeleted);
      subs.push(deleteSub);
    }

    setSubscriptions(subs);

    // Cleanup subscriptions on unmount
    return () => {
      subs.forEach(sub => {
        if (sub && typeof sub.unsubscribe === 'function') {
          sub.unsubscribe();
        }
      });
    };
  }, [onContactCreated, onContactUpdated, onContactDeleted]);

  const unsubscribeAll = useCallback(() => {
    subscriptions.forEach(sub => {
      if (sub && typeof sub.unsubscribe === 'function') {
        sub.unsubscribe();
      }
    });
    setSubscriptions([]);
  }, [subscriptions]);

  return {
    subscriptions,
    unsubscribeAll
  };
}

export default {
  useContactSubmission,
  useContactManagement,
  useContactSubscriptions
};
