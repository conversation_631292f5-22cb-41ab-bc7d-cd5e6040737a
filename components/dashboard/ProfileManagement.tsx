"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Camera,
  Save,
  Edit,
  Shield,
  Bell,
  Globe,
  Building,
  Loader2,
  CheckCircle,
  AlertCircle,
  Eye,
  EyeOff
} from 'lucide-react'
import Image from 'next/image'
import { useAuth } from '@/contexts/AuthContext'
import { profileService, UserProfileResponse, UserProfileInput } from '@/lib/services/profileService'
import { showToast } from '@/lib/toast'

export function ProfileManagement() {
  const { user } = useAuth()
  const [profile, setProfile] = useState<UserProfileResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [editing, setEditing] = useState(false)
  const [activeTab, setActiveTab] = useState('personal')
  // Helper for showing image size error
  const [imageError, setImageError] = useState<string | null>(null);

  const [formData, setFormData] = useState<UserProfileInput>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: undefined,
    address: '',
    city: '',
    state: '',
    pincode: '',
    country: 'India',
    profilePhoto: '',
    bio: '',
    website: '',
    socialMedia: {
      facebook: '',
      instagram: '',
      twitter: '',
      linkedin: '',
      youtube: ''
    },
    preferences: {
      language: 'en',
      currency: 'INR',
      timezone: 'Asia/Kolkata',
      notifications: {
        email: true,
        sms: true,
        push: true,
        marketing: false
      },
      privacy: {
        profileVisibility: 'PUBLIC',
        contactVisibility: 'PUBLIC',
        showOnlineStatus: true
      }
    },
    businessInfo: {
      businessName: '',
      businessType: '',
      businessAddress: '',
      businessPhone: '',
      businessEmail: '',
      businessWebsite: '',
      gstNumber: '',
      panNumber: '',
      businessLicense: ''
    },
    isVendor: false
  })

  // Load profile data
  useEffect(() => {
    loadProfile()
  }, [])

  const loadProfile = async () => {
    try {
      setLoading(true)
      const profileData = await profileService.getCurrentUserProfile()

      if (profileData) {
        setProfile(profileData)
        setFormData({
          firstName: profileData.firstName || '',
          lastName: profileData.lastName || '',
          email: profileData.email || '',
          phone: profileData.phone || '',
          dateOfBirth: profileData.dateOfBirth || '',
          gender: profileData.gender,
          address: profileData.address || '',
          city: profileData.city || '',
          state: profileData.state || '',
          pincode: profileData.pincode || '',
          country: profileData.country || 'India',
          profilePhoto: profileData.profilePhoto || '',
          bio: profileData.bio || '',
          website: profileData.website || '',
          socialMedia: profileData.socialMedia || {
            facebook: '',
            instagram: '',
            twitter: '',
            linkedin: '',
            youtube: ''
          },
          preferences: profileData.preferences || {
            language: 'en',
            currency: 'INR',
            timezone: 'Asia/Kolkata',
            notifications: {
              email: true,
              sms: true,
              push: true,
              marketing: false
            },
            privacy: {
              profileVisibility: 'PUBLIC',
              contactVisibility: 'PUBLIC',
              showOnlineStatus: true
            }
          },
          businessInfo: profileData.businessInfo || {
            businessName: '',
            businessType: '',
            businessAddress: '',
            businessPhone: '',
            businessEmail: '',
            businessWebsite: '',
            gstNumber: '',
            panNumber: '',
            businessLicense: ''
          },
          isVendor: profileData.isVendor || false
        })
      } else {
        // No profile exists, enable editing mode
        setEditing(true)
      }
    } catch (error) {
      console.error('Error loading profile:', error)
      showToast.error('Failed to load profile')
      setEditing(true) // Enable editing if profile doesn't exist
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleNestedInputChange = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...prev[parent as keyof typeof prev] as any,
        [field]: value
      }
    }))
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      setImageError(null)

      // Validate required fields
      if (!formData.firstName.trim() || !formData.lastName.trim() || !formData.email.trim()) {
        showToast.error('Please fill in all required fields')
        return
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(formData.email)) {
        showToast.error('Please enter a valid email address')
        return
      }

      // Validate date of birth format if provided
      if (formData.dateOfBirth && !/^\d{4}-\d{2}-\d{2}$/.test(formData.dateOfBirth)) {
        showToast.error('Please enter a valid date of birth')
        return
      }

      // Prepare clean form data (remove empty strings)
      const cleanFormData = Object.fromEntries(
        Object.entries(formData).map(([key, value]) => [
          key,
          typeof value === 'string' && value.trim() === '' ? undefined : value
        ])
      )

      if (profile) {
        // Update existing profile
        const updatedProfile = await profileService.updateProfile({
          id: profile.id,
          ...cleanFormData
        })
        setProfile(updatedProfile)
        showToast.success('Profile updated successfully')
      } else {
        // Create new profile
        const newProfile = await profileService.createProfile(cleanFormData)
        setProfile(newProfile)
        showToast.success('Profile created successfully')
      }

      setEditing(false)
    } catch (error) {
      console.error('Error saving profile:', error)
      showToast.error('Failed to save profile')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    if (profile) {
      // Reset form data to original profile data
      loadProfile()
    }
    setEditing(false)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="w-8 h-8 animate-spin text-primary" />
        <span className="ml-2">Loading profile...</span>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto px-4 py-10">
      {/* Profile Header */}
      <Card className="mb-8 shadow-lg rounded-2xl">
        <CardContent className="flex flex-col items-center py-8">
          <div className="relative mb-4">
                <Image
              src={profile?.profilePhoto || '/placeholder.svg'}
                  alt="Profile"
              width={100}
              height={100}
              className="rounded-full object-cover border-4 border-primary shadow"
            />
            {profile?.isVerified && (
              <CheckCircle className="absolute -bottom-2 -right-2 w-7 h-7 text-green-600 bg-white rounded-full border-2 border-white" />
            )}
            {editing && (
              <div className="mt-3 flex flex-col items-center">
                <label htmlFor="profilePhotoUpload" className="cursor-pointer px-4 py-2 bg-primary text-white rounded-full text-sm font-medium shadow hover:bg-primary/90 transition mb-1">Upload Photo</label>
                <input
                  id="profilePhotoUpload"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={e => {
                    setImageError(null);
                    const file = e.target.files?.[0];
                    if (file) {
                      if (file.size > 1024 * 1024) { // 1MB limit
                        setImageError('Image is too large. Please select a file under 1MB.');
                        return;
                      }
                      const reader = new FileReader();
                      reader.onload = async (ev) => {
                        const photoUrl = ev.target?.result;
                        if (photoUrl && profile) {
                          try {
                            const updated = await profileService.updateProfilePhoto(profile.id, photoUrl);
                            setProfile(updated);
                            handleInputChange('profilePhoto', photoUrl);
                            showToast.success('Profile photo updated successfully');
                          } catch (err) {
                            showToast.error('Failed to update profile photo');
                          }
                        } else {
                          handleInputChange('profilePhoto', photoUrl);
                        }
                      };
                      reader.readAsDataURL(file);
                    }
                  }}
                />
                <span className="text-xs text-gray-400">JPG, PNG, or GIF (max 1MB)</span>
                {imageError && <span className="text-xs text-red-500 mt-1">{imageError}</span>}
              </div>
            )}
          </div>
          <h2 className="text-2xl font-bold text-primary mb-1">{profile?.firstName} {profile?.lastName}</h2>
          <p className="text-gray-600 mb-2">{profile?.email}</p>
          <div className="flex gap-2 mb-4">
            {profile?.isVendor && (
                    <Badge variant="secondary">Vendor</Badge>
                  )}
            {profile?.isVerified && (
                    <Badge className="bg-green-100 text-green-800">Verified</Badge>
                  )}
                </div>
          <div>
            {editing ? (
              <div className="flex gap-2">
                <Button variant="outline" onClick={handleCancel} disabled={saving}>Cancel</Button>
                <Button onClick={handleSave} disabled={saving}>
                  {saving ? (<><Loader2 className="w-4 h-4 mr-2 animate-spin" />Saving...</>) : (<><Save className="w-4 h-4 mr-2" />Save Changes</>)}
                </Button>
              </div>
            ) : (
              <Button onClick={() => setEditing(true)} className="px-6 py-2 rounded-full bg-primary text-white font-semibold shadow hover:bg-primary/90 transition-all duration-200">Edit Profile</Button>
            )}
            </div>
          </CardContent>
        </Card>

      {/* Tab Navigation */}
      <div className="mb-6 border-b">
        <nav className="flex gap-8 justify-center">
          {[
            { id: 'personal', label: 'Personal Info', icon: User },
            { id: 'contact', label: 'Contact', icon: MapPin },
            { id: 'preferences', label: 'Preferences', icon: Bell },
          ].map((tab) => {
            const Icon = tab.icon
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-base transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-gray-500 hover:text-primary'
                }`}
              >
                <Icon className="w-5 h-5" />
                {tab.label}
              </button>
            )
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <Card className="shadow-lg rounded-2xl">
        <CardContent className="py-8 px-6 md:px-10">
        {/* Personal Information Tab */}
        {activeTab === 'personal' && (
            <form className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="firstName">First Name *</Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    disabled={!editing}
                    placeholder="Enter your first name"
                  />
                </div>
                <div>
                  <Label htmlFor="lastName">Last Name *</Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    disabled={!editing}
                    placeholder="Enter your last name"
                  />
                </div>
              <div className="md:col-span-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  value={formData.bio}
                  onChange={(e) => handleInputChange('bio', e.target.value)}
                  disabled={!editing}
                  placeholder="Tell us about yourself..."
                  rows={3}
                />
              </div>
                <div>
                  <Label htmlFor="dateOfBirth">Date of Birth</Label>
                  <Input
                    id="dateOfBirth"
                    type="date"
                    value={formData.dateOfBirth}
                    onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                    disabled={!editing}
                  />
                </div>
                <div>
                  <Label htmlFor="gender">Gender</Label>
                  <Select
                    value={formData.gender}
                    onValueChange={(value) => handleInputChange('gender', value)}
                    disabled={!editing}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MALE">Male</SelectItem>
                      <SelectItem value="FEMALE">Female</SelectItem>
                      <SelectItem value="OTHER">Other</SelectItem>
                      <SelectItem value="PREFER_NOT_TO_SAY">Prefer not to say</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
            </form>
          )}

          {/* Contact Tab */}
        {activeTab === 'contact' && (
            <form className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  disabled={!editing}
                  placeholder="+91 8148376909"
                />
              </div>
              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  disabled={!editing}
                  placeholder="Enter your email address"
                />
              </div>
              <div className="md:col-span-2">
                <Label htmlFor="address">Address</Label>
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  disabled={!editing}
                  placeholder="Enter your full address"
                  rows={2}
                />
              </div>
                <div>
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    disabled={!editing}
                    placeholder="Enter your city"
                  />
                </div>
                <div>
                  <Label htmlFor="state">State</Label>
                  <Input
                    id="state"
                    value={formData.state}
                    onChange={(e) => handleInputChange('state', e.target.value)}
                    disabled={!editing}
                    placeholder="Enter your state"
                  />
                </div>
                <div>
                  <Label htmlFor="pincode">Pincode</Label>
                  <Input
                    id="pincode"
                    value={formData.pincode}
                    onChange={(e) => handleInputChange('pincode', e.target.value)}
                    disabled={!editing}
                    placeholder="Enter your pincode"
                  />
                </div>
                <div>
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    value={formData.country}
                    onChange={(e) => handleInputChange('country', e.target.value)}
                    disabled={!editing}
                    placeholder="Enter your country"
                  />
                </div>
            </form>
          )}

          {/* Preferences Tab */}
          {activeTab === 'preferences' && (
            <form className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="language">Language</Label>
                <Input
                  id="language"
                  value={formData.preferences.language}
                  onChange={(e) => handleNestedInputChange('preferences', 'language', e.target.value)}
                  disabled={!editing}
                  placeholder="en"
                />
              </div>
              <div>
                <Label htmlFor="currency">Currency</Label>
                <Input
                  id="currency"
                  value={formData.preferences.currency}
                  onChange={(e) => handleNestedInputChange('preferences', 'currency', e.target.value)}
                  disabled={!editing}
                  placeholder="INR"
                />
              </div>
              <div>
                <Label htmlFor="timezone">Timezone</Label>
                <Input
                  id="timezone"
                  value={formData.preferences.timezone}
                  onChange={(e) => handleNestedInputChange('preferences', 'timezone', e.target.value)}
                  disabled={!editing}
                  placeholder="Asia/Kolkata"
                />
              </div>
              <div className="md:col-span-2">
                <Label>Notifications</Label>
                <div className="flex flex-wrap gap-4 mt-2">
                  <div className="flex items-center gap-2">
                    <Switch
                      id="notif-email"
                      checked={formData.preferences.notifications.email}
                      onCheckedChange={(checked) => handleNestedInputChange('preferences', 'notifications', { ...formData.preferences.notifications, email: checked })}
                      disabled={!editing}
                    />
                    <Label htmlFor="notif-email">Email</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      id="notif-sms"
                      checked={formData.preferences.notifications.sms}
                      onCheckedChange={(checked) => handleNestedInputChange('preferences', 'notifications', { ...formData.preferences.notifications, sms: checked })}
                      disabled={!editing}
                    />
                    <Label htmlFor="notif-sms">SMS</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      id="notif-push"
                      checked={formData.preferences.notifications.push}
                      onCheckedChange={(checked) => handleNestedInputChange('preferences', 'notifications', { ...formData.preferences.notifications, push: checked })}
                      disabled={!editing}
                    />
                    <Label htmlFor="notif-push">Push</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      id="notif-marketing"
                      checked={formData.preferences.notifications.marketing}
                      onCheckedChange={(checked) => handleNestedInputChange('preferences', 'notifications', { ...formData.preferences.notifications, marketing: checked })}
                      disabled={!editing}
                    />
                    <Label htmlFor="notif-marketing">Marketing</Label>
                  </div>
                </div>
              </div>
            </form>
          )}
            </CardContent>
          </Card>
    </div>
  )
}

export default ProfileManagement