"use client"

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { ShoppingCart, Loader2, Plus, Minus, Check } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { CartService, CartItemInput } from '@/lib/services/cartService'
import { showToast } from '@/lib/toast'
import { useRouter } from 'next/navigation'

interface AddToCartButtonProps {
  productId: string
  productData: {
    name: string
    image?: string
    price: number
    originalPrice?: number
    discount?: number
    brand?: string
    category?: string
    description?: string
  }
  variant?: 'default' | 'outline' | 'secondary'
  size?: 'default' | 'sm' | 'lg'
  className?: string
  showText?: boolean
  showQuantityControls?: boolean
  initialQuantity?: number
}

export function AddToCartButton({
  productId,
  productData,
  variant = 'default',
  size = 'default',
  className = '',
  showText = true,
  showQuantityControls = false,
  initialQuantity = 1
}: AddToCartButtonProps) {
  const { isAuthenticated, user } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [quantity, setQuantity] = useState(initialQuantity)
  const [isInCart, setIsInCart] = useState(false)
  const [cartItemId, setCartItemId] = useState<string | null>(null)
  const [justAdded, setJustAdded] = useState(false)

  // Check if item is in cart on component mount
  useEffect(() => {
    if (isAuthenticated && user) {
      checkCartStatus()
    }
  }, [isAuthenticated, user, productId])

  const checkCartStatus = async () => {
    try {
      const cartItem = await CartService.getCartItem(productId)
      if (cartItem) {
        setIsInCart(true)
        setCartItemId(cartItem.id)
        setQuantity(cartItem.quantity)
      } else {
        setIsInCart(false)
        setCartItemId(null)
        setQuantity(initialQuantity)
      }
    } catch (error) {
      console.error('Error checking cart status:', error)
    }
  }

  const handleAddToCart = async () => {
    if (!isAuthenticated) {
      showToast.info('Please login to add items to cart')
      router.push(`/login?redirect=${encodeURIComponent(window.location.pathname)}`)
      return
    }

    setLoading(true)

    try {
      const cartInput: CartItemInput = {
        productId,
        productName: productData.name,
        productImage: productData.image,
        productPrice: productData.price,
        originalPrice: productData.originalPrice,
        discount: productData.discount,
        quantity: quantity,
        productBrand: productData.brand,
        productCategory: productData.category,
        productDescription: productData.description
      }

      const result = await CartService.addToCart(cartInput)
      if (result.success && result.cartItem) {
        setIsInCart(true)
        setCartItemId(result.cartItem.id)
        setJustAdded(true)
        showToast.success('Added to cart successfully')

        // Dispatch cart update event to notify header
        window.dispatchEvent(new CustomEvent('cartUpdated'))

        // Reset "just added" state after 2 seconds
        setTimeout(() => setJustAdded(false), 2000)
      }
    } catch (error) {
      console.error('Add to cart error:', error)
      showToast.error(error.message || 'Failed to add to cart')
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateQuantity = async (newQuantity: number) => {
    if (!cartItemId || newQuantity < 1) return

    setLoading(true)
    try {
      const result = await CartService.updateQuantity(cartItemId, newQuantity)
      if (result.success) {
        setQuantity(newQuantity)
        showToast.success('Quantity updated')

        // Dispatch cart update event to notify header
        window.dispatchEvent(new CustomEvent('cartUpdated'))
      }
    } catch (error) {
      console.error('Update quantity error:', error)
      showToast.error('Failed to update quantity')
    } finally {
      setLoading(false)
    }
  }

  const handleRemoveFromCart = async () => {
    if (!cartItemId) return

    setLoading(true)
    try {
      const result = await CartService.removeFromCart(cartItemId)
      if (result.success) {
        setIsInCart(false)
        setCartItemId(null)
        setQuantity(initialQuantity)
        showToast.success('Removed from cart')
      }
    } catch (error) {
      console.error('Remove from cart error:', error)
      showToast.error('Failed to remove from cart')
    } finally {
      setLoading(false)
    }
  }

  // If item is in cart and we want to show quantity controls
  if (isInCart && showQuantityControls) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="flex items-center border rounded-md">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleUpdateQuantity(quantity - 1)}
            disabled={loading || quantity <= 1}
            className="h-8 w-8 p-0"
          >
            <Minus className="h-3 w-3" />
          </Button>
          <span className="px-3 py-1 text-sm font-medium min-w-[40px] text-center">
            {quantity}
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleUpdateQuantity(quantity + 1)}
            disabled={loading}
            className="h-8 w-8 p-0"
          >
            <Plus className="h-3 w-3" />
          </Button>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRemoveFromCart}
          disabled={loading}
          className="text-red-600 hover:text-red-700 hover:bg-red-50"
        >
          Remove
        </Button>
      </div>
    )
  }

  // Regular add to cart button
  const buttonText = justAdded 
    ? 'Added!' 
    : isInCart 
      ? 'In Cart' 
      : 'Add to Cart'

  const buttonIcon = loading 
    ? <Loader2 className="h-4 w-4 animate-spin" />
    : justAdded 
      ? <Check className="h-4 w-4" />
      : <ShoppingCart className="h-4 w-4" />

  return (
    <Button
      variant={justAdded ? 'secondary' : variant}
      size={size}
      className={`${className} ${justAdded ? 'bg-green-100 text-green-700 border-green-300' : ''}`}
      onClick={isInCart ? () => router.push('/cart') : handleAddToCart}
      disabled={loading}
    >
      {buttonIcon}
      {showText && (
        <span className="ml-2">
          {buttonText}
        </span>
      )}
    </Button>
  )
}

export default AddToCartButton
