"use client"

import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  MessageCircle,
  Phone,
  Mail,
  Globe,
  LogIn,
  UserPlus,
  Lock,
  Shield,
  Heart,
  Star,
  CheckCircle,
  Users,
  MapPin,
  Calendar,
  Share2,
  Bookmark
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter, usePathname } from 'next/navigation'
import VenueInquiryForm from './VenueInquiryForm'

interface AuthenticatedContactVenueProps {
  venue: {
    id: string
    userId: string
    name: string
    type?: string
    capacity?: number
    contactPhone?: string
    contactEmail?: string
    website?: string
    rating?: number
    reviewCount?: number
    priceRange?: string
    city?: string
    state?: string
  }
  showInquiryForm?: boolean
}

export function AuthenticatedContactVenue({ 
  venue, 
  showInquiryForm = true 
}: AuthenticatedContactVenueProps) {
  const { isAuthenticated, isLoading, user } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [showLoginPrompt, setShowLoginPrompt] = useState(false)

  // Handle contact actions for non-authenticated users
  const handleContactAction = (action: 'call' | 'email' | 'website' | 'book') => {
    if (!isAuthenticated) {
      setShowLoginPrompt(true)
      return
    }

    // Handle authenticated actions
    switch (action) {
      case 'book':
        // Navigate to booking page or open booking modal
        router.push(`/booking/venue/${venue.id}`)
        break
      case 'call':
        if (venue.contactPhone) {
          window.open(`tel:${venue.contactPhone}`, '_self')
        }
        break
      case 'email':
        if (venue.contactEmail) {
          window.open(`mailto:${venue.contactEmail}`, '_self')
        }
        break
      case 'website':
        if (venue.website) {
          window.open(venue.website, '_blank')
        }
        break
    }
  }

  // Login prompt component
  const LoginPrompt = () => (
    <Card className="border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-accent/5">
      <CardContent className="p-6">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Lock className="w-8 h-8 text-primary" />
          </div>
          
          <h3 className="text-xl font-semibold mb-2 text-gray-800">
            Login Required to Contact Venue
          </h3>
          
          <p className="text-gray-600 mb-6 leading-relaxed">
            To protect our venues from spam and ensure quality inquiries, 
            you need to be logged in to contact {venue.name}.
          </p>

          {/* Venue highlights */}
          <div className="bg-white rounded-lg p-4 mb-6 text-left">
            <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
              <MapPin className="w-4 h-4 mr-2 text-primary" />
              About This Venue:
            </h4>
            <div className="grid grid-cols-2 gap-3 text-sm text-gray-600">
              {venue.type && (
                <div className="flex items-center">
                  <CheckCircle className="w-3 h-3 mr-2 text-green-500" />
                  {venue.type}
                </div>
              )}
              {venue.capacity && (
                <div className="flex items-center">
                  <Users className="w-3 h-3 mr-2 text-blue-500" />
                  {venue.capacity} guests
                </div>
              )}
              {venue.city && venue.state && (
                <div className="flex items-center col-span-2">
                  <MapPin className="w-3 h-3 mr-2 text-red-500" />
                  {venue.city}, {venue.state}
                </div>
              )}
            </div>
          </div>

          {/* Benefits of logging in */}
          <div className="bg-white rounded-lg p-4 mb-6 text-left">
            <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
              <Shield className="w-4 h-4 mr-2 text-green-600" />
              Benefits of Creating an Account:
            </h4>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                Direct contact with verified venues
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                Save favorite venues and vendors
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                Track your inquiries and responses
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                Get personalized venue recommendations
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                Access exclusive venue packages
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                Schedule venue visits and tours
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                Save venues to favorites list
              </li>
            </ul>
          </div>

          {/* Action buttons */}
          <div className="flex justify-center">
            <Button
              onClick={() => router.push(`/login?redirect=${encodeURIComponent(pathname)}`)}
              className="w-full bg-primary hover:bg-primary/90 text-white font-semibold py-3"
            >
              <LogIn className="w-4 h-4 mr-2" />
              Login to Contact
            </Button>
          </div>

          {/* Quick stats to build trust */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-500 mb-2">Join thousands of happy couples</p>
            <div className="flex justify-center items-center gap-4 text-sm">
              <div className="flex items-center text-gray-600">
                <Heart className="w-4 h-4 mr-1 text-red-500" />
                <span>50,000+ Weddings</span>
              </div>
              <div className="flex items-center text-gray-600">
                <Star className="w-4 h-4 mr-1 text-yellow-500" />
                <span>4.8/5 Rating</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded mb-4"></div>
            <div className="space-y-3">
              <div className="h-12 bg-gray-200 rounded"></div>
              <div className="h-12 bg-gray-200 rounded"></div>
              <div className="h-12 bg-gray-200 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Show login prompt if not authenticated
  if (!isAuthenticated) {
    return <LoginPrompt />
  }

  // Authenticated user - show full contact options
  return (
    <div className="space-y-6">
      {/* Contact Venue Card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold">Contact Venue</h3>
            <Badge className="bg-green-100 text-green-800">
              <CheckCircle className="w-3 h-3 mr-1" />
              Verified
            </Badge>
          </div>

          <div className="space-y-4">
            <Button
              onClick={() => handleContactAction('book')}
              className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-bold py-3 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Calendar className="h-5 w-5 mr-2" />
              Book Now
            </Button>

            <Button
              onClick={() => handleContactAction('call')}
              className="w-full bg-primary hover:bg-primary/90 text-white font-semibold py-3"
              disabled={!venue.contactPhone}
            >
              <Phone className="h-5 w-5 mr-2" />
              {venue.contactPhone ? 'Call Now' : 'Phone Not Available'}
            </Button>

            <Button
              onClick={() => handleContactAction('email')}
              variant="outline"
              className="w-full border-2 border-primary text-primary hover:bg-primary hover:text-white font-semibold py-3"
              disabled={!venue.contactEmail}
            >
              <Mail className="h-5 w-5 mr-2" />
              {venue.contactEmail ? 'Send Message' : 'Email Not Available'}
            </Button>

            {venue.website && (
              <Button
                onClick={() => handleContactAction('website')}
                variant="outline"
                className="w-full font-semibold py-3"
              >
                <Globe className="h-5 w-5 mr-2" />
                Visit Website
              </Button>
            )}
          </div>

          {/* Contact Info */}
          <div className="mt-6 pt-6 border-t space-y-3">
            {venue.contactPhone && (
              <div className="flex items-center text-gray-600">
                <Phone className="h-4 w-4 mr-3 text-primary" />
                <span>{venue.contactPhone}</span>
              </div>
            )}
            {venue.contactEmail && (
              <div className="flex items-center text-gray-600">
                <Mail className="h-4 w-4 mr-3 text-primary" />
                <span>{venue.contactEmail}</span>
              </div>
            )}
            {venue.website && (
              <div className="flex items-center text-gray-600">
                <Globe className="h-4 w-4 mr-3 text-primary" />
                <span className="truncate">{venue.website}</span>
              </div>
            )}
          </div>

          {/* Venue Stats */}
          <div className="mt-6 pt-4 border-t">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-primary">
                  {venue.rating || 'N/A'}
                </div>
                <div className="text-sm text-gray-600">Rating</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary">
                  {venue.reviewCount || 0}
                </div>
                <div className="text-sm text-gray-600">Reviews</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions Card */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-bold mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <Button
              variant="outline"
              className="w-full justify-start bg-transparent hover:bg-primary hover:text-white"
              onClick={() => {
                // Scroll to inquiry form
                const inquiryForm = document.getElementById('venue-inquiry-form')
                if (inquiryForm) {
                  inquiryForm.scrollIntoView({ behavior: 'smooth' })
                }
              }}
            >
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Visit
            </Button>

            <Button
              variant="outline"
              className="w-full justify-start bg-transparent hover:bg-primary hover:text-white"
              onClick={() => {
                // Add to favorites functionality
                console.log('Adding venue to favorites:', venue.name)
                // You can implement the actual favorites functionality here
              }}
            >
              <Bookmark className="h-4 w-4 mr-2" />
              Save to Favorites
            </Button>

            <Button
              variant="outline"
              className="w-full justify-start bg-transparent hover:bg-primary hover:text-white"
              onClick={() => {
                // Share venue functionality
                if (navigator.share) {
                  navigator.share({
                    title: venue.name,
                    text: `Check out this amazing venue: ${venue.name}`,
                    url: window.location.href
                  })
                } else {
                  // Fallback: copy to clipboard
                  navigator.clipboard.writeText(window.location.href)
                  console.log('Venue link copied to clipboard')
                }
              }}
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share Venue
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Venue Inquiry Form - Only for authenticated users */}
      {showInquiryForm && (
        <div id="venue-inquiry-form">
          <VenueInquiryForm
            venueUserId={venue.userId}
            venueId={venue.id}
            venueName={venue.name}
            venueType={venue.type}
            venueCapacity={venue.capacity}
          />
        </div>
      )}
    </div>
  )
}

export default AuthenticatedContactVenue
