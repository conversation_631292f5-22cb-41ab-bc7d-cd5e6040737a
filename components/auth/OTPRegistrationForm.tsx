"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Mail, 
  Phone, 
  User, 
  Lock, 
  Eye, 
  EyeOff, 
  MessageSquare, 
  RefreshCw, 
  CheckCircle, 
  Clock,
  AlertCircle
} from "lucide-react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/contexts/AuthContext"
import HybridMobileAuthService from "@/lib/services/hybridMobileAuth"
import MobileAuthService from "@/lib/services/mobileAuthService"
import AuthRoutingService from "@/lib/services/authRouting"

export default function OTPRegistrationForm() {
  const router = useRouter()
  const { userType, userProfile } = useAuth()
  
  const [activeTab, setActiveTab] = useState<'email' | 'mobile'>('email')
  const [step, setStep] = useState<'form' | 'otp'>('form')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [resendTimer, setResendTimer] = useState(0)
  const [otpSession, setOtpSession] = useState('')
  
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    otp: ''
  })
  
  const [countryCode, setCountryCode] = useState('+91')
  
  // Country codes
  const countryCodes = HybridMobileAuthService.getSupportedCountryCodes()
  
  // Start resend timer
  const startResendTimer = () => {
    setResendTimer(30)
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }
  
  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    setError('')
  }
  
  // Validate form
  const validateForm = () => {
    if (!formData.fullName.trim()) {
      setError('Please enter your full name')
      return false
    }
    
    if (activeTab === 'email') {
      if (!formData.email.trim() || !/\S+@\S+\.\S+/.test(formData.email)) {
        setError('Please enter a valid email address')
        return false
      }
    } else {
      if (!formData.phone.trim() || !HybridMobileAuthService.validatePhoneNumber(formData.phone)) {
        setError('Please enter a valid 10-digit mobile number')
        return false
      }
    }
    
    if (!formData.password || formData.password.length < 8) {
      setError('Password must be at least 8 characters long')
      return false
    }
    
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match')
      return false
    }
    
    return true
  }
  
  // Handle email registration
  const handleEmailRegistration = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateForm()) return
    
    setLoading(true)
    setError('')
    
    try {
      const result = await MobileAuthService.sendEmailOTP(formData.email)
      
      if (result.success) {
        setStep('otp')
        startResendTimer()
        setSuccess('OTP sent to your email address')
      } else {
        setError(result.message)
      }
    } catch (error: any) {
      setError('Failed to send OTP. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  // Handle mobile registration
  const handleMobileRegistration = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!validateForm()) return
    
    setLoading(true)
    setError('')
    
    try {
      const result = await HybridMobileAuthService.sendMobileOTP(formData.phone, countryCode)
      
      if (result.success && result.session) {
        setOtpSession(result.session)
        setStep('otp')
        startResendTimer()
        setSuccess('OTP sent to your mobile number')
      } else {
        setError(result.message)
      }
    } catch (error: any) {
      setError('Failed to send OTP. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  // Handle OTP verification
  const handleOTPVerification = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.otp || formData.otp.length !== 6) {
      setError('Please enter the 6-digit OTP')
      return
    }
    
    setLoading(true)
    setError('')
    
    try {
      let result
      
      if (activeTab === 'email') {
        result = await MobileAuthService.verifyEmailOTP(formData.email, formData.otp)
      } else {
        result = await HybridMobileAuthService.verifyMobileOTP(otpSession, formData.otp)
      }
      
      if (result.success) {
        setSuccess('Account created successfully! Redirecting...')
        
        // Redirect to dashboard
        setTimeout(() => {
          const dashboardRoute = AuthRoutingService.getDashboardRoute(userType, userProfile)
          router.push(dashboardRoute)
        }, 2000)
      } else {
        setError(result.message)
      }
    } catch (error: any) {
      setError('OTP verification failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  // Handle resend OTP
  const handleResendOTP = async () => {
    if (resendTimer > 0) return
    
    setLoading(true)
    setError('')
    
    try {
      let result
      
      if (activeTab === 'email') {
        result = await MobileAuthService.sendEmailOTP(formData.email)
      } else {
        result = await HybridMobileAuthService.resendOTP(otpSession)
      }
      
      if (result.success) {
        startResendTimer()
        setSuccess('OTP resent successfully')
      } else {
        setError(result.message)
      }
    } catch (error: any) {
      setError('Failed to resend OTP. Please try again.')
    } finally {
      setLoading(false)
    }
  }
  
  if (step === 'otp') {
    return (
      <Card className="w-full max-w-md mx-auto shadow-lg">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-gray-900">Verify Your Account</CardTitle>
          <CardDescription>
            We've sent a 6-digit OTP to your {activeTab === 'email' ? 'email address' : 'mobile number'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleOTPVerification} className="space-y-4">
            {error && (
              <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="w-4 h-4 text-red-500" />
                <span className="text-sm text-red-700">{error}</span>
              </div>
            )}
            
            {success && (
              <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-700">{success}</span>
              </div>
            )}
            
            <div className="text-center text-sm text-gray-600 mb-4">
              {activeTab === 'email' ? formData.email : `${countryCode} ${HybridMobileAuthService.formatPhoneNumber(formData.phone)}`}
            </div>
            
            <div className="relative">
              <Input
                name="otp"
                type="text"
                placeholder="Enter 6-digit OTP"
                className="pl-12 text-center text-lg tracking-widest"
                value={formData.otp}
                onChange={(e) => setFormData({...formData, otp: e.target.value.replace(/\D/g, '').slice(0, 6)})}
                maxLength={6}
                required
              />
              <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                <MessageSquare className="h-5 w-5" />
              </span>
            </div>
            
            <Button 
              type="submit" 
              className="w-full bg-primary hover:bg-primary/90" 
              disabled={loading || formData.otp.length !== 6}
            >
              {loading ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Verifying...
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Verify & Create Account
                </>
              )}
            </Button>
            
            <div className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                className="flex-1"
                onClick={() => {
                  setStep('form')
                  setFormData({...formData, otp: ''})
                  setError('')
                  setSuccess('')
                }}
              >
                Back
              </Button>
              <Button
                type="button"
                variant="outline"
                className="flex-1"
                onClick={handleResendOTP}
                disabled={resendTimer > 0 || loading}
              >
                {resendTimer > 0 ? (
                  <>
                    <Clock className="w-4 h-4 mr-1" />
                    {resendTimer}s
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4 mr-1" />
                    Resend
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    )
  }
  
  return (
    <Card className="w-full max-w-md mx-auto shadow-lg">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold text-gray-900">Create Your Account</CardTitle>
        <CardDescription>
          Join thousands of couples planning their dream wedding
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'email' | 'mobile')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="email" className="flex items-center gap-2">
              <Mail className="w-4 h-4" />
              Email
            </TabsTrigger>
            <TabsTrigger value="mobile" className="flex items-center gap-2">
              <Phone className="w-4 h-4" />
              Mobile
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="email">
            <form onSubmit={handleEmailRegistration} className="space-y-4">
              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <span className="text-sm text-red-700">{error}</span>
                </div>
              )}
              
              <div className="relative">
                <Input
                  name="fullName"
                  type="text"
                  placeholder="Full Name*"
                  className="pl-12"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  required
                />
                <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                  <User className="h-5 w-5" />
                </span>
              </div>
              
              <div className="relative">
                <Input
                  name="email"
                  type="email"
                  placeholder="Email Address*"
                  className="pl-12"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
                <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                  <Mail className="h-5 w-5" />
                </span>
              </div>
              
              <div className="relative">
                <Input
                  name="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Password (min 8 characters)*"
                  className="pl-12 pr-12"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                />
                <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                  <Lock className="h-5 w-5" />
                </span>
                <button
                  type="button"
                  className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
              
              <div className="relative">
                <Input
                  name="confirmPassword"
                  type="password"
                  placeholder="Confirm Password*"
                  className="pl-12"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  required
                />
                <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                  <Lock className="h-5 w-5" />
                </span>
              </div>
              
              <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                {loading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Sending OTP...
                  </>
                ) : (
                  <>
                    <Mail className="w-4 h-4 mr-2" />
                    Register with Email
                  </>
                )}
              </Button>
            </form>
          </TabsContent>
          
          <TabsContent value="mobile">
            <form onSubmit={handleMobileRegistration} className="space-y-4">
              {error && (
                <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
                  <AlertCircle className="w-4 h-4 text-red-500" />
                  <span className="text-sm text-red-700">{error}</span>
                </div>
              )}
              
              <div className="relative">
                <Input
                  name="fullName"
                  type="text"
                  placeholder="Full Name*"
                  className="pl-12"
                  value={formData.fullName}
                  onChange={handleInputChange}
                  required
                />
                <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                  <User className="h-5 w-5" />
                </span>
              </div>
              
              <div className="flex gap-2">
                <Select value={countryCode} onValueChange={setCountryCode}>
                  <SelectTrigger className="w-24">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {countryCodes.map((country) => (
                      <SelectItem key={country.code} value={country.code}>
                        {country.flag} {country.code}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <div className="relative flex-1">
                  <Input
                    name="phone"
                    type="tel"
                    placeholder="Mobile Number*"
                    className="pl-12"
                    value={formData.phone}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '')
                      setFormData({...formData, phone: value})
                      setError('')
                    }}
                    maxLength={10}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <Phone className="h-5 w-5" />
                  </span>
                </div>
              </div>
              
              <div className="relative">
                <Input
                  name="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Password (min 8 characters)*"
                  className="pl-12 pr-12"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                />
                <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                  <Lock className="h-5 w-5" />
                </span>
                <button
                  type="button"
                  className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
              
              <div className="relative">
                <Input
                  name="confirmPassword"
                  type="password"
                  placeholder="Confirm Password*"
                  className="pl-12"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  required
                />
                <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                  <Lock className="h-5 w-5" />
                </span>
              </div>
              
              <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                {loading ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    Sending OTP...
                  </>
                ) : (
                  <>
                    <Phone className="w-4 h-4 mr-2" />
                    Register with Mobile
                  </>
                )}
              </Button>
            </form>
          </TabsContent>
        </Tabs>
        
        <div className="mt-6 text-center text-sm text-gray-600">
          By registering, you agree to our{' '}
          <a href="/terms" className="text-primary hover:underline">Terms of Service</a>
          {' '}and{' '}
          <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a>
        </div>
      </CardContent>
    </Card>
  )
}
