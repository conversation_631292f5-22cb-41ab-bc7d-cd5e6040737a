'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Star, 
  MessageSquare, 
  TrendingUp, 
  Users, 
  Filter, 
  Calendar, 
  ThumbsUp,
  CheckCircle,
  XCircle,
  Clock,
  Eye
} from 'lucide-react';
import { getCurrentUser } from 'aws-amplify/auth';
import EntityReviewService from '@/lib/services/entityReviewService';

interface AdminReviewsDashboardProps {
  className?: string;
}

interface ReviewData {
  id: string;
  name: string;
  email: string;
  title: string;
  review: string;
  rating: number;
  category: string;
  reviewTarget: 'ADMIN' | 'VENDOR';
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  entityType?: string;
  entityName?: string;
  entityId?: string;
  wouldRecommend: boolean;
  createdAt: string;
  adminNotes?: string;
  moderatedBy?: string;
  moderatedAt?: string;
}

export default function AdminReviewsDashboard({ className }: AdminReviewsDashboardProps) {
  const [platformReviews, setPlatformReviews] = useState<ReviewData[]>([]);
  const [vendorReviews, setVendorReviews] = useState<ReviewData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('platform');
  
  // Filters
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [sortBy, setSortBy] = useState('newest');

  useEffect(() => {
    loadReviews();
  }, [statusFilter, sortBy, activeTab]);

  const loadReviews = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get current user (admin)
      const user = await getCurrentUser();
      const userId = user.sub || user.userId || user.username;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      if (activeTab === 'platform') {
        // Load platform reviews (reviewTarget = 'ADMIN')
        const result = await EntityReviewService.getAdminReviews({
          reviewTarget: 'ADMIN',
          statusFilter,
          sortBy,
          limit: 50
        });

        if (result.success) {
          setPlatformReviews(result.data || []);
        } else {
          setError(result.error || 'Failed to load platform reviews');
        }
      } else {
        // Load vendor reviews for admin oversight (reviewTarget = 'VENDOR')
        const result = await EntityReviewService.getAdminReviews({
          reviewTarget: 'VENDOR',
          statusFilter,
          sortBy,
          limit: 50
        });

        if (result.success) {
          setVendorReviews(result.data || []);
        } else {
          setError(result.error || 'Failed to load vendor reviews');
        }
      }

    } catch (err) {
      console.error('Error loading reviews:', err);
      setError(err instanceof Error ? err.message : 'Failed to load reviews');
    } finally {
      setLoading(false);
    }
  };

  const handleReviewAction = async (reviewId: string, action: 'APPROVE' | 'REJECT', notes?: string) => {
    try {
      const result = await EntityReviewService.moderateReview(reviewId, action, notes);

      if (result.success) {
        // Update the review in the local state
        const updateReview = (review: ReviewData) => 
          review.id === reviewId 
            ? { 
                ...review, 
                status: action === 'APPROVE' ? 'APPROVED' : 'REJECTED',
                adminNotes: notes,
                moderatedAt: new Date().toISOString()
              }
            : review;

        if (activeTab === 'platform') {
          setPlatformReviews(prev => prev.map(updateReview));
        } else {
          setVendorReviews(prev => prev.map(updateReview));
        }
      } else {
        setError(result.error || 'Failed to moderate review');
      }

    } catch (err) {
      console.error('Error moderating review:', err);
      setError(err instanceof Error ? err.message : 'Failed to moderate review');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'APPROVED': return 'bg-green-100 text-green-800';
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'REJECTED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'PENDING': return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'REJECTED': return <XCircle className="w-4 h-4 text-red-600" />;
      default: return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
      />
    ));
  };

  const ReviewCard = ({ review }: { review: ReviewData }) => (
    <Card key={review.id} className="overflow-hidden">
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Review Header */}
          <div className="flex justify-between items-start">
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <h3 className="font-semibold text-gray-900">{review.title}</h3>
                <Badge className={getStatusBadgeColor(review.status)}>
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(review.status)}
                    <span>{review.status}</span>
                  </div>
                </Badge>
                {review.entityType && (
                  <Badge variant="outline">
                    {review.entityType}
                  </Badge>
                )}
              </div>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span>By {review.name}</span>
                <span>•</span>
                <span>{review.email}</span>
                {review.entityName && (
                  <>
                    <span>•</span>
                    <span>{review.entityName}</span>
                  </>
                )}
                <span>•</span>
                <span>{formatDate(review.createdAt)}</span>
              </div>
            </div>
            <div className="flex items-center space-x-1">
              {renderStars(review.rating)}
              <span className="ml-1 text-sm font-medium text-gray-700">
                {review.rating}/5
              </span>
            </div>
          </div>

          {/* Review Content */}
          <div className="space-y-2">
            <p className="text-gray-800">{review.review}</p>
            
            {review.wouldRecommend && (
              <div className="flex items-center space-x-1 text-green-600">
                <ThumbsUp className="h-4 w-4" />
                <span className="text-sm">Would recommend</span>
              </div>
            )}
          </div>

          {/* Admin Actions */}
          {review.status === 'PENDING' && (
            <div className="border-t pt-4">
              <div className="flex space-x-2">
                <Button
                  onClick={() => handleReviewAction(review.id, 'APPROVE')}
                  size="sm"
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Approve
                </Button>
                <Button
                  onClick={() => handleReviewAction(review.id, 'REJECT')}
                  size="sm"
                  variant="destructive"
                >
                  <XCircle className="w-4 h-4 mr-1" />
                  Reject
                </Button>
              </div>
            </div>
          )}

          {/* Admin Notes */}
          {review.adminNotes && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
              <div className="flex items-center space-x-2 mb-1">
                <MessageSquare className="h-4 w-4 text-gray-600" />
                <span className="text-sm font-medium text-gray-800">Admin Notes</span>
                {review.moderatedAt && (
                  <span className="text-xs text-gray-600">
                    {formatDate(review.moderatedAt)}
                  </span>
                )}
              </div>
              <p className="text-gray-700 text-sm">{review.adminNotes}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-48 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={loadReviews}>Try Again</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentReviews = activeTab === 'platform' ? platformReviews : vendorReviews;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Admin Reviews Dashboard</h1>
        <Button onClick={loadReviews} variant="outline">
          Refresh
        </Button>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="platform">Platform Reviews</TabsTrigger>
          <TabsTrigger value="vendor">Vendor Reviews Oversight</TabsTrigger>
        </TabsList>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Filters:</span>
              </div>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Status</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="APPROVED">Approved</SelectItem>
                  <SelectItem value="REJECTED">Rejected</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                  <SelectItem value="highest-rating">Highest Rating</SelectItem>
                  <SelectItem value="lowest-rating">Lowest Rating</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <TabsContent value="platform" className="space-y-4">
          <div className="space-y-4">
            {currentReviews.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Platform Reviews Found</h3>
                  <p className="text-gray-600">
                    Platform reviews will appear here when users submit reviews about the overall platform experience.
                  </p>
                </CardContent>
              </Card>
            ) : (
              currentReviews.map((review) => <ReviewCard key={review.id} review={review} />)
            )}
          </div>
        </TabsContent>

        <TabsContent value="vendor" className="space-y-4">
          <div className="space-y-4">
            {currentReviews.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Vendor Reviews Found</h3>
                  <p className="text-gray-600">
                    Vendor reviews will appear here for admin oversight and moderation if needed.
                  </p>
                </CardContent>
              </Card>
            ) : (
              currentReviews.map((review) => <ReviewCard key={review.id} review={review} />)
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
