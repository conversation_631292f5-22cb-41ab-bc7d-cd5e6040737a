"use client"

import { useSafeTranslation } from '@/hooks/use-safe-translation'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle, AlertCircle } from "lucide-react"

export function MultiLanguageHeaderTest() {
  const { t, i18n } = useSafeTranslation()

  const switchLanguage = (lng: string) => {
    i18n.changeLanguage(lng)
  }

  // Available languages with their native names
  const languages = [
    { code: 'en', name: 'English', native: 'English' },
    { code: 'ta', name: 'Tamil', native: 'தமிழ்' },
    { code: 'hi', name: 'Hindi', native: 'हिंदी' },
    { code: 'kn', name: 'Kannada', native: 'ಕನ್ನಡ' },
    { code: 'ml', name: 'Malayalam', native: 'മലയാളം' },
    { code: 'te', name: 'Telugu', native: 'తెలుగు' },
    { code: 'gu', name: 'Gujarati', native: 'ગુજરાતી' },
    { code: 'mr', name: 'Marathi', native: 'मराठी' },
    { code: 'bn', name: 'Bengali', native: 'বাংলা' },
    { code: 'pa', name: 'Punjabi', native: 'ਪੰਜਾਬੀ' },
    { code: 'ur', name: 'Urdu', native: 'اردو' },
    { code: 'or', name: 'Odia', native: 'ଓଡ଼ିଆ' }
  ]

  // Test translation keys
  const testKeys = [
    // Main Navigation
    { key: 'header.vendors.title', fallback: 'Vendors', category: 'Main Navigation' },
    { key: 'header.venues.title', fallback: 'Venues', category: 'Main Navigation' },
    { key: 'header.shop.title', fallback: 'Shop', category: 'Main Navigation' },
    { key: 'header.planning.title', fallback: 'Planning Tools', category: 'Main Navigation' },
    { key: 'header.community.title', fallback: 'Community', category: 'Main Navigation' },
    
    // Vendor Categories
    { key: 'header.vendors.photography.title', fallback: 'Photography', category: 'Vendor Categories' },
    { key: 'header.vendors.makeupBeauty.title', fallback: 'Makeup & Beauty', category: 'Vendor Categories' },
    { key: 'header.vendors.planningDecor.title', fallback: 'Planning & Decor', category: 'Vendor Categories' },
    { key: 'header.vendors.bridalWear.title', fallback: 'Bridal Wear', category: 'Vendor Categories' },
    { key: 'header.vendors.groomWear.title', fallback: 'Groom Wear', category: 'Vendor Categories' },
    
    // Venue Categories
    { key: 'header.venues.categories.marriageMahal', fallback: 'Marriage Mahal', category: 'Venue Categories' },
    { key: 'header.venues.categories.kalyanaMandapam', fallback: 'Kalyana Mandapam', category: 'Venue Categories' },
    { key: 'header.venues.categories.banquetHall', fallback: 'Banquet Hall', category: 'Venue Categories' },
    
    // Badges
    { key: 'header.vendors.badges.hot', fallback: 'HOT', category: 'Badges' },
    { key: 'header.vendors.badges.trending', fallback: 'TRENDING', category: 'Badges' },
    { key: 'header.vendors.badges.luxury', fallback: 'LUXURY', category: 'Badges' },
    { key: 'header.vendors.badges.premium', fallback: 'PREMIUM', category: 'Badges' }
  ]

  // Function to check if translation exists and is different from key
  const checkTranslation = (key: string, fallback: string) => {
    const translation = t(key, fallback)
    const exists = translation !== key && translation !== fallback
    return {
      key,
      fallback,
      translation,
      exists,
      status: exists ? 'success' : 'missing'
    }
  }

  // Group test keys by category
  const groupedKeys = testKeys.reduce((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = []
    }
    acc[item.category].push(item)
    return acc
  }, {} as Record<string, typeof testKeys>)

  const currentLanguage = languages.find(lang => lang.code === i18n.language) || languages[0]

  return (
    <div className="w-full max-w-7xl mx-auto p-4">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-center">Multi-Language Header Translation Test</CardTitle>
          <p className="text-center text-sm text-gray-600">
            Current Language: <span className="font-semibold">{currentLanguage.native} ({currentLanguage.name})</span>
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-2 mb-6">
            {languages.map((language) => (
              <Button
                key={language.code}
                variant={i18n.language === language.code ? 'default' : 'outline'}
                size="sm"
                onClick={() => switchLanguage(language.code)}
                className="text-xs"
              >
                {language.native}
              </Button>
            ))}
          </div>
          
          <div className="text-sm text-gray-600 text-center">
            <AlertCircle className="w-4 h-4 inline mr-1" />
            Switch between languages to test header translations. Green checkmarks indicate successful translations.
          </div>
        </CardContent>
      </Card>

      {Object.entries(groupedKeys).map(([category, keys]) => {
        const results = keys.map(item => checkTranslation(item.key, item.fallback))
        const successCount = results.filter(r => r.status === 'success').length
        const totalCount = results.length
        const successRate = Math.round((successCount / totalCount) * 100)

        return (
          <Card key={category} className="mb-4">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {category}
                <Badge variant={successRate === 100 ? "default" : successRate > 50 ? "secondary" : "destructive"}>
                  {successCount}/{totalCount} ({successRate}%)
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {results.map((result, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-2 flex-1">
                      {result.status === 'success' ? (
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                      ) : (
                        <XCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
                      )}
                      <span className="text-xs font-mono text-gray-600 truncate">{result.key}</span>
                    </div>
                    <div className="text-right ml-2">
                      <div className="font-semibold text-sm">{result.translation}</div>
                      <div className="text-xs text-gray-500">Fallback: {result.fallback}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )
      })}

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Translation Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{languages.length}</div>
              <div className="text-sm text-gray-600">Languages Supported</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{testKeys.length}</div>
              <div className="text-sm text-gray-600">Translation Keys</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{Object.keys(groupedKeys).length}</div>
              <div className="text-sm text-gray-600">Categories</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">{currentLanguage.native}</div>
              <div className="text-sm text-gray-600">Current Language</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
