"use client"

import { useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import {
  usePrefetchCriticalRoutes,
  usePrefetchDashboardRoutes,
  useLinkPrefetching,
  useCacheWarming,
  useNavigationPerformance,
  useHoverPreload
} from '@/lib/performance/navigation-optimizer'

/**
 * Navigation Optimizer Component
 * 
 * This component implements various performance optimizations to reduce
 * first-click loading times throughout the application.
 */
export function NavigationOptimizer() {
  const { isAuthenticated, userType } = useAuth()

  // Enable all performance optimizations
  usePrefetchCriticalRoutes()
  usePrefetchDashboardRoutes(isAuthenticated, userType)
  useLinkPrefetching()
  useCacheWarming()
  useNavigationPerformance()

  // Additional optimizations
  useEffect(() => {
    // Preload critical CSS and fonts
    const preloadCriticalResources = () => {
      // Preload fonts
      const fontLink = document.createElement('link')
      fontLink.rel = 'preload'
      fontLink.as = 'font'
      fontLink.type = 'font/woff2'
      fontLink.crossOrigin = 'anonymous'
      fontLink.href = '/fonts/inter-var.woff2' // Adjust path as needed
      document.head.appendChild(fontLink)

      // Preload critical images
      const heroImage = new Image()
      heroImage.src = '/hero_image_1.webp'
      
      const logoImage = new Image()
      logoImage.src = '/logo.png'
    }

    // Run after a short delay to not block initial render
    const timer = setTimeout(preloadCriticalResources, 100)
    return () => clearTimeout(timer)
  }, [])

  // Optimize service worker registration
  useEffect(() => {
    if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
      navigator.serviceWorker.register('/sw.js').catch(console.error)
    }
  }, [])

  return null // This component doesn't render anything
}

/**
 * Enhanced Link component with performance optimizations
 */
interface FastLinkProps {
  href: string
  children: React.ReactNode
  className?: string
  prefetch?: boolean
  instant?: boolean
}

export function FastLink({ 
  href, 
  children, 
  className, 
  prefetch = true,
  instant = false,
  ...props 
}: FastLinkProps) {
  const handleMouseDown = () => {
    if (instant) {
      // Start navigation on mousedown for instant feel
      window.location.href = href
    }
  }

  const handleMouseEnter = () => {
    if (prefetch) {
      // Prefetch on hover
      const link = document.createElement('link')
      link.rel = 'prefetch'
      link.href = href
      document.head.appendChild(link)
    }
  }

  return (
    <a
      href={href}
      className={className}
      onMouseEnter={handleMouseEnter}
      onMouseDown={instant ? handleMouseDown : undefined}
      {...props}
    >
      {children}
    </a>
  )
}

/**
 * Performance monitoring component
 */
export function PerformanceMonitor() {
  useEffect(() => {
    // Monitor Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming
          console.log('Navigation timing:', {
            domContentLoaded: navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart,
            loadComplete: navEntry.loadEventEnd - navEntry.loadEventStart,
            totalTime: navEntry.loadEventEnd - navEntry.fetchStart
          })
        }
      })
    })

    observer.observe({ entryTypes: ['navigation'] })

    return () => observer.disconnect()
  }, [])

  return null
}

/**
 * Optimized Link component with built-in prefetching
 */
interface OptimizedLinkProps extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, 'href'> {
  href: string
  children: React.ReactNode
  prefetch?: boolean
}

export function OptimizedLink({
  href,
  children,
  className,
  prefetch = true,
  onMouseEnter,
  ...props
}: OptimizedLinkProps) {
  const router = useRouter()
  const { handleMouseEnter } = useHoverPreload()

  const handleClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    router.push(href)
  }, [router, href])

  const handleHover = useCallback((e: React.MouseEvent<HTMLAnchorElement>) => {
    if (prefetch) {
      handleMouseEnter(href)
    }
    onMouseEnter?.(e)
  }, [prefetch, handleMouseEnter, href, onMouseEnter])

  return (
    <a
      href={href}
      className={className}
      onClick={handleClick}
      onMouseEnter={handleHover}
      {...props}
    >
      {children}
    </a>
  )
}

export default NavigationOptimizer
