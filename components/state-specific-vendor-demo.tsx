"use client"

import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MapPin, Camera, Sparkles, Crown, Music, Utensils } from "lucide-react"
import { useStateContext, INDIAN_STATES } from '@/contexts/StateContext'
import { getVendorsForState, STATE_VENDOR_CONFIGS } from '@/utils/state-specific-vendors'

export function StateSpecificVendorDemo() {
  const { selectedState, setSelectedState } = useStateContext()
  const [selectedCategory, setSelectedCategory] = useState<string>('Photography')

  const stateVendorConfig = getVendorsForState(selectedState.code)
  
  const categoryIcons = {
    'Photography': Camera,
    'Makeup & Beauty': Sparkles,
    'Traditional Wear': Crown,
    'Music & Entertainment': Music,
    'Catering': Utensils
  }

  const categoryColors = {
    'Photography': 'bg-blue-100 text-blue-800 border-blue-200',
    'Makeup & Beauty': 'bg-pink-100 text-pink-800 border-pink-200',
    'Traditional Wear': 'bg-purple-100 text-purple-800 border-purple-200',
    'Music & Entertainment': 'bg-green-100 text-green-800 border-green-200',
    'Catering': 'bg-orange-100 text-orange-800 border-orange-200'
  }

  const selectedVendor = stateVendorConfig?.vendors.find(v => v.category === selectedCategory)

  return (
    <div className="w-full max-w-6xl mx-auto p-4">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-center flex items-center justify-center gap-2">
            <MapPin className="w-6 h-6" />
            State-Specific Vendor Menu Demo
          </CardTitle>
          <p className="text-center text-gray-600">
            See how vendor categories change based on selected state and cultural preferences
          </p>
        </CardHeader>
        <CardContent>
          {/* State Selector */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3">Select State:</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {INDIAN_STATES.map((state) => (
                <Button
                  key={state.code}
                  variant={selectedState.code === state.code ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedState(state)}
                  className="text-xs"
                >
                  {state.name}
                </Button>
              ))}
            </div>
          </div>

          {/* Current State Info */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-blue-900">
                  📍 {selectedState.name}
                </h3>
                <p className="text-blue-700">
                  Language: {selectedState.language} | Code: {selectedState.code}
                </p>
              </div>
              <Badge variant="secondary">
                {stateVendorConfig ? 'State-Specific Data Available' : 'Using Default Data'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {stateVendorConfig ? (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Category Selector */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Vendor Categories</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {stateVendorConfig.vendors.map((vendor) => {
                  const Icon = categoryIcons[vendor.category as keyof typeof categoryIcons] || Camera
                  return (
                    <Button
                      key={vendor.category}
                      variant={selectedCategory === vendor.category ? 'default' : 'ghost'}
                      className="w-full justify-start"
                      onClick={() => setSelectedCategory(vendor.category)}
                    >
                      <Icon className="w-4 h-4 mr-2" />
                      {vendor.category}
                    </Button>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Category Details */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {(() => {
                  const Icon = categoryIcons[selectedCategory as keyof typeof categoryIcons] || Camera
                  return <Icon className="w-5 h-5" />
                })()}
                {selectedCategory} in {selectedState.name}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedVendor ? (
                <div className="space-y-6">
                  {/* Subcategories */}
                  <div>
                    <h4 className="font-semibold mb-3 text-gray-800">Standard Services:</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedVendor.subcategories.map((sub, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {sub}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* State-Specific Specialties */}
                  <div>
                    <h4 className="font-semibold mb-3 text-gray-800">
                      🌟 {selectedState.name} Specialties:
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedVendor.specialties.map((specialty, index) => (
                        <Badge 
                          key={index} 
                          className={`text-xs ${categoryColors[selectedCategory as keyof typeof categoryColors] || 'bg-gray-100 text-gray-800'}`}
                        >
                          📍 {specialty}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Cultural Items */}
                  {selectedVendor.culturalItems && selectedVendor.culturalItems.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-3 text-gray-800">
                        🎭 Cultural Specialties:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedVendor.culturalItems.map((item, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            🎨 {item}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Popular Services */}
                  {selectedVendor.popularServices && selectedVendor.popularServices.length > 0 && (
                    <div>
                      <h4 className="font-semibold mb-3 text-gray-800">
                        🔥 Popular Services:
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedVendor.popularServices.map((service, index) => (
                          <Badge key={index} variant="destructive" className="text-xs">
                            ⭐ {service}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-gray-500">No specific data available for this category in {selectedState.name}</p>
              )}
            </CardContent>
          </Card>
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-8">
            <p className="text-gray-500 mb-4">
              State-specific vendor data not available for {selectedState.name}
            </p>
            <p className="text-sm text-gray-400">
              Available states: {STATE_VENDOR_CONFIGS.map(config => config.stateName).join(', ')}
            </p>
          </CardContent>
        </Card>
      )}

      {/* Traditional Wear & Cultural Info */}
      {stateVendorConfig && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Traditional Wear</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-pink-800 mb-2">👰 Bridal Wear:</h4>
                  <div className="flex flex-wrap gap-1">
                    {stateVendorConfig.traditionalWear.bridal.map((item, index) => (
                      <Badge key={index} variant="outline" className="text-xs text-pink-700">
                        {item}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-blue-800 mb-2">🤵 Groom Wear:</h4>
                  <div className="flex flex-wrap gap-1">
                    {stateVendorConfig.traditionalWear.groom.map((item, index) => (
                      <Badge key={index} variant="outline" className="text-xs text-blue-700">
                        {item}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Cultural Specialties</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {stateVendorConfig.culturalSpecialties.map((specialty, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    🎭 {specialty}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
