"use client"

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Loader2, CheckCircle, XCircle, Play, RefreshCw } from 'lucide-react'
import { BlogService, BlogCategory, BlogStatus, AuthorType } from '@/lib/services/blogService'
import { useToast } from "@/components/ui/use-toast"

interface BlogCreationResult {
  success: boolean
  title: string
  id?: string
  error?: string
}

const testBlogs = [
  {
    title: "10 Essential Wedding Planning Tips for Indian Weddings",
    content: "Planning an Indian wedding can be overwhelming with its rich traditions, multiple ceremonies, and elaborate celebrations. Here are 10 essential tips to make your wedding planning journey smooth and memorable: 1. Start Early - Begin planning at least 8-12 months in advance. 2. Set a Realistic Budget - Allocate funds properly. 3. Choose the Right Venue - Consider capacity and location. 4. Plan the Guest List - Coordinate with both families. 5. Book Vendors Early - Good vendors get booked quickly.",
    excerpt: "Planning an Indian wedding can be overwhelming. Here are the top 10 tips to make your wedding planning journey smooth and memorable.",
    category: BlogCategory.WEDDING_PLANNING,
    authorName: "Priya Sharma",
    authorType: AuthorType.EXPERT,
    featuredImage: "/hero_image.png",
    tags: ["wedding planning", "indian wedding", "tips", "guide"],
    status: BlogStatus.PUBLISHED
  },
  {
    title: "How to Choose the Perfect Wedding Venue",
    content: "Selecting the perfect wedding venue is one of the most important decisions you'll make for your big day. Your venue sets the tone for your entire celebration and significantly impacts your budget. Consider your wedding style, capacity needs, location accessibility, and budget constraints when making this crucial decision.",
    excerpt: "Venue selection is crucial for your big day. Here's how to pick the best one for your style and budget.",
    category: BlogCategory.VENUE_SELECTION,
    authorName: "Anjali Menon",
    authorType: AuthorType.VENDOR,
    featuredImage: "/mahal.png",
    tags: ["venue selection", "wedding venue", "planning", "budget"],
    status: BlogStatus.PUBLISHED
  },
  {
    title: "Latest Bridal Lehenga Trends for 2024",
    content: "2024 is bringing exciting new trends in bridal fashion, especially for lehengas. Color trends include sage green, dusty pink, royal blue, and ivory with gold. Fabric innovations focus on sustainable materials and lightweight silks. Design elements feature minimalist embroidery, geometric patterns, and cape sleeves.",
    excerpt: "Discover the hottest bridal lehenga trends that are taking the wedding fashion world by storm this year.",
    category: BlogCategory.FASHION_STYLE,
    authorName: "Kavya Patel",
    authorType: AuthorType.VENDOR,
    featuredImage: "/wedding_jewels.png",
    tags: ["bridal fashion", "lehenga", "trends", "2024", "style"],
    status: BlogStatus.PUBLISHED
  },
  {
    title: "Capturing Perfect Wedding Memories: Photography Tips",
    content: "Your wedding photos will be treasured for generations. Here are expert tips to ensure your wedding photography captures every precious moment. Pre-wedding planning is crucial - meet with your photographer multiple times, share your vision, and create a comprehensive shot list.",
    excerpt: "Make your wedding album unforgettable with these expert photography tips from professional wedding photographers.",
    category: BlogCategory.PHOTOGRAPHY_VIDEOGRAPHY,
    authorName: "Suresh Kumar",
    authorType: AuthorType.VENDOR,
    featuredImage: "/photographer.png",
    tags: ["wedding photography", "tips", "memories", "professional"],
    status: BlogStatus.PUBLISHED
  },
  {
    title: "Top 5 Wedding Catering Trends in 2024",
    content: "Wedding catering is evolving with exciting new trends that focus on experience, sustainability, and personalization. Interactive food stations, sustainable sourcing, fusion cuisine, health-conscious options, and experiential dining are leading the way in 2024.",
    excerpt: "From fusion menus to live food stations, discover the latest trends in wedding catering that will wow your guests.",
    category: BlogCategory.CATERING_FOOD,
    authorName: "Chef Arjun",
    authorType: AuthorType.VENDOR,
    featuredImage: "/catering.png",
    tags: ["catering", "food trends", "wedding menu", "2024"],
    status: BlogStatus.PUBLISHED
  },
  {
    title: "Budget-Friendly Wedding Decoration Ideas",
    content: "Creating stunning wedding decorations doesn't have to break the bank. DIY centerpieces, creative lighting, fabric draping, natural elements, and smart repurposing can transform your venue beautifully while staying within budget.",
    excerpt: "Create stunning wedding decorations without breaking the bank. These creative ideas will transform your venue beautifully.",
    category: BlogCategory.DECORATIONS_THEMES,
    authorName: "Rohit Gupta",
    authorType: AuthorType.VENDOR,
    featuredImage: "/decorators.png",
    tags: ["decorations", "budget", "DIY", "wedding decor"],
    status: BlogStatus.PUBLISHED
  },
  {
    title: "Wedding Budget Planning: Save Without Sacrificing Style",
    content: "Planning a beautiful wedding on a budget requires smart strategies and creative thinking. Proper budget allocation, money-saving strategies, smart vendor choices, and knowing what costs to watch can help you create your dream wedding affordably.",
    excerpt: "Smart budgeting tips to help you have a beautiful wedding without breaking the bank.",
    category: BlogCategory.BUDGET_FINANCE,
    authorName: "Meera Joshi",
    authorType: AuthorType.EXPERT,
    featuredImage: "/return_gift.png",
    tags: ["budget", "wedding planning", "money saving", "finance"],
    status: BlogStatus.PUBLISHED
  },
  {
    title: "Real Wedding: Priya & Raj's Garden Celebration",
    content: "Love was in the air as Priya and Raj celebrated their union in a breathtaking garden wedding that perfectly blended tradition with modern elegance. Their celebration featured sage green and ivory colors, heritage venue, and personalized touches.",
    excerpt: "Follow Priya and Raj's journey as they plan their magical garden wedding, complete with budget breakdown and vendor tips.",
    category: BlogCategory.REAL_WEDDINGS,
    authorName: "Wedding Stories Team",
    authorType: AuthorType.ADMIN,
    featuredImage: "/hero_image.png",
    tags: ["real wedding", "garden wedding", "budget breakdown", "inspiration"],
    status: BlogStatus.PUBLISHED
  },
  {
    title: "Expert Tips: 5 Common Wedding Planning Mistakes",
    content: "After planning hundreds of weddings, we've identified the top 5 mistakes couples make: not setting realistic budgets, booking vendors too late, ignoring guest experience, micromanaging details, and forgetting about the marriage itself.",
    excerpt: "Learn from wedding planning experts about the top 5 mistakes couples make and how to avoid them for a stress-free celebration.",
    category: BlogCategory.EXPERT_TIPS,
    authorName: "Dr. Sunita Reddy",
    authorType: AuthorType.EXPERT,
    featuredImage: "/placeholder.jpg",
    tags: ["expert tips", "wedding planning", "mistakes", "advice"],
    status: BlogStatus.PUBLISHED
  },
  {
    title: "Vendor Spotlight: Mumbai's Top Wedding Photographer",
    content: "Meet Arjun Shah, one of Mumbai's most sought-after wedding photographers. With over 8 years of experience and 500+ weddings captured, Arjun specializes in photojournalistic and fine art photography with signature techniques.",
    excerpt: "Meet Arjun Shah, Mumbai's top wedding photographer, as he shares his journey, style, and tips for capturing perfect wedding moments.",
    category: BlogCategory.VENDOR_SPOTLIGHT,
    authorName: "Editorial Team",
    authorType: AuthorType.ADMIN,
    featuredImage: "/photographer.png",
    tags: ["vendor spotlight", "photographer", "Mumbai", "wedding photography"],
    status: BlogStatus.PUBLISHED
  }
]

export function CreateTestBlogs() {
  const [isCreating, setIsCreating] = useState(false)
  const [progress, setProgress] = useState(0)
  const [results, setResults] = useState<BlogCreationResult[]>([])
  const [currentBlog, setCurrentBlog] = useState<string>('')
  const { toast } = useToast()

  const createTestBlogs = async () => {
    setIsCreating(true)
    setProgress(0)
    setResults([])
    setCurrentBlog('')

    const newResults: BlogCreationResult[] = []

    for (let i = 0; i < testBlogs.length; i++) {
      const blog = testBlogs[i]
      setCurrentBlog(blog.title)
      setProgress((i / testBlogs.length) * 100)

      try {
        const createdBlog = await BlogService.createBlog(blog)
        newResults.push({
          success: true,
          title: blog.title,
          id: createdBlog.id
        })
        
        toast({
          title: "Blog Created",
          description: `Successfully created "${blog.title}"`,
        })
      } catch (error) {
        newResults.push({
          success: false,
          title: blog.title,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        
        toast({
          title: "Error",
          description: `Failed to create "${blog.title}"`,
          variant: "destructive"
        })
      }

      setResults([...newResults])
      
      // Add delay between requests
      if (i < testBlogs.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1500))
      }
    }

    setProgress(100)
    setCurrentBlog('')
    setIsCreating(false)

    const successful = newResults.filter(r => r.success).length
    toast({
      title: "Blog Creation Complete",
      description: `Successfully created ${successful} out of ${testBlogs.length} blogs`,
    })
  }

  const resetResults = () => {
    setResults([])
    setProgress(0)
    setCurrentBlog('')
  }

  const successCount = results.filter(r => r.success).length
  const failureCount = results.filter(r => !r.success).length

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Play className="w-5 h-5" />
          Create Test Blogs
        </CardTitle>
        <p className="text-sm text-gray-600">
          Create 10 sample blogs across different categories for testing purposes
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Action Buttons */}
        <div className="flex gap-4">
          <Button 
            onClick={createTestBlogs} 
            disabled={isCreating}
            className="flex items-center gap-2"
          >
            {isCreating ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            {isCreating ? 'Creating Blogs...' : 'Create Test Blogs'}
          </Button>
          
          {results.length > 0 && (
            <Button 
              onClick={resetResults} 
              variant="outline"
              className="flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Reset
            </Button>
          )}
        </div>

        {/* Progress */}
        {isCreating && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress: {Math.round(progress)}%</span>
              <span>{results.length} / {testBlogs.length} completed</span>
            </div>
            <Progress value={progress} className="w-full" />
            {currentBlog && (
              <p className="text-sm text-gray-600">
                Creating: {currentBlog}
              </p>
            )}
          </div>
        )}

        {/* Summary */}
        {results.length > 0 && (
          <div className="flex gap-4">
            <Badge variant="default" className="bg-green-100 text-green-800">
              <CheckCircle className="w-3 h-3 mr-1" />
              Success: {successCount}
            </Badge>
            {failureCount > 0 && (
              <Badge variant="destructive">
                <XCircle className="w-3 h-3 mr-1" />
                Failed: {failureCount}
              </Badge>
            )}
          </div>
        )}

        {/* Results */}
        {results.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-semibold">Results:</h3>
            <div className="max-h-60 overflow-y-auto space-y-1">
              {results.map((result, index) => (
                <div 
                  key={index}
                  className={`flex items-center gap-2 p-2 rounded text-sm ${
                    result.success 
                      ? 'bg-green-50 text-green-800' 
                      : 'bg-red-50 text-red-800'
                  }`}
                >
                  {result.success ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-600" />
                  )}
                  <span className="flex-1">{result.title}</span>
                  {result.success && result.id && (
                    <span className="text-xs text-gray-500">ID: {result.id}</span>
                  )}
                  {!result.success && result.error && (
                    <span className="text-xs text-red-600">{result.error}</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Blog Categories Preview */}
        <div className="space-y-2">
          <h3 className="font-semibold">Blogs to be created:</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
            {testBlogs.map((blog, index) => (
              <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                <span className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs">
                  {index + 1}
                </span>
                <div className="flex-1">
                  <div className="font-medium truncate">{blog.title}</div>
                  <div className="text-xs text-gray-500">{blog.category.replace('_', ' ')}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
