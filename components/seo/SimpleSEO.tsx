'use client';

import Head from 'next/head';
import { SITE_URL } from '@/lib/config/seo';

interface SimpleSEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
}

export const SimpleSEO: React.FC<SimpleSEOProps> = ({
  title = "Thirumanam 360 - Your Dream Wedding Starts Here",
  description = "Discover the best wedding vendors, venues, and inspiration for your perfect day. Plan your dream wedding with Thirumanam 360.",
  keywords = "wedding, vendors, venues, photography, catering, decoration, bridal wear, wedding planning, Thirumanam 360",
  image = "https://images.unsplash.com/photo-1519741497674-611481863552?w=1200&h=630&fit=crop&crop=center",
  url = "/",
  type = "website"
}) => {
  const fullUrl = url.startsWith('http') ? url : `${SITE_URL}${url}`;
  const fullImageUrl = image.startsWith('http') ? image : `${SITE_URL}${image}`;

  return (
    <Head>
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="robots" content="index,follow" />
      <link rel="canonical" href={fullUrl} />
      
      {/* Open Graph */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={fullImageUrl} />
      <meta property="og:url" content={fullUrl} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content="Thirumanam 360" />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={fullImageUrl} />
      <meta name="twitter:site" content="@thirumanam360" />
    </Head>
  );
};

// Simple structured data component
export const SimpleStructuredData = () => {
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Thirumanam 360",
    "url": SITE_URL,
    "logo": `${SITE_URL}/Thirumanam360_logo.svg`,
    "description": "India's premier wedding planning platform connecting couples with the best wedding vendors, venues, and services.",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "availableLanguage": ["English", "Tamil", "Hindi"]
    },
    "sameAs": [
      "https://www.facebook.com/thirumanam360",
      "https://www.instagram.com/thirumanam360",
      "https://www.twitter.com/thirumanam360"
    ]
  };

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Thirumanam 360",
    "url": SITE_URL,
    "potentialAction": {
      "@type": "SearchAction",
      "target": `${SITE_URL}/search?q={search_term_string}`,
      "query-input": "required name=search_term_string"
    }
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema)
        }}
      />
    </>
  );
};

// Simple Google Analytics component
export const SimpleGoogleAnalytics = () => {
  const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;

  if (!GA_MEASUREMENT_ID) {
    return null;
  }

  return (
    <>
      <script
        async
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      />
      <script
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_MEASUREMENT_ID}');
          `,
        }}
      />
    </>
  );
};
