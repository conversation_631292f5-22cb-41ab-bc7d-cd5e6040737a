'use client';

import {
  OrganizationJsonLd,
  BreadcrumbJsonLd,
  FAQPageJsonLd
} from 'next-seo';
import { businessInfo, SITE_URL } from '@/lib/config/seo';

// Organization Schema for the main business
export const OrganizationSchema = () => (
  <OrganizationJsonLd
    type="Organization"
    id={SITE_URL}
    name={businessInfo.name}
    url={SITE_URL}
    logo={businessInfo.logo}
    description={businessInfo.description}
    address={{
      streetAddress: businessInfo.address.streetAddress,
      addressLocality: businessInfo.address.addressLocality,
      addressRegion: businessInfo.address.addressRegion,
      postalCode: businessInfo.address.postalCode,
      addressCountry: businessInfo.address.addressCountry,
    }}
    contactPoint={[
      {
        telephone: businessInfo.contactPoint.telephone,
        contactType: businessInfo.contactPoint.contactType,
        availableLanguage: businessInfo.contactPoint.availableLanguage,
      },
    ]}
    sameAs={businessInfo.sameAs}
  />
);

// Website Schema with search functionality
export const WebsiteSchema = () => (
  <script
    type="application/ld+json"
    dangerouslySetInnerHTML={{
      __html: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'WebSite',
        name: businessInfo.name,
        url: SITE_URL,
        potentialAction: {
          '@type': 'SearchAction',
          target: `${SITE_URL}/search?q={search_term_string}`,
          'query-input': 'required name=search_term_string',
        },
      }),
    }}
  />
);

// Breadcrumb Schema Component
interface BreadcrumbSchemaProps {
  items: Array<{
    name: string;
    url: string;
  }>;
}

export const BreadcrumbSchema: React.FC<BreadcrumbSchemaProps> = ({ items }) => (
  <BreadcrumbJsonLd
    itemListElements={items.map((item, index) => ({
      position: index + 1,
      name: item.name,
      item: item.url.startsWith('http') ? item.url : `${SITE_URL}${item.url}`,
    }))}
  />
);

// FAQ Schema Component
interface FAQSchemaProps {
  faqs: Array<{
    question: string;
    answer: string;
  }>;
}

export const FAQSchema: React.FC<FAQSchemaProps> = ({ faqs }) => (
  <FAQPageJsonLd
    mainEntity={faqs.map(faq => ({
      questionName: faq.question,
      acceptedAnswerText: faq.answer,
    }))}
  />
);

// Collection Page Schema (for listing pages like vendors, venues)
interface CollectionSchemaProps {
  name: string;
  description: string;
  url: string;
  numberOfItems?: number;
}

export const CollectionSchema: React.FC<CollectionSchemaProps> = ({
  name,
  description,
  url,
  numberOfItems,
}) => (
  <script
    type="application/ld+json"
    dangerouslySetInnerHTML={{
      __html: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'CollectionPage',
        name,
        description,
        url: url.startsWith('http') ? url : `${SITE_URL}${url}`,
        mainEntity: {
          '@type': 'ItemList',
          numberOfItems: numberOfItems || 0,
        },
      }),
    }}
  />
);

// Service Schema for wedding services
interface ServiceSchemaProps {
  name: string;
  description: string;
  provider: string;
  areaServed?: string;
  serviceType: string;
  url?: string;
}

export const ServiceSchema: React.FC<ServiceSchemaProps> = ({
  name,
  description,
  provider,
  areaServed = 'India',
  serviceType,
  url,
}) => (
  <script
    type="application/ld+json"
    dangerouslySetInnerHTML={{
      __html: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'Service',
        name,
        description,
        provider: {
          '@type': 'Organization',
          name: provider,
          url: SITE_URL,
        },
        areaServed: areaServed,
        serviceType: serviceType,
        url: url ? (url.startsWith('http') ? url : `${SITE_URL}${url}`) : SITE_URL,
      }),
    }}
  />
);

// Wedding Planning Service Schema
export const WeddingPlanningServiceSchema = () => (
  <ServiceSchema
    name="Wedding Planning Services"
    description="Comprehensive wedding planning services including vendor coordination, venue booking, and complete wedding management"
    provider="Thirumanam 360"
    serviceType="Wedding Planning"
    url="/planning"
  />
);

// Vendor Marketplace Service Schema
export const VendorMarketplaceServiceSchema = () => (
  <ServiceSchema
    name="Wedding Vendor Marketplace"
    description="Connect with verified wedding vendors including photographers, decorators, caterers, and more"
    provider="Thirumanam 360"
    serviceType="Vendor Marketplace"
    url="/vendors"
  />
);

// Venue Booking Service Schema
export const VenueBookingServiceSchema = () => (
  <ServiceSchema
    name="Wedding Venue Booking"
    description="Find and book beautiful wedding venues, marriage halls, and destination wedding locations"
    provider="Thirumanam 360"
    serviceType="Venue Booking"
    url="/venues"
  />
);

// Wedding Shopping Service Schema
export const WeddingShoppingServiceSchema = () => (
  <ServiceSchema
    name="Wedding Shopping"
    description="Shop for wedding essentials including bridal wear, jewelry, decorations, and accessories"
    provider="Thirumanam 360"
    serviceType="Wedding Shopping"
    url="/shop"
  />
);

// Event Schema for weddings
interface EventSchemaProps {
  name: string;
  description: string;
  startDate: string;
  endDate?: string;
  location: {
    name: string;
    address: string;
  };
  organizer?: string;
  url?: string;
  image?: string;
}

export const EventSchema: React.FC<EventSchemaProps> = ({
  name,
  description,
  startDate,
  endDate,
  location,
  organizer = 'Thirumanam 360',
  url,
  image,
}) => (
  <script
    type="application/ld+json"
    dangerouslySetInnerHTML={{
      __html: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'Event',
        name,
        description,
        startDate,
        endDate: endDate || startDate,
        location: {
          '@type': 'Place',
          name: location.name,
          address: location.address,
        },
        organizer: {
          '@type': 'Organization',
          name: organizer,
          url: SITE_URL,
        },
        url: url ? (url.startsWith('http') ? url : `${SITE_URL}${url}`) : undefined,
        image: image ? (image.startsWith('http') ? image : `${SITE_URL}${image}`) : undefined,
        eventStatus: 'https://schema.org/EventScheduled',
        eventAttendanceMode: 'https://schema.org/OfflineEventAttendanceMode',
      }),
    }}
  />
);

// Review Schema for testimonials
interface ReviewSchemaProps {
  reviews: Array<{
    author: string;
    rating: number;
    reviewBody: string;
    datePublished: string;
  }>;
  itemReviewed: {
    name: string;
    type: string;
  };
}

export const ReviewSchema: React.FC<ReviewSchemaProps> = ({ reviews, itemReviewed }) => (
  <script
    type="application/ld+json"
    dangerouslySetInnerHTML={{
      __html: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'Review',
        itemReviewed: {
          '@type': itemReviewed.type,
          name: itemReviewed.name,
        },
        review: reviews.map(review => ({
          '@type': 'Review',
          author: {
            '@type': 'Person',
            name: review.author,
          },
          reviewRating: {
            '@type': 'Rating',
            ratingValue: review.rating,
            bestRating: 5,
            worstRating: 1,
          },
          reviewBody: review.reviewBody,
          datePublished: review.datePublished,
        })),
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: (reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length).toFixed(1),
          reviewCount: reviews.length,
          bestRating: 5,
          worstRating: 1,
        },
      }),
    }}
  />
);

// Local Business Hours Schema
export const BusinessHoursSchema = () => (
  <script
    type="application/ld+json"
    dangerouslySetInnerHTML={{
      __html: JSON.stringify({
        '@context': 'https://schema.org',
        '@type': 'LocalBusiness',
        name: businessInfo.name,
        url: SITE_URL,
        openingHoursSpecification: [
          {
            '@type': 'OpeningHoursSpecification',
            dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
            opens: '09:00',
            closes: '18:00',
          },
          {
            '@type': 'OpeningHoursSpecification',
            dayOfWeek: ['Saturday'],
            opens: '09:00',
            closes: '17:00',
          },
          {
            '@type': 'OpeningHoursSpecification',
            dayOfWeek: ['Sunday'],
            opens: '10:00',
            closes: '16:00',
          },
        ],
      }),
    }}
  />
);
