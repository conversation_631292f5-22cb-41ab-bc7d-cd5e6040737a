"use client"

import { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import AuthRoutingService, { UserRole } from '@/lib/services/authRouting';
import { RedirectUtils } from '@/lib/utils/redirectUtils';

interface RouteProtectionProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  allowedUserTypes?: UserRole[];
  redirectTo?: string;
}

export default function RouteProtection({
  children,
  requireAuth = true,
  allowedUserTypes = ['customer', 'vendor', 'admin', 'super_admin'],
  redirectTo
}: RouteProtectionProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { isAuthenticated, isLoading, userType, userProfile } = useAuth();

  useEffect(() => {
    // Don't redirect while still loading
    if (isLoading) return;

    // Check if authentication is required
    if (requireAuth && !isAuthenticated) {
      // Store current URL for redirect after login
      RedirectUtils.storeRedirectUrl();
      router.push('/login');
      return;
    }

    // Check if user type is allowed for this route
    if (isAuthenticated && userType && !allowedUserTypes.includes(userType)) {
      const appropriateRoute = redirectTo || AuthRoutingService.getDashboardRoute(userType, userProfile);
      router.push(appropriateRoute);
      return;
    }

    // Check if current route is appropriate for user type
    if (isAuthenticated && userType) {
      const redirectUrl = AuthRoutingService.getRedirectUrl(pathname, userType);
      if (redirectUrl) {
        router.push(redirectUrl);
        return;
      }
    }
  }, [isAuthenticated, isLoading, userType, pathname, router, requireAuth, allowedUserTypes, redirectTo, userProfile]);

  // Show loading while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Show login prompt if authentication is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Authentication Required</h2>
          <p className="text-gray-600 mb-4">Please sign in to access this page.</p>
          <button 
            onClick={() => router.push('/login')}
            className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary/90"
          >
            Sign In
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// Specific route protection components for common use cases

export function VendorOnlyRoute({ children }: { children: React.ReactNode }) {
  return (
    <RouteProtection 
      requireAuth={true} 
      allowedUserTypes={['vendor']}
      redirectTo="/dashboard"
    >
      {children}
    </RouteProtection>
  );
}

export function CustomerOnlyRoute({ children }: { children: React.ReactNode }) {
  return (
    <RouteProtection
      requireAuth={true}
      allowedUserTypes={['customer']}
      redirectTo="/dashboard/vendor"
    >
      {children}
    </RouteProtection>
  );
}

export function AdminOnlyRoute({ children }: { children: React.ReactNode }) {
  return (
    <RouteProtection
      requireAuth={true}
      allowedUserTypes={['admin', 'super_admin']}
      redirectTo="/dashboard"
    >
      {children}
    </RouteProtection>
  );
}

export function SuperAdminOnlyRoute({ children }: { children: React.ReactNode }) {
  return (
    <RouteProtection
      requireAuth={true}
      allowedUserTypes={['super_admin']}
      redirectTo="/dashboard"
    >
      {children}
    </RouteProtection>
  );
}

export function AuthenticatedRoute({ children }: { children: React.ReactNode }) {
  return (
    <RouteProtection requireAuth={true}>
      {children}
    </RouteProtection>
  );
}

export function PublicRoute({ children }: { children: React.ReactNode }) {
  return (
    <RouteProtection requireAuth={false}>
      {children}
    </RouteProtection>
  );
}
