"use client"

import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"

interface LayoutWrapperProps {
  children: React.ReactNode
  showTopHeader?: boolean
  showHeader?: boolean
  showFooter?: boolean
}

export function LayoutWrapper({ 
  children, 
  showTopHeader = true, 
  showHeader = true, 
  showFooter = true 
}: LayoutWrapperProps) {
  return (
    <>
      {showTopHeader && <TopHeader />}
      {showHeader && <Header />}
      {children}
      {showFooter && <Footer />}
    </>
  )
}
