'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertCircle, ExternalLink } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  action?: string;
}

export default function ReviewSystemTest() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [testing, setTesting] = useState(false);

  const runTests = async () => {
    setTesting(true);
    const results: TestResult[] = [];

    // Test 1: Check if review components exist
    try {
      const reviewTypeSelector = await import('@/components/review-type-selector');
      results.push({
        name: 'Review Type Selector Component',
        status: 'pass',
        message: 'Component loaded successfully'
      });
    } catch (error) {
      results.push({
        name: 'Review Type Selector Component',
        status: 'fail',
        message: 'Component failed to load'
      });
    }

    // Test 2: Check platform review form
    try {
      const platformReviewForm = await import('@/components/platform-review-form');
      results.push({
        name: 'Platform Review Form Component',
        status: 'pass',
        message: 'Component loaded successfully'
      });
    } catch (error) {
      results.push({
        name: 'Platform Review Form Component',
        status: 'fail',
        message: 'Component failed to load'
      });
    }

    // Test 3: Check admin dashboard
    try {
      const adminDashboard = await import('@/components/admin-reviews-dashboard');
      results.push({
        name: 'Admin Reviews Dashboard Component',
        status: 'pass',
        message: 'Component loaded successfully'
      });
    } catch (error) {
      results.push({
        name: 'Admin Reviews Dashboard Component',
        status: 'fail',
        message: 'Component failed to load'
      });
    }

    // Test 4: Check vendor dashboard
    try {
      const vendorDashboard = await import('@/components/vendor-reviews-dashboard');
      results.push({
        name: 'Vendor Reviews Dashboard Component',
        status: 'pass',
        message: 'Component loaded successfully'
      });
    } catch (error) {
      results.push({
        name: 'Vendor Reviews Dashboard Component',
        status: 'fail',
        message: 'Component failed to load'
      });
    }

    // Test 5: Check review services
    try {
      const entityReviewService = await import('@/lib/services/entityReviewService');
      results.push({
        name: 'Entity Review Service',
        status: 'pass',
        message: 'Service loaded successfully'
      });
    } catch (error) {
      results.push({
        name: 'Entity Review Service',
        status: 'fail',
        message: 'Service failed to load'
      });
    }

    // Test 6: Check GraphQL queries
    try {
      const queries = await import('@/src/graphql/queries');
      results.push({
        name: 'GraphQL Queries',
        status: 'pass',
        message: 'Queries loaded successfully'
      });
    } catch (error) {
      results.push({
        name: 'GraphQL Queries',
        status: 'fail',
        message: 'Queries failed to load'
      });
    }

    // Test 7: Check GraphQL mutations
    try {
      const mutations = await import('@/src/graphql/mutations');
      results.push({
        name: 'GraphQL Mutations',
        status: 'pass',
        message: 'Mutations loaded successfully'
      });
    } catch (error) {
      results.push({
        name: 'GraphQL Mutations',
        status: 'fail',
        message: 'Mutations failed to load'
      });
    }

    // Test 8: Check review pages
    const pages = [
      { name: 'Reviews Page', path: '/reviews' },
      { name: 'Review Selector Page', path: '/review-selector' }
    ];

    pages.forEach(page => {
      results.push({
        name: `${page.name} Route`,
        status: 'warning',
        message: 'Manual testing required',
        action: `Visit ${page.path}`
      });
    });

    setTestResults(results);
    setTesting(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pass': return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'fail': return <XCircle className="w-5 h-5 text-red-600" />;
      case 'warning': return <AlertCircle className="w-5 h-5 text-yellow-600" />;
      default: return <AlertCircle className="w-5 h-5 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      pass: 'bg-green-100 text-green-800',
      fail: 'bg-red-100 text-red-800',
      warning: 'bg-yellow-100 text-yellow-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <span>Review System Test Suite</span>
            <Badge variant="outline">Two-Tier Review System</Badge>
          </CardTitle>
          <p className="text-gray-600">
            Test the complete review system implementation including components, services, and routing.
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-4">
            <Button onClick={runTests} disabled={testing}>
              {testing ? 'Running Tests...' : 'Run All Tests'}
            </Button>
            <Button variant="outline" asChild>
              <a href="/reviews" target="_blank" rel="noopener noreferrer">
                <ExternalLink className="w-4 h-4 mr-2" />
                Test Reviews Page
              </a>
            </Button>
            <Button variant="outline" asChild>
              <a href="/review-selector" target="_blank" rel="noopener noreferrer">
                <ExternalLink className="w-4 h-4 mr-2" />
                Test Review Selector
              </a>
            </Button>
          </div>

          {testResults.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Test Results</h3>
              {testResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(result.status)}
                    <div>
                      <p className="font-medium">{result.name}</p>
                      <p className="text-sm text-gray-600">{result.message}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusBadge(result.status)}>
                      {result.status.toUpperCase()}
                    </Badge>
                    {result.action && (
                      <Button size="sm" variant="outline">
                        {result.action}
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-semibold text-blue-900 mb-2">Review System Architecture</h4>
            <div className="text-sm text-blue-800 space-y-1">
              <p>✅ <strong>Platform Reviews:</strong> Header → /reviews → Type Selection → Platform Form → Admin Dashboard</p>
              <p>✅ <strong>Product Reviews:</strong> Header → /reviews → Type Selection → /review-selector → Entity Page → Vendor Dashboard</p>
              <p>✅ <strong>Admin Access:</strong> All reviews with moderation capabilities</p>
              <p>✅ <strong>Vendor Access:</strong> Only their product/service reviews with response functionality</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
