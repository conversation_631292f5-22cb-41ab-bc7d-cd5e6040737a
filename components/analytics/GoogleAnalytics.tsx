'use client';

import Script from 'next/script';

// Google Analytics configuration
const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;

// Google Analytics component
export const GoogleAnalytics = () => {
  if (!GA_MEASUREMENT_ID) {
    return null;
  }

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      />
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${GA_MEASUREMENT_ID}', {
              page_location: window.location.href,
              page_title: document.title,
            });
          `,
        }}
      />
    </>
  );
};

// Google Tag Manager component
export const GoogleTagManager = () => {
  const GTM_ID = process.env.NEXT_PUBLIC_GTM_ID;

  if (!GTM_ID) {
    return null;
  }

  return (
    <>
      <Script
        id="google-tag-manager"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','${GTM_ID}');
          `,
        }}
      />
      <noscript>
        <iframe
          src={`https://www.googletagmanager.com/ns.html?id=${GTM_ID}`}
          height="0"
          width="0"
          style={{ display: 'none', visibility: 'hidden' }}
        />
      </noscript>
    </>
  );
};

// Event tracking functions
export const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, parameters);
  }
};

// Specific event tracking functions for wedding platform
export const trackVendorView = (vendorId: string, vendorName: string, category: string) => {
  trackEvent('vendor_view', {
    vendor_id: vendorId,
    vendor_name: vendorName,
    vendor_category: category,
  });
};

export const trackVenueView = (venueId: string, venueName: string, location: string) => {
  trackEvent('venue_view', {
    venue_id: venueId,
    venue_name: venueName,
    venue_location: location,
  });
};

export const trackProductView = (productId: string, productName: string, category: string, price?: number) => {
  trackEvent('product_view', {
    product_id: productId,
    product_name: productName,
    product_category: category,
    price: price,
  });
};

export const trackSearch = (searchTerm: string, searchType: 'vendor' | 'venue' | 'product' | 'blog') => {
  trackEvent('search', {
    search_term: searchTerm,
    search_type: searchType,
  });
};

export const trackInquiry = (type: 'vendor' | 'venue', itemId: string, itemName: string) => {
  trackEvent('inquiry_sent', {
    inquiry_type: type,
    item_id: itemId,
    item_name: itemName,
  });
};

export const trackBooking = (type: 'vendor' | 'venue', itemId: string, itemName: string, value?: number) => {
  trackEvent('booking_initiated', {
    booking_type: type,
    item_id: itemId,
    item_name: itemName,
    value: value,
  });
};

export const trackNewsletterSignup = (email: string) => {
  trackEvent('newsletter_signup', {
    email_domain: email.split('@')[1],
  });
};

export const trackBlogRead = (blogId: string, blogTitle: string, category: string) => {
  trackEvent('blog_read', {
    blog_id: blogId,
    blog_title: blogTitle,
    blog_category: category,
  });
};

// Conversion tracking
export const trackConversion = (conversionType: string, value?: number, currency = 'INR') => {
  trackEvent('conversion', {
    conversion_type: conversionType,
    value: value,
    currency: currency,
  });
};

// Enhanced ecommerce tracking
export const trackPurchase = (transactionId: string, items: any[], value: number) => {
  trackEvent('purchase', {
    transaction_id: transactionId,
    value: value,
    currency: 'INR',
    items: items,
  });
};

// User engagement tracking
export const trackEngagement = (engagementType: string, details?: Record<string, any>) => {
  trackEvent('user_engagement', {
    engagement_type: engagementType,
    ...details,
  });
};

// Declare gtag function for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}
