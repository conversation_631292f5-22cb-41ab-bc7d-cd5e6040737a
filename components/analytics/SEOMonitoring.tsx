'use client';

import { useEffect } from 'react';

// SEO monitoring and performance tracking
export const SEOMonitoring = () => {
  useEffect(() => {
    // Monitor Core Web Vitals
    if (typeof window !== 'undefined') {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS(sendToAnalytics);
        getFID(sendToAnalytics);
        getFCP(sendToAnalytics);
        getLCP(sendToAnalytics);
        getTTFB(sendToAnalytics);
      }).catch(() => {
        // Silently fail if web-vitals is not available
      });
    }

    // Monitor page load performance
    if (typeof window !== 'undefined' && window.performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        const pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
        const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart;

        // Send performance metrics
        sendToAnalytics({
          name: 'page_load_time',
          value: pageLoadTime,
          url: window.location.pathname,
        });

        sendToAnalytics({
          name: 'dom_content_loaded',
          value: domContentLoaded,
          url: window.location.pathname,
        });
      }
    }

    // Monitor SEO-related metrics
    monitorSEOMetrics();
  }, []);

  return null; // This component doesn't render anything
};

// Function to send analytics data
const sendToAnalytics = (metric: any) => {
  // Send to Google Analytics
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', metric.name, {
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      event_category: 'Web Vitals',
      event_label: metric.id,
      non_interaction: true,
    });
  }

  // You can also send to other analytics services here
  console.log('SEO Metric:', metric);
};

// Monitor SEO-specific metrics
const monitorSEOMetrics = () => {
  if (typeof window === 'undefined') return;

  // Check if page has proper meta tags
  const metaDescription = document.querySelector('meta[name="description"]');
  const metaKeywords = document.querySelector('meta[name="keywords"]');
  const canonicalLink = document.querySelector('link[rel="canonical"]');
  const ogTitle = document.querySelector('meta[property="og:title"]');
  const ogDescription = document.querySelector('meta[property="og:description"]');
  const ogImage = document.querySelector('meta[property="og:image"]');

  const seoScore = {
    hasMetaDescription: !!metaDescription,
    hasMetaKeywords: !!metaKeywords,
    hasCanonical: !!canonicalLink,
    hasOgTitle: !!ogTitle,
    hasOgDescription: !!ogDescription,
    hasOgImage: !!ogImage,
    titleLength: document.title.length,
    metaDescriptionLength: metaDescription?.getAttribute('content')?.length || 0,
  };

  // Send SEO audit data
  if (window.gtag) {
    window.gtag('event', 'seo_audit', {
      event_category: 'SEO',
      custom_parameters: seoScore,
    });
  }
};

// Schema.org validation component
export const SchemaValidation = () => {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Check for structured data
    const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
    const structuredDataCount = jsonLdScripts.length;

    // Validate JSON-LD syntax
    let validSchemas = 0;
    jsonLdScripts.forEach((script) => {
      try {
        JSON.parse(script.textContent || '');
        validSchemas++;
      } catch (error) {
        console.error('Invalid JSON-LD schema:', error);
      }
    });

    // Send structured data metrics
    if (window.gtag) {
      window.gtag('event', 'structured_data_audit', {
        event_category: 'SEO',
        structured_data_count: structuredDataCount,
        valid_schemas: validSchemas,
      });
    }
  }, []);

  return null;
};

// Search Console integration helper
export const SearchConsoleIntegration = () => {
  const SEARCH_CONSOLE_ID = process.env.NEXT_PUBLIC_SEARCH_CONSOLE_ID;

  if (!SEARCH_CONSOLE_ID) {
    return null;
  }

  return (
    <meta name="google-site-verification" content={SEARCH_CONSOLE_ID} />
  );
};

// Bing Webmaster Tools integration
export const BingWebmasterIntegration = () => {
  const BING_WEBMASTER_ID = process.env.NEXT_PUBLIC_BING_WEBMASTER_ID;

  if (!BING_WEBMASTER_ID) {
    return null;
  }

  return (
    <meta name="msvalidate.01" content={BING_WEBMASTER_ID} />
  );
};

// Facebook domain verification
export const FacebookDomainVerification = () => {
  const FACEBOOK_DOMAIN_VERIFICATION = process.env.NEXT_PUBLIC_FACEBOOK_DOMAIN_VERIFICATION;

  if (!FACEBOOK_DOMAIN_VERIFICATION) {
    return null;
  }

  return (
    <meta name="facebook-domain-verification" content={FACEBOOK_DOMAIN_VERIFICATION} />
  );
};

// Pinterest domain verification
export const PinterestDomainVerification = () => {
  const PINTEREST_DOMAIN_VERIFICATION = process.env.NEXT_PUBLIC_PINTEREST_DOMAIN_VERIFICATION;

  if (!PINTEREST_DOMAIN_VERIFICATION) {
    return null;
  }

  return (
    <meta name="p:domain_verify" content={PINTEREST_DOMAIN_VERIFICATION} />
  );
};

// Combined SEO and Analytics component
export const SEOAnalytics = () => {
  return (
    <>
      <SEOMonitoring />
      <SchemaValidation />
      <SearchConsoleIntegration />
      <BingWebmasterIntegration />
      <FacebookDomainVerification />
      <PinterestDomainVerification />
    </>
  );
};

// Hook for tracking user interactions
export const useTrackInteraction = () => {
  const trackClick = (elementName: string, elementType: string, additionalData?: Record<string, any>) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'click', {
        event_category: 'User Interaction',
        event_label: elementName,
        element_type: elementType,
        ...additionalData,
      });
    }
  };

  const trackFormSubmission = (formName: string, formType: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'form_submit', {
        event_category: 'Form Interaction',
        event_label: formName,
        form_type: formType,
      });
    }
  };

  const trackDownload = (fileName: string, fileType: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'file_download', {
        event_category: 'Downloads',
        event_label: fileName,
        file_type: fileType,
      });
    }
  };

  return {
    trackClick,
    trackFormSubmission,
    trackDownload,
  };
};

// Performance monitoring hook
export const usePerformanceMonitoring = () => {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Monitor resource loading
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming;
          
          // Track slow loading resources
          if (resourceEntry.duration > 1000) {
            if (window.gtag) {
              window.gtag('event', 'slow_resource', {
                event_category: 'Performance',
                resource_name: resourceEntry.name,
                duration: Math.round(resourceEntry.duration),
              });
            }
          }
        }
      });
    });

    observer.observe({ entryTypes: ['resource'] });

    return () => observer.disconnect();
  }, []);
};
