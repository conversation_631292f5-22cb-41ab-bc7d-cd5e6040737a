"use client"
import '../lib/disable-indexeddb'; // Disable IndexedDB early to prevent creation
import '../lib/disable-console-simple'; // Disable console logs in production
import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback, useMemo } from 'react';
import { AuthProvider } from '@/contexts/AuthContext';
import { StateProvider } from '@/contexts/StateContext';
import LanguageSelectorModal from "@/components/LanguageSelectorModal";
import { NavigationOptimizer } from '@/components/performance/NavigationOptimizer';
import { Toaster } from 'react-hot-toast';
import { DefaultSEOWrapper } from '@/components/seo/DefaultSEOWrapper';
import { saveCart, loadCart } from '@/lib/storage-utils';
import '../lib/i18n';
import '../lib/aws-config';
import '../lib/cleanup-indexeddb'; // Import to trigger automatic cleanup
import '../lib/simple-performance-fix'; // Import to trigger performance optimizations

// Cart item type
type CartItem = {
  id: number | string;
  name: string;
  price: number;
  quantity: number;
  size?: string;
  color?: string;
  image?: string;
};

type CartContextType = {
  cart: CartItem[];
  addToCart: (item: CartItem) => void;
  removeFromCart: (itemIdx: number) => void;
  updateQuantity: (itemIdx: number, quantity: number) => void;
  clearCart: () => void;
  getTotalItems: () => number;
  getTotalPrice: () => number;
};

const CartContext = createContext<CartContextType | undefined>(undefined);

export function useCart() {
  const ctx = useContext(CartContext);
  if (!ctx) throw new Error('useCart must be used within a CartContext provider');
  return ctx;
}

export function ClientRoot({ children }: { children: ReactNode }) {
  const [cart, setCart] = useState<CartItem[]>([]);

  // Load cart from storage on component mount
  useEffect(() => {
    const loadCartFromStorage = () => {
      try {
        const savedCart = loadCart();
        if (process.env.NODE_ENV === 'development') {
          console.log('Raw storage cart data:', savedCart);
        }

        if (savedCart) {
          if (Array.isArray(savedCart)) {
            setCart(savedCart);
          } else {
            console.error('Invalid cart data format:', savedCart);
            setCart([]);
          }
        } else {
          if (process.env.NODE_ENV === 'development') {
            console.log('No cart data found in storage');
          }
          setCart([]);
        }
      } catch (error) {
        console.error('Error loading cart from storage:', error);
        setCart([]);
      }
    };

    // Load immediately
    loadCartFromStorage();

    // Listen for storage events (in case cart is updated from another tab)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'thirumanam-cart') {
        if (process.env.NODE_ENV === 'development') {
          console.log('Cart updated from another tab, reloading...');
        }
        loadCartFromStorage();
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('storage', handleStorageChange);
      return () => window.removeEventListener('storage', handleStorageChange);
    }
  }, []);

  // Save cart to storage whenever cart changes (throttled for performance)
  useEffect(() => {
    if (cart.length > 0 || cart.length === 0) { // Save even empty cart to clear storage
      saveCart(cart);
      if (process.env.NODE_ENV === 'development') {
        console.log('Saved cart to storage:', cart);
      }
    }
  }, [cart]);

  const addToCart = useCallback((item: CartItem) => {
    // Only log in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Adding item to cart:', item);
    }

    // Validate item before adding
    if (!item.id || !item.name || isNaN(item.price) || isNaN(item.quantity) || item.quantity <= 0) {
      console.error('Invalid cart item:', item);
      return;
    }

    setCart((prev) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('Current cart before adding:', prev);
      }

      // If item with same id, size, color exists, increase quantity
      const idx = prev.findIndex(
        (i) => i.id === item.id && i.size === item.size && i.color === item.color
      );

      if (idx > -1) {
        const updated = [...prev];
        updated[idx].quantity += item.quantity;
        if (process.env.NODE_ENV === 'development') {
          console.log('Updated existing item:', updated[idx]);
          console.log('Updated cart:', updated);
        }
        return updated;
      }

      const newCart = [...prev, item];
      if (process.env.NODE_ENV === 'development') {
        console.log('Added new item, cart now:', newCart);
      }
      return newCart;
    });
  }, []);

  const removeFromCart = useCallback((itemIdx: number) => {
    setCart((prev) => prev.filter((_, idx) => idx !== itemIdx));
  }, []);

  const updateQuantity = useCallback((itemIdx: number, quantity: number) => {
    if (quantity <= 0) {
      removeFromCart(itemIdx);
      return;
    }
    setCart((prev) => {
      const updated = [...prev];
      updated[itemIdx].quantity = quantity;
      return updated;
    });
  }, [removeFromCart]);

  const clearCart = useCallback(() => setCart([]), []);

  // Memoize expensive calculations
  const getTotalItems = useMemo(() => cart.reduce((total, item) => total + item.quantity, 0), [cart]);

  const getTotalPrice = useMemo(() => cart.reduce((total, item) => total + (item.price * item.quantity), 0), [cart]);

  // Memoize the cart context value to prevent unnecessary re-renders
  const cartContextValue = useMemo(() => ({
    cart,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    getTotalItems,
    getTotalPrice
  }), [cart, addToCart, removeFromCart, updateQuantity, clearCart, getTotalItems, getTotalPrice]);

  // Memoize toast options to prevent recreation on each render
  const toastOptions = useMemo(() => ({
    duration: 4000,
    style: {
      background: '#363636',
      color: '#fff',
      borderRadius: '8px',
      padding: '16px',
      fontSize: '14px',
      maxWidth: '500px',
    },
    success: {
      style: {
        background: '#10B981',
      },
      iconTheme: {
        primary: '#fff',
        secondary: '#10B981',
      },
    },
    error: {
      style: {
        background: '#EF4444',
      },
      iconTheme: {
        primary: '#fff',
        secondary: '#EF4444',
      },
    },
    loading: {
      style: {
        background: '#3B82F6',
      },
    },
  }), []);

  return (
    <AuthProvider>
      <StateProvider>
        <CartContext.Provider value={cartContextValue}>
          <DefaultSEOWrapper />
          <LanguageSelectorModal />
          {children}
          <Toaster
            position="top-right"
            toastOptions={toastOptions}
          />
        </CartContext.Provider>
      </StateProvider>
    </AuthProvider>
  );
} 