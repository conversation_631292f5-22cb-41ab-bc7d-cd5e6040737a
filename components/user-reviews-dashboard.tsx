"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Star, Edit, Trash2, Calendar, MapPin, ShoppingBag, Users, Building } from "lucide-react"
// import { EntityReviewService } from '@/src/services/entityReviewService' // TODO: Create this service
import { useAuth } from '@/contexts/AuthContext'

interface UserReview {
  id: string
  entityType: string
  entityId: string
  entityName?: string
  rating: number
  serviceRating?: number
  valueRating?: number
  communicationRating?: number
  professionalismRating?: number
  title: string
  review: string
  wouldRecommend: boolean
  status: string
  helpfulCount: number
  createdAt: string
  updatedAt: string
}

export function UserReviewsDashboard() {
  const { user, isAuthenticated } = useAuth()
  const [reviews, setReviews] = useState<UserReview[]>([])
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    averageRating: 0
  })

  useEffect(() => {
    if (isAuthenticated && user) {
      loadUserReviews()
    }
  }, [isAuthenticated, user])

  const loadUserReviews = async () => {
    if (!user) return

    try {
      setLoading(true)
      const userId = user.sub || user.id
      const result = await EntityReviewService.getUserReviews(userId)
      
      if (result.success) {
        setReviews(result.data)
        calculateStats(result.data)
      }
    } catch (error) {
      console.error('Error loading user reviews:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (reviewsData: UserReview[]) => {
    const total = reviewsData.length
    const pending = reviewsData.filter(r => r.status === 'PENDING').length
    const approved = reviewsData.filter(r => r.status === 'APPROVED').length
    const rejected = reviewsData.filter(r => r.status === 'REJECTED').length
    
    const totalRating = reviewsData.reduce((sum, review) => sum + review.rating, 0)
    const averageRating = total > 0 ? totalRating / total : 0

    setStats({
      total,
      pending,
      approved,
      rejected,
      averageRating: Math.round(averageRating * 10) / 10
    })
  }

  const getEntityIcon = (entityType: string) => {
    switch (entityType) {
      case 'SHOP':
        return <ShoppingBag className="h-4 w-4" />
      case 'VENUE':
        return <Building className="h-4 w-4" />
      case 'VENDOR':
        return <Users className="h-4 w-4" />
      default:
        return <Star className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'REJECTED':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${
              i < Math.floor(rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">{rating}/5</span>
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (!isAuthenticated) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-gray-600">Please log in to view your reviews.</p>
        </CardContent>
      </Card>
    )
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <p className="text-sm text-gray-600">Total Reviews</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
            <p className="text-sm text-gray-600">Approved</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            <p className="text-sm text-gray-600">Pending</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
            <p className="text-sm text-gray-600">Rejected</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{stats.averageRating}</div>
            <p className="text-sm text-gray-600">Avg Rating</p>
          </CardContent>
        </Card>
      </div>

      {/* Reviews List */}
      <Card>
        <CardHeader>
          <CardTitle>Your Reviews ({reviews.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {reviews.length === 0 ? (
            <div className="text-center py-8">
              <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">No reviews yet</h3>
              <p className="text-gray-500">Start sharing your experiences with wedding services!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {reviews.map((review) => (
                <div key={review.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {getEntityIcon(review.entityType)}
                      <span className="font-medium">{review.entityType}</span>
                      <span className="text-gray-500">•</span>
                      <span className="text-sm text-gray-600">ID: {review.entityId}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={getStatusColor(review.status)}>
                        {review.status}
                      </Badge>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="mb-3">
                    <h4 className="font-semibold text-lg mb-1">{review.title}</h4>
                    {renderStars(review.rating)}
                  </div>

                  <p className="text-gray-700 mb-3 line-clamp-2">{review.review}</p>

                  {/* Detailed Ratings */}
                  {(review.serviceRating || review.valueRating || review.communicationRating || review.professionalismRating) && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-3 p-2 bg-gray-50 rounded">
                      {review.serviceRating && (
                        <div className="text-center">
                          <div className="text-sm font-medium">{review.serviceRating}/5</div>
                          <p className="text-xs text-gray-600">Service</p>
                        </div>
                      )}
                      {review.valueRating && (
                        <div className="text-center">
                          <div className="text-sm font-medium">{review.valueRating}/5</div>
                          <p className="text-xs text-gray-600">Value</p>
                        </div>
                      )}
                      {review.communicationRating && (
                        <div className="text-center">
                          <div className="text-sm font-medium">{review.communicationRating}/5</div>
                          <p className="text-xs text-gray-600">Communication</p>
                        </div>
                      )}
                      {review.professionalismRating && (
                        <div className="text-center">
                          <div className="text-sm font-medium">{review.professionalismRating}/5</div>
                          <p className="text-xs text-gray-600">Professional</p>
                        </div>
                      )}
                    </div>
                  )}

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {formatDate(review.createdAt)}
                      </div>
                      {review.wouldRecommend && (
                        <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
                          Recommends
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span>{review.helpfulCount || 0} helpful votes</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
