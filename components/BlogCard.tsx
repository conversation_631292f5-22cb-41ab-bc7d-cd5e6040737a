"use client"

import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Calendar, Clock, Eye, Heart, MessageCircle, Pin, Star } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { BLOG_CATEGORIES, Blog, BlogCategory } from '@/lib/services/blogService'

interface BlogCardProps {
  blog: Blog
  variant?: 'default' | 'featured' | 'compact'
  showAuthor?: boolean
}

export function BlogCard({ blog, variant = 'default', showAuthor = true }: BlogCardProps) {
  const category = BLOG_CATEGORIES[blog.category as BlogCategory]
  const publishedDate = blog.publishedAt ? new Date(blog.publishedAt).toLocaleDateString('en-US', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }) : 'Draft'

  const getReadTime = (content: string) => {
    const wordsPerMinute = 200
    const words = content.split(' ').length
    const minutes = Math.ceil(words / wordsPerMinute)
    return `${minutes} min read`
  }

  if (variant === 'compact') {
    return (
      <Card className="hover:shadow-md transition-shadow cursor-pointer">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            {blog.featuredImage && (
              <div className="relative w-16 h-16 flex-shrink-0">
                <Image
                  src={blog.featuredImage}
                  alt={blog.title}
                  fill
                  className="object-cover rounded-md"
                />
              </div>
            )}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <Badge className={`text-xs ${category?.color}`}>
                  {category?.icon} {category?.title}
                </Badge>
                {blog.isPinned && <Pin className="w-3 h-3 text-primary" />}
              </div>
              <h3 className="font-semibold text-sm line-clamp-2 mb-1">
                <Link href={`/community/forums/blog/${blog.id}`} className="hover:text-primary">
                  {blog.title}
                </Link>
              </h3>
              <div className="flex items-center gap-3 text-xs text-gray-500">
                <span>{publishedDate}</span>
                <span>{getReadTime(blog.content)}</span>
                {blog.views && (
                  <span className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    {blog.views}
                  </span>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (variant === 'featured') {
    return (
      <Card className="overflow-hidden hover:shadow-lg transition-shadow">
        <div className="grid grid-cols-1 lg:grid-cols-2">
          {blog.featuredImage && (
            <div className="relative h-64 lg:h-full">
              <Image
                src={blog.featuredImage}
                alt={blog.title}
                fill
                className="object-cover"
              />
              {blog.isFeatured && (
                <Badge className="absolute top-4 left-4 bg-primary">
                  <Star className="w-3 h-3 mr-1" />
                  Featured
                </Badge>
              )}
            </div>
          )}
          <CardContent className="p-6 flex flex-col justify-center">
            <div className="flex items-center gap-2 mb-3">
              <Badge className={category?.color}>
                {category?.icon} {category?.title}
              </Badge>
              {blog.isPinned && <Pin className="w-4 h-4 text-primary" />}
            </div>
            <h3 className="text-xl font-bold mb-3 line-clamp-2">
              <Link href={`/community/forums/blog/${blog.id}`} className="hover:text-primary">
                {blog.title}
              </Link>
            </h3>
            {blog.excerpt && (
              <p className="text-gray-600 mb-4 line-clamp-3">{blog.excerpt}</p>
            )}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <span className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {publishedDate}
                </span>
                <span className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  {getReadTime(blog.content)}
                </span>
              </div>
              <Link href={`/community/forums/blog/${blog.id}`}>
                <Button variant="outline" size="sm">
                  Read More
                </Button>
              </Link>
            </div>
          </CardContent>
        </div>
      </Card>
    )
  }

  return (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow">
      {blog.featuredImage && (
        <div className="relative h-48">
          <Image
            src={blog.featuredImage}
            alt={blog.title}
            fill
            className="object-cover"
          />
          {blog.isFeatured && (
            <Badge className="absolute top-4 left-4 bg-primary">
              <Star className="w-3 h-3 mr-1" />
              Featured
            </Badge>
          )}
        </div>
      )}
      <CardContent className="p-6">
        <div className="flex items-center gap-2 mb-3">
          <Badge className={category?.color}>
            {category?.icon} {category?.title}
          </Badge>
          {blog.isPinned && <Pin className="w-4 h-4 text-primary" />}
        </div>
        
        <h3 className="font-bold text-lg mb-2 line-clamp-2">
          <Link href={`/community/forums/blog/${blog.id}`} className="hover:text-primary">
            {blog.title}
          </Link>
        </h3>
        
        {blog.excerpt && (
          <p className="text-gray-600 text-sm mb-4 line-clamp-2">{blog.excerpt}</p>
        )}

        {showAuthor && (
          <div className="flex items-center gap-2 mb-3">
            <Avatar className="w-6 h-6">
              <AvatarFallback className="text-xs">
                {blog.authorName.charAt(0)}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm font-medium">{blog.authorName}</span>
            {blog.authorType === 'VENDOR' && (
              <Badge variant="outline" className="text-xs">Vendor</Badge>
            )}
          </div>
        )}

        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center gap-3">
            <span className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              {publishedDate}
            </span>
            <span className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              {getReadTime(blog.content)}
            </span>
          </div>
          
          <div className="flex items-center gap-3">
            {blog.views && (
              <span className="flex items-center gap-1">
                <Eye className="w-3 h-3" />
                {blog.views}
              </span>
            )}
            {blog.likes && (
              <span className="flex items-center gap-1">
                <Heart className="w-3 h-3" />
                {blog.likes}
              </span>
            )}
            {blog.comments && (
              <span className="flex items-center gap-1">
                <MessageCircle className="w-3 h-3" />
                {blog.comments}
              </span>
            )}
          </div>
        </div>

        {blog.tags && blog.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-3">
            {blog.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                #{tag}
              </Badge>
            ))}
            {blog.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{blog.tags.length - 3} more
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
} 