"use client"

import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { format } from 'date-fns'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Loader2, Calendar, User, MapPin, Clock, Users, DollarSign, Phone, Mail, MessageSquare } from 'lucide-react'
import { generateClient } from '@aws-amplify/api'
import { listBookings, updateBooking } from '@/src/graphql/queries'
import { updateBooking as updateBookingMutation } from '@/src/graphql/mutations'

const client = generateClient()

interface BookingsListProps {
  isVendor: boolean
}

export default function BookingsList({ isVendor }: BookingsListProps) {
  const { user } = useAuth()
  const [bookings, setBookings] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [selectedBooking, setSelectedBooking] = useState<any>(null)
  const [updating, setUpdating] = useState(false)
  const [updateForm, setUpdateForm] = useState({
    status: '',
    vendorNotes: '',
    estimatedCost: '',
    priority: ''
  })

  useEffect(() => {
    if (user) {
      loadBookings()
    }
  }, [user, selectedStatus, isVendor])

  const loadBookings = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('🔍 Debug - BookingsList loading:', {
        isVendor,
        userId: user?.userId,
        selectedStatus
      })

      // Build filter based on isVendor flag
      let filter: any = {}

      if (isVendor) {
        // Vendor view - show bookings where this user is the vendor
        filter.or = [
          { vendorId: { eq: user?.userId } },
          { entityId: { eq: user?.userId } }
        ]
        console.log('🔍 Debug - Using VENDOR filter:', filter)
      } else {
        // Customer view - show bookings where this user is the customer
        filter.customerId = { eq: user?.userId }
        console.log('🔍 Debug - Using CUSTOMER filter:', filter)
      }

      // Add status filter if not 'all'
      if (selectedStatus !== 'all') {
        filter.status = { eq: selectedStatus.toUpperCase() }
      }

      console.log('🔍 Debug - Final filter:', filter)

      const result = await client.graphql({
        query: listBookings,
        variables: {
          filter: filter,
          limit: 50,
          sortDirection: 'DESC'
        },
        authMode: 'userPool'
      })

      console.log('🔍 Debug - Query result:', {
        itemCount: result.data.listBookings.items.length,
        items: result.data.listBookings.items.map(item => ({
          id: item.id,
          customerId: item.customerId,
          vendorId: item.vendorId,
          entityId: item.entityId,
          entityName: item.entityName,
          status: item.status
        }))
      })

      setBookings(result.data.listBookings.items)
    } catch (err) {
      console.error('Error loading bookings:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateBooking = async () => {
    if (!selectedBooking || !isVendor) return
    
    try {
      setUpdating(true)
      
      const updateInput = {
        id: selectedBooking.id,
        status: updateForm.status || selectedBooking.status,
        vendorNotes: updateForm.vendorNotes || selectedBooking.vendorNotes,
        estimatedCost: updateForm.estimatedCost || selectedBooking.estimatedCost,
        priority: updateForm.priority || selectedBooking.priority
      }

      console.log('🔍 Debug - Updating booking:', updateInput)

      const result = await client.graphql({
        query: updateBookingMutation,
        variables: { input: updateInput },
        authMode: 'userPool'
      })

      // Update local state
      setBookings(prev => prev.map(booking => 
        booking.id === selectedBooking.id ? result.data.updateBooking : booking
      ))

      setSelectedBooking(null)
      setUpdateForm({ status: '', vendorNotes: '', estimatedCost: '', priority: '' })
      
    } catch (err) {
      console.error('Error updating booking:', err)
      alert(`Failed to update booking: ${err.message}`)
    } finally {
      setUpdating(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'confirmed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      case 'completed': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p>Loading bookings...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-12 text-center">
          <div className="text-red-600 mb-4">Error loading bookings</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={loadBookings}>Try Again</Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Status Filter */}
      <div className="flex gap-2 flex-wrap">
        {['all', 'pending', 'confirmed', 'cancelled', 'completed'].map((status) => (
          <Button
            key={status}
            variant={selectedStatus === status ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedStatus(status)}
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Button>
        ))}
      </div>

      {/* Bookings List */}
      {bookings.length === 0 ? (
        <Card>
          <CardContent className="py-12 text-center">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              No bookings found
            </h3>
            <p className="text-gray-600">
              {isVendor 
                ? "You haven't received any booking requests yet." 
                : "You haven't made any bookings yet."
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {bookings.map((booking) => (
            <Card key={booking.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{booking.entityName}</CardTitle>
                  <Badge className={getStatusColor(booking.status)}>
                    {booking.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-4 w-4 text-gray-500" />
                      <span>{isVendor ? booking.customerName : 'Your booking'}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span>{format(new Date(booking.eventDate), 'PPP')} at {booking.eventTime}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <Users className="h-4 w-4 text-gray-500" />
                      <span>{booking.guestCount} guests • {booking.eventType}</span>
                    </div>
                    {booking.budget && (
                      <div className="flex items-center gap-2 text-sm">
                        <DollarSign className="h-4 w-4 text-gray-500" />
                        <span>{booking.budget}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    {booking.specialRequests && (
                      <div className="text-sm">
                        <span className="font-medium">Special Requests:</span>
                        <p className="text-gray-600 mt-1">{booking.specialRequests}</p>
                      </div>
                    )}
                    {booking.vendorNotes && (
                      <div className="text-sm">
                        <span className="font-medium">Vendor Notes:</span>
                        <p className="text-gray-600 mt-1">{booking.vendorNotes}</p>
                      </div>
                    )}
                    {booking.estimatedCost && (
                      <div className="text-sm">
                        <span className="font-medium">Estimated Cost:</span>
                        <p className="text-gray-600 mt-1">{booking.estimatedCost}</p>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* Action Buttons */}
                <div className="flex gap-2 mt-4">
                  {isVendor && (
                    <Button
                      size="sm"
                      onClick={() => {
                        setSelectedBooking(booking)
                        setUpdateForm({
                          status: booking.status,
                          vendorNotes: booking.vendorNotes || '',
                          estimatedCost: booking.estimatedCost || '',
                          priority: booking.priority || 'MEDIUM'
                        })
                      }}
                    >
                      Update Status
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    <Phone className="h-4 w-4 mr-1" />
                    Contact
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Update Modal for Vendors */}
      {selectedBooking && isVendor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Update Booking</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Status</label>
                <select
                  value={updateForm.status}
                  onChange={(e) => setUpdateForm(prev => ({ ...prev, status: e.target.value }))}
                  className="w-full p-2 border rounded"
                >
                  <option value="PENDING">Pending</option>
                  <option value="CONFIRMED">Confirmed</option>
                  <option value="CANCELLED">Cancelled</option>
                  <option value="COMPLETED">Completed</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Vendor Notes</label>
                <textarea
                  value={updateForm.vendorNotes}
                  onChange={(e) => setUpdateForm(prev => ({ ...prev, vendorNotes: e.target.value }))}
                  className="w-full p-2 border rounded"
                  rows={3}
                  placeholder="Add notes for the customer..."
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Estimated Cost</label>
                <input
                  type="text"
                  value={updateForm.estimatedCost}
                  onChange={(e) => setUpdateForm(prev => ({ ...prev, estimatedCost: e.target.value }))}
                  className="w-full p-2 border rounded"
                  placeholder="₹50,000"
                />
              </div>
              
              <div className="flex gap-2">
                <Button onClick={handleUpdateBooking} disabled={updating}>
                  {updating ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                  Update
                </Button>
                <Button variant="outline" onClick={() => setSelectedBooking(null)}>
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
