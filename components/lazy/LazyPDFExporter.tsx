'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download, Loader2 } from 'lucide-react';
import { showToast } from '@/lib/toast';

interface PDFExporterProps {
  contentRef: React.RefObject<HTMLElement>;
  filename?: string;
  buttonText?: string;
}

export default function LazyPDFExporter({ 
  contentRef, 
  filename = 'document',
  buttonText = 'Download PDF'
}: PDFExporterProps) {
  const [isDownloading, setIsDownloading] = useState(false);

  const downloadPDF = async () => {
    if (!contentRef.current) {
      showToast.error('Content not found for PDF generation');
      return;
    }

    setIsDownloading(true);
    try {
      // Dynamic import to avoid SSR issues and reduce initial bundle size
      const [{ default: html2canvas }, { default: jsPDF }] = await Promise.all([
        import('html2canvas'),
        import('jspdf')
      ]);

      // Create a temporary container for PDF content
      const tempContainer = document.createElement('div');
      tempContainer.style.position = 'absolute';
      tempContainer.style.left = '-9999px';
      tempContainer.style.top = '0';
      tempContainer.style.width = contentRef.current.offsetWidth + 'px';
      tempContainer.style.backgroundColor = '#ffffff';
      tempContainer.style.padding = '20px';
      tempContainer.style.margin = '0 20px';

      // Clone the content and remove download buttons
      const clonedContent = contentRef.current.cloneNode(true) as HTMLElement;
      
      // Remove all download buttons from the cloned content
      const downloadButtons = clonedContent.querySelectorAll('[data-pdf-exclude]');
      downloadButtons.forEach(button => button.remove());

      tempContainer.appendChild(clonedContent);
      document.body.appendChild(tempContainer);

      const canvas = await html2canvas(tempContainer, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: tempContainer.offsetWidth,
        height: tempContainer.offsetHeight
      });

      // Clean up temporary container
      document.body.removeChild(tempContainer);

      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');

      const imgWidth = 170; // Reduced width to add margins
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 0;

      // Add the image to PDF
      pdf.addImage(imgData, 'PNG', 20, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      // Add new pages if content is longer than one page
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 20, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save(`${filename}-${new Date().toISOString().split('T')[0]}.pdf`);
      showToast.success('PDF downloaded successfully!');
    } catch (error) {
      console.error('Error generating PDF:', error);
      showToast.error('Failed to download PDF. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <Button 
      onClick={downloadPDF} 
      disabled={isDownloading}
      className="bg-primary hover:bg-primary/90 text-white"
      data-pdf-exclude
    >
      {isDownloading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Generating PDF...
        </>
      ) : (
        <>
          <Download className="mr-2 h-4 w-4" />
          {buttonText}
        </>
      )}
    </Button>
  );
}
