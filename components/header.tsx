"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { useState, useRef, useEffect } from "react"
import {
  Menu, X, ChevronLeft, ChevronRight, MapPin, Star, TrendingUp, Camera,
  Palette, Music, Utensils, Heart, Users, Calendar, Gift, Sparkles, Crown,
  Shirt, Gem, Mail, Building, ShoppingBag, Calculator, MessageSquare, Home,
  Zap, ShoppingCart, User, Search, LogOut, Settings, Target, MessageCircle,
  HelpCircle, Lightbulb
} from "@/lib/icon-imports"
import { useAuth } from '@/contexts/AuthContext'
import { CartService } from '@/lib/services/cartService'
import OptimizedImage from '@/components/OptimizedImage'
import { Fragment } from "react"
import { useSafeTranslation } from '@/hooks/use-safe-translation'
import { useStateContext } from '@/contexts/StateContext'
import { getVendorsForState, getPopularServicesForState, getCulturalSpecialtiesForState } from '@/utils/state-specific-vendors'
import AppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import MuiButton from '@mui/material/Button';
import MuiMenu from '@mui/material/Menu';
import MuiMenuItem from '@mui/material/MenuItem';


export function Header() {
  const pathname = usePathname()
  const router = useRouter();
  const { t } = useSafeTranslation();
  const { selectedState } = useStateContext();
  const [vendorAnchorEl, setVendorAnchorEl] = useState<null | HTMLElement>(null);
  const [planningOpen, setPlanningOpen] = useState(false)
  const [venuesAnchorEl, setVenuesAnchorEl] = useState<null | HTMLElement>(null);
  const [photosOpen, setPhotosOpen] = useState(false)
  const [weddingsOpen, setWeddingsOpen] = useState(false)
  const [shopOpen, setShopOpen] = useState(false)
  const [communityOpen, setCommunityOpen] = useState(false)
  const [offersOpen, setOffersOpen] = useState(false)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [shopAnchorEl, setShopAnchorEl] = useState<null | HTMLElement>(null);
  const [planningAnchorEl, setPlanningAnchorEl] = useState<null | HTMLElement>(null);
  const [communityAnchorEl, setCommunityAnchorEl] = useState<null | HTMLElement>(null);
  const [mobileVendorsOpen, setMobileVendorsOpen] = useState(false);
  const [mobileVenuesOpen, setMobileVenuesOpen] = useState(false);
  const [mobileShopOpen, setMobileShopOpen] = useState(false);
  const [mobilePlanningOpen, setMobilePlanningOpen] = useState(false);
  const [mobileCommunityOpen, setMobileCommunityOpen] = useState(false);
  const [openMenu, setOpenMenu] = useState<null | "vendors" | "venues" | "shop" | "planning" | "community">(null);
  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [menuPosition, setMenuPosition] = useState<{ left: number; width: number } | null>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const [profileDropdownOpen, setProfileDropdownOpen] = useState(false);
  const profileDropdownTimeout = useRef<NodeJS.Timeout | null>(null);
  const menuCloseTimeout = useRef<NodeJS.Timeout | null>(null);

  // Navigation behavior
  const handleMenuHover = (menu: "vendors" | "venues" | "shop" | "planning" | "community") => (event: React.MouseEvent<HTMLElement>) => {
    // Instant menu switching with no delays
    const target = event.currentTarget;
    const rect = target.getBoundingClientRect();
    const navContainer = target.closest('nav');
    const navRect = navContainer?.getBoundingClientRect();

    if (navRect) {
      setMenuPosition({
        left: rect.left - navRect.left,
        width: rect.width
      });
    }

    setOpenMenu(menu);
    setMenuAnchorEl(target);
  };

  const handleMenuItemClick = (url: string) => {
    // Close menu and navigate when clicking menu items
    setOpenMenu(null);
    setMenuAnchorEl(null);
    router.push(url);
  };

  const closeMenuImmediately = () => {
    // Immediate menu close function
    setOpenMenu(null);
    setMenuAnchorEl(null);
  };

  // Remove this useEffect that adds global event listeners for closing the menu
  // useEffect(() => {
  //   const handleClickOutside = (event: MouseEvent) => {
  //     const target = event.target as Element;
  //     const headerElement = document.querySelector('.header-container');

  //     if (headerElement && !headerElement.contains(target) && openMenu) {
  //       closeMenuImmediately();
  //     }
  //   };

  //   const handleMouseMove = (event: MouseEvent) => {
  //     const target = event.target as Element;
  //     const headerElement = document.querySelector('.header-container');

  //     // Close menu if mouse moves far from header area
  //     if (headerElement && openMenu) {
  //       const headerRect = headerElement.getBoundingClientRect();
  //       const mouseY = event.clientY;

  //       // Close if mouse is more than 50px below the header
  //       if (mouseY > headerRect.bottom + 50) {
  //         closeMenuImmediately();
  //       }
  //     }
  //   };

  //   if (openMenu) {
  //     document.addEventListener('click', handleClickOutside);
  //     document.addEventListener('mousemove', handleMouseMove);
  //   }

  //   return () => {
  //     document.removeEventListener('click', handleClickOutside);
  //     document.removeEventListener('mousemove', handleMouseMove);
  //   };
  // }, [openMenu]);

  const { isAuthenticated, signOut } = useAuth();
  const [totalCartItems, setTotalCartItems] = useState(0);

  // Load cart count
  const loadCartCount = async () => {
    if (isAuthenticated) {
      try {
        const count = await CartService.getCartCount();
        setTotalCartItems(count);
      } catch (error) {
        console.error('Error loading cart count:', error);
        setTotalCartItems(0);
      }
    } else {
      setTotalCartItems(0);
    }
  };

  useEffect(() => {
    loadCartCount();
  }, [isAuthenticated]);

  // Listen for cart update events
  useEffect(() => {
    const handleCartUpdate = () => {
      loadCartCount();
    };

    // Listen for custom cart update events
    window.addEventListener('cartUpdated', handleCartUpdate);

    return () => {
      window.removeEventListener('cartUpdated', handleCartUpdate);
    };
  }, [isAuthenticated]);

  // Get state-specific vendor data
  const stateVendorConfig = getVendorsForState(selectedState.code);
  const isStateSpecificDataAvailable = !!stateVendorConfig;

  // Tamil Nadu-specific cities and localities
  const tnCities = [
    "Chennai", "Coimbatore", "Madurai", "Trichy", "Salem", "Erode", "Tirunelveli", "Thanjavur", "Kanchipuram", "Vellore", "Tiruppur", "Dindigul", "Cuddalore", "Thoothukudi", "Nagercoil", "Karur"
  ];
  const chennaiLocalities = [
    "Mylapore", "T. Nagar", "Anna Nagar", "Velachery", "Adyar", "Tambaram", "Porur", "Ambattur", "Kodambakkam", "Perambur"
  ];

  const handleVendorMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setVendorAnchorEl(event.currentTarget);
  };
  const handleVendorMenuClose = () => {
    setVendorAnchorEl(null);
  };

  const handleVenuesMenuOpen = (event: React.MouseEvent<HTMLElement>) => setVenuesAnchorEl(event.currentTarget);
  const handleVenuesMenuClose = () => setVenuesAnchorEl(null);

  const handleShopMenuOpen = (event: React.MouseEvent<HTMLElement>) => setShopAnchorEl(event.currentTarget);
  const handleShopMenuClose = () => setShopAnchorEl(null);

  const handlePlanningMenuOpen = (event: React.MouseEvent<HTMLElement>) => setPlanningAnchorEl(event.currentTarget);
  const handlePlanningMenuClose = () => setPlanningAnchorEl(null);

  const handleCommunityMenuOpen = (event: React.MouseEvent<HTMLElement>) => setCommunityAnchorEl(event.currentTarget);
  const handleCommunityMenuClose = () => setCommunityAnchorEl(null);



  const handleLogout = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleProfileMouseEnter = () => {
    if (profileDropdownTimeout.current) {
      clearTimeout(profileDropdownTimeout.current);
      profileDropdownTimeout.current = null;
    }
    setProfileDropdownOpen(true);
  };

  const handleProfileMouseLeave = () => {
    profileDropdownTimeout.current = setTimeout(() => {
      setProfileDropdownOpen(false);
    }, 150); // Small delay to allow moving to dropdown
  };

  // Favorite state
  const [favoritesCount, setFavoritesCount] = useState(0);

  // Load favorites count
  useEffect(() => {
    if (isAuthenticated) {
      loadFavoritesCount();
    } else {
      setFavoritesCount(0);
    }
  }, [isAuthenticated]);

  const loadFavoritesCount = async () => {
    try {
      const { FavoritesService } = await import('@/lib/services/favoritesService');
      const counts = await FavoritesService.getFavoritesCount();
      setFavoritesCount(counts.total);
    } catch (error) {
      console.error('Error loading favorites count:', error);
    }
  };

  return (
    <header ref={headerRef} className="header-container border-b bg-white sticky top-0 md:top-[44px] z-50 shadow-sm">
      <AppBar
        position="static"
        color="default"
        elevation={0}
        sx={{
          borderBottom: 1,
          borderColor: 'divider',
          background: 'linear-gradient(135deg, #ffffff 0%, #fafafa 100%)',
          backdropFilter: 'blur(10px)'
        }}
      >
        <Toolbar sx={{
          minHeight: '72px',
          px: { xs: 3, sm: 4, md: 6 },
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          {/* Logo Section */}
          <div
            className="flex items-center min-w-[200px]"
            onMouseEnter={() => {
              // Close menu when hovering over logo
              if (openMenu) {
                menuCloseTimeout.current = setTimeout(() => setOpenMenu(null), 100);
              }
            }}
          >
            <Link href="/" className="flex-shrink-0 group">
              <OptimizedImage
                src="/Thirumanam360-logo.png"
                alt="Thirumanam 360 Logo"
                width={240}
                height={60}
                className="h-14 w-auto transition-transform duration-200 group-hover:scale-105"
                priority={true}
                quality={90}
                sizes="240px"
                fallbackSrc="/placeholder-logo.svg"
              />
            </Link>
          </div>

          {/* Enhanced Desktop Navigation */}
          {/* Large navigation area that includes dropdowns */}
          <div
            className="navigation-area"
            onMouseEnter={() => {
              if (menuCloseTimeout.current) clearTimeout(menuCloseTimeout.current);
            }}
            style={{ position: 'relative', display: 'flex', alignItems: 'flex-start', flex: 1 }}
          >
            <nav
              className="hidden lg:flex items-center space-x-8 relative flex-1 justify-center"
              onMouseEnter={() => {
                if (menuCloseTimeout.current) clearTimeout(menuCloseTimeout.current);
              }}
            >
              {/* Vendors Enhanced Link */}
              <div
                className={`relative cursor-pointer px-3 py-2 transition-all duration-200 ease-in-out ${pathname === "/vendors" ? 'text-[#D4AF37] font-bold' : 'text-gray-700 font-medium hover:text-[#D4AF37] hover:-translate-y-0.5'}`}
                aria-haspopup="true"
                aria-expanded={openMenu === 'vendors' ? 'true' : undefined}
                onMouseEnter={handleMenuHover('vendors')}
                onClick={() => router.push('/vendors')}
              >
                <span className="text-lg">{t('header.vendors.title', 'Vendors')}</span>
                {pathname === "/vendors" && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-4/5 h-0.5 bg-[#D4AF37] rounded-t-sm" />
                )}
              </div>
              {/* Venues Enhanced Link */}
              <div
                className={`relative cursor-pointer px-3 py-2 transition-all duration-200 ease-in-out ${pathname === "/venues" ? 'text-[#D4AF37] font-bold' : 'text-gray-700 font-medium hover:text-[#D4AF37] hover:-translate-y-0.5'}`}
                aria-haspopup="true"
                aria-expanded={openMenu === 'venues' ? 'true' : undefined}
                onMouseEnter={handleMenuHover('venues')}
                onClick={() => router.push('/venues')}
              >
                <span className="text-lg">{t('header.venues.title', 'Venues')}</span>
                {pathname === "/venues" && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-4/5 h-0.5 bg-[#D4AF37] rounded-t-sm" />
                )}
              </div>
              {/* Shop Enhanced Link */}
              <div
                className={`relative cursor-pointer px-3 py-2 transition-all duration-200 ease-in-out ${pathname === "/shop" ? 'text-[#D4AF37] font-bold' : 'text-gray-700 font-medium hover:text-[#D4AF37] hover:-translate-y-0.5'}`}
                aria-haspopup="true"
                aria-expanded={openMenu === 'shop' ? 'true' : undefined}
                onMouseEnter={handleMenuHover('shop')}
                onClick={() => router.push('/shop')}
              >
                <span className="text-lg">{t('header.shop.title', 'Shop')}</span>
                {pathname === "/shop" && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-4/5 h-0.5 bg-[#D4AF37] rounded-t-sm" />
                )}
              </div>
              {/* Planning Tools Enhanced Link */}
              <div
                className={`relative cursor-pointer px-3 py-2 transition-all duration-200 ease-in-out ${pathname?.startsWith("/planning") ? 'text-[#D4AF37] font-bold' : 'text-gray-700 font-medium hover:text-[#D4AF37] hover:-translate-y-0.5'}`}
                aria-haspopup="true"
                aria-expanded={openMenu === 'planning' ? 'true' : undefined}
                onMouseEnter={handleMenuHover('planning')}
                onClick={() => router.push('/planning/budget')}
              >
                <span className="text-lg">{t('header.planning.title', 'Planning Tools')}</span>
                {pathname?.startsWith("/planning") && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-4/5 h-0.5 bg-[#D4AF37] rounded-t-sm" />
                )}
              </div>
              {/* Community Enhanced Link */}
              <div
                className={`relative cursor-pointer px-3 py-2 transition-all duration-200 ease-in-out ${pathname?.startsWith("/community") ? 'text-[#D4AF37] font-bold' : 'text-gray-700 font-medium hover:text-[#D4AF37] hover:-translate-y-0.5'}`}
                aria-haspopup="true"
                aria-expanded={openMenu === 'community' ? 'true' : undefined}
                onMouseEnter={handleMenuHover('community')}
                onClick={() => router.push('/community/forums')}
              >
                <span className="text-lg">{t('header.community.title', 'Community')}</span>
                {pathname?.startsWith("/community") && (
                  <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-4/5 h-0.5 bg-[#D4AF37] rounded-t-sm" />
                )}
              </div>
            </nav>
            {/* Dropdowns for each section, only one open at a time */}
            {openMenu === 'vendors' && menuPosition && (
              <div
                className="absolute z-40 bg-white shadow-xl border-t border-gray-200 animate-fade-in"
                style={{
                  top: '100%',
                  left: `${menuPosition.left - 50}px`, // Offset to align better with menu item
                  width: 'fit-content',
                  minWidth: 'max(600px, 50vw)',
                  maxWidth: '95vw',
                  whiteSpace: 'nowrap'
                }}
                onMouseEnter={() => {
                  if (menuCloseTimeout.current) clearTimeout(menuCloseTimeout.current);
                }}
                onMouseLeave={() => {
                  menuCloseTimeout.current = setTimeout(() => setOpenMenu(null), 150);
                }}
              >
                <div className="flex px-6 py-4 gap-6">
                  {/* Vendor Categories */}
                  <div className="flex-shrink-0 pr-4" style={{ minWidth: '280px' }}>
                    <div className="font-bold text-lg mb-3 text-[#a31515] flex items-baseline gap-2">
                      <span>Vendor Categories</span>
                  </div>
                    <div className="grid grid-cols-2 gap-1">
                      <button onClick={() => handleMenuItemClick('/vendors?category=photo-video')} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Photo & Videographers
                      </button>
                      <button onClick={() => handleMenuItemClick('/vendors?category=catering')} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Cooks & Caterings
                      </button>
                      <button onClick={() => handleMenuItemClick('/vendors?category=makeup-mehandi')} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Makeup & Mehandi Artists
                      </button>
                      <button onClick={() => handleMenuItemClick('/vendors?category=musical-artists')} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Musical Artists
                      </button>
                      <button onClick={() => { router.push('/vendors?category=invitations'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Invitations
                      </button>
                      <button onClick={() => { router.push('/vendors?category=wedding-jewellery'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Wedding Jewellery Sets
                      </button>
                      <button onClick={() => { router.push('/vendors?category=marriage-outfits'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Marriage Outfits
                      </button>
                      <button onClick={() => { router.push('/vendors?category=astrologers'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Astrologers
                      </button>
                      <button onClick={() => { router.push('/vendors?category=dj-music'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        DJ Music
                      </button>
                      <button onClick={() => { router.push('/vendors?category=decorators'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Decorators
                      </button>
                      <button onClick={() => { router.push('/vendors?category=snacks-stall'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Snacks Stall
                      </button>
                      <button onClick={() => { router.push('/vendors?category=event-organizers'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Event Organizers
                      </button>
                      <button onClick={() => { router.push('/vendors?category=iyer-pandit'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Iyer/Pandit
                      </button>
                      <button onClick={() => { router.push('/vendors?category=return-gift'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Return Gift
                      </button>
                      <button onClick={() => { router.push('/vendors?category=flower-shops'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Flower Shops
                      </button>
                      <button onClick={() => { router.push('/vendors?category=travels'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Travels
                      </button>
                    </div>
                  </div>
                  {/* Featured Vendors */}
                  <div className="flex-shrink-0 pl-4" style={{ minWidth: '280px' }}>
                    <div className="font-bold text-lg mb-3 text-[#a31515]">Featured Vendors</div>
                    <div className="flex gap-6">
                      <div className="text-center cursor-pointer" onClick={() => handleMenuItemClick('/vendors/featured-1')}>
                        <img src="/placeholder.svg" alt="Vendor 1" className="rounded mb-2 w-20 h-16 object-cover" />
                        <div className="text-xs font-semibold">Vendor One</div>
                      </div>
                      <div className="text-center cursor-pointer" onClick={() => handleMenuItemClick('/vendors/featured-2')}>
                        <img src="/placeholder.svg" alt="Vendor 2" className="rounded mb-2 w-20 h-16 object-cover" />
                        <div className="text-xs font-semibold">Vendor Two</div>
                      </div>
                      <div className="text-center cursor-pointer" onClick={() => handleMenuItemClick('/vendors/featured-3')}>
                        <img src="/placeholder.svg" alt="Vendor 3" className="rounded mb-2 w-20 h-16 object-cover" />
                        <div className="text-xs font-semibold">Vendor Three</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {openMenu === 'venues' && menuPosition && (
              <div
                className="absolute z-40 bg-white shadow-xl border-t border-gray-200 animate-fade-in"
                style={{
                  top: '100%',
                  left: `${menuPosition.left - 50}px`, // Offset to align better with menu item
                  width: 'fit-content',
                  minWidth: 'max(500px, 40vw)',
                  maxWidth: '95vw',
                  whiteSpace: 'nowrap'
                }}
                onMouseEnter={() => {
                  if (menuCloseTimeout.current) clearTimeout(menuCloseTimeout.current);
                }}
                onMouseLeave={() => {
                  menuCloseTimeout.current = setTimeout(() => setOpenMenu(null), 150);
                }}
              >
                <div className="flex gap-6 px-6 py-4">
                  {/* Venue Categories */}
                  <div className="flex-shrink-0 pr-4" style={{ minWidth: '200px' }}>
                    <div className="font-bold text-lg mb-3 text-[#a31515] flex items-center gap-2">
                      <span>Venue Categories</span>
                    </div>
                    <div className="flex flex-col gap-1">
                      <button onClick={() => { router.push('/venues?category=marriage-mahal'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Marriage Mahal
                      </button>
                      <button onClick={() => { router.push('/venues?category=kalyana-mandapam'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Kalyana Mandapam
                      </button>
                      <button onClick={() => { router.push('/venues?category=banquet-hall'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Banquet Hall
                      </button>
                      <button onClick={() => { router.push('/venues?category=resort'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Resort Venues
                      </button>
                      <button onClick={() => { router.push('/venues?category=temple-wedding'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Temple Wedding
                      </button>
                      <button onClick={() => { router.push('/venues?category=destination-wedding'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Destination
                      </button>
                      <button onClick={() => { router.push('/venues?category=outdoor-venues'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Outdoor Venues
                      </button>
                      <button onClick={() => { router.push('/venues?category=heritage-venues'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Heritage Venues
                      </button>
                    </div>
                  </div>
                  {/* Featured Venues */}
                  <div className="flex-shrink-0" style={{ minWidth: '280px' }}>
                    <div className="font-bold text-lg mb-4 text-[#a31515]">Featured Venues</div>
                    <div className="flex gap-6">
                      <div className="text-center cursor-pointer" onClick={() => { router.push('/venues/featured-1'); closeMenuImmediately(); }}>
                        <img src="/placeholder.svg" alt="Venue 1" className="rounded mb-2 w-20 h-16 object-cover" />
                        <div className="text-xs font-semibold">Venue One</div>
                      </div>
                      <div className="text-center cursor-pointer" onClick={() => { router.push('/venues/featured-2'); closeMenuImmediately(); }}>
                        <img src="/placeholder.svg" alt="Venue 2" className="rounded mb-2 w-20 h-16 object-cover" />
                        <div className="text-xs font-semibold">Venue Two</div>
                      </div>
                      <div className="text-center cursor-pointer" onClick={() => { router.push('/venues/featured-3'); closeMenuImmediately(); }}>
                        <img src="/placeholder.svg" alt="Venue 3" className="rounded mb-2 w-20 h-16 object-cover" />
                        <div className="text-xs font-semibold">Venue Three</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {openMenu === 'shop' && menuPosition && (
              <div
                className="absolute z-40 bg-white shadow-xl border-t border-gray-200 animate-fade-in"
                style={{
                  top: '100%',
                  left: `${menuPosition.left - 50}px`, // Offset to align better with menu item
                  width: 'fit-content',
                  minWidth: '500px', // Reduced from 700px
                  maxWidth: '80vw', // Reduced from 95vw
                  whiteSpace: 'nowrap'
                }}
                onMouseEnter={() => {
                  if (menuCloseTimeout.current) clearTimeout(menuCloseTimeout.current);
                }}
                onMouseLeave={() => {
                  menuCloseTimeout.current = setTimeout(() => setOpenMenu(null), 150);
                }}
              >
                <div className="flex gap-4 px-4 py-4"> {/* Reduced gap from 6 to 4, padding from 6 to 4 */}
                  {/* Shop Categories */}
                  <div className="flex-shrink-0" style={{ minWidth: '150px' }}> {/* Reduced from 180px to 150px */}
                    <div className="font-bold text-lg mb-3 text-[#a31515] flex items-center gap-2">
                      <span>Shop Categories</span>
                    </div>
                    <div className="flex flex-col gap-1">
                      <button onClick={() => { router.push('/shop?category=gifts'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Wedding Gifts
                      </button>
                      <button onClick={() => { router.push('/shop?category=favors'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Wedding Favors
                      </button>
                      <button onClick={() => { router.push('/shop?category=decor'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Home Decor
                      </button>
                      <button onClick={() => { router.push('/shop?category=accessories'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Accessories
                      </button>
                    </div>
                  </div>
                  {/* Special Offers */}
                  <div className="flex-shrink-0" style={{ minWidth: '150px' }}> {/* Reduced from 180px to 150px */}
                    <div className="font-bold text-lg mb-3 text-[#a31515] flex items-center gap-2">
                      <span>Special Offers</span>
                    </div>
                    <div className="flex flex-col gap-2">
                      <button onClick={() => { router.push('/offers?type=shop'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Shop Offers
                      </button>
                      <button onClick={() => { router.push('/offers?type=vendors'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Vendor Offers
                      </button>
                      <button onClick={() => { router.push('/offers?type=venues'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Venue Offers
                      </button>
                      <button onClick={() => { router.push('/offers'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm font-bold">
                        → All Offers
                      </button>
                    </div>
                  </div>
                  {/* Featured Products */}
                  <div className="flex-shrink-0 flex flex-col" style={{ minWidth: '180px' }}> {/* Reduced from 220px to 180px */}
                    <div className="font-bold text-lg mb-3 text-[#a31515] flex items-center gap-2">
                      <span>Featured Products</span>
                    </div>
                    <div className="flex gap-4">
                      <div className="text-center cursor-pointer" onClick={() => { router.push('/shop/featured-1'); closeMenuImmediately(); }}>
                        <img src="/placeholder.svg" alt="Product 1" className="rounded mb-2 w-16 h-16 object-cover" />
                        <div className="text-xs font-semibold">Product One</div>
                      </div>
                      <div className="text-center cursor-pointer" onClick={() => { router.push('/shop/featured-2'); closeMenuImmediately(); }}>
                        <img src="/placeholder.svg" alt="Product 2" className="rounded mb-2 w-16 h-16 object-cover" />
                        <div className="text-xs font-semibold">Product Two</div>
                      </div>
                      <div className="text-center cursor-pointer" onClick={() => { router.push('/shop/featured-3'); closeMenuImmediately(); }}>
                        <img src="/placeholder.svg" alt="Product 3" className="rounded mb-2 w-16 h-16 object-cover" />
                        <div className="text-xs font-semibold">Product Three</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {openMenu === 'planning' && menuPosition && (
              <div
                className="absolute z-40 bg-white shadow-xl border-t border-gray-200 animate-fade-in"
                style={{
                  top: '100%',
                  left: `${menuPosition.left - 50}px`, // Offset to align better with menu item
                  width: 'fit-content',
                  minWidth: '280px', // Reduced from 400px to fit content better
                  maxWidth: '80vw',
                  whiteSpace: 'nowrap'
                }}
                onMouseEnter={() => {
                  if (menuCloseTimeout.current) clearTimeout(menuCloseTimeout.current);
                }}
                onMouseLeave={() => {
                  menuCloseTimeout.current = setTimeout(() => setOpenMenu(null), 150);
                }}
              >
                <div className="flex px-4 py-4 gap-4"> {/* Reduced padding and gap */}
                  {/* Planning Tools */}
                  <div className="flex-shrink-0" style={{ minWidth: '200px' }}> {/* Reduced from 250px to 200px, removed pr-4 */}
                    <div className="font-bold text-lg mb-3 text-[#a31515] flex items-center gap-2">
                      <span>Planning Tools</span>
                    </div>
                    <div className="grid grid-cols-2 gap-1">
                      <button onClick={() => { router.push('/planning/budget'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Budget Calculator
                      </button>
                      <button onClick={() => { router.push('/planning/checklist'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Checklist
                      </button>
                      <button onClick={() => { router.push('/planning/timeline'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Timeline
                      </button>
                      <button onClick={() => { router.push('/planning/guest-list'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Guest List
                      </button>
                      <button onClick={() => { router.push('/planning/ideas'); closeMenuImmediately(); }} className="flex items-center gap-2 px-2 py-1 rounded hover:bg-gray-100 text-sm">
                        Wedding Ideas
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
            {openMenu === 'community' && menuPosition && (
              <div
                className="absolute z-40 bg-white shadow-xl border-t border-gray-200 animate-fade-in"
                style={{
                  top: '100%',
                  left: `${menuPosition.left - 50}px`, // Offset to align better with menu item
                  width: 'fit-content',
                  minWidth: '320px', // Reduced from 400px to fit actual content
                  maxWidth: '80vw',
                  whiteSpace: 'nowrap'
                }}
                onMouseEnter={() => {
                  if (menuCloseTimeout.current) clearTimeout(menuCloseTimeout.current);
                }}
                onMouseLeave={() => {
                  menuCloseTimeout.current = setTimeout(() => setOpenMenu(null), 150);
                }}
              >
                <div className="flex px-4 py-4 gap-4"> {/* Reduced padding and gap */}
                  {/* Forums & Groups */}
                  <div className="flex-shrink-0" style={{ minWidth: '140px' }}> {/* Changed from w-1/4 to fixed width */}
                    <div className="font-bold text-lg mb-3 text-[#a31515]">Forums</div>
                    <div className="flex flex-col gap-1">
                      <button className="block w-full text-left px-2 py-1 rounded hover:bg-gray-100 text-sm font-normal">
                        Forums
                      </button>
                      <button className="block w-full text-left px-2 py-1 rounded hover:bg-gray-100 text-sm font-normal">
                        Groups
                      </button>
                      <button className="block w-full text-left px-2 py-1 rounded hover:bg-gray-100 text-sm font-normal">
                        Q&A
                      </button>
                    </div>
                  </div>
                  {/* Trending Topics */}
                  <div className="flex-shrink-0" style={{ minWidth: '140px' }}> {/* Changed from w-1/4 to fixed width, removed empty columns */}
                    <div className="font-bold text-lg mb-3 text-[#a31515]">Trending Topics</div>
                    <div className="flex flex-col gap-1">
                      <button className="block w-full text-left px-2 py-1 rounded hover:bg-gray-100 text-sm font-normal">
                        Wedding Planning
                      </button>
                      <button className="block w-full text-left px-2 py-1 rounded hover:bg-gray-100 text-sm font-normal">
                        Budget Tips
                      </button>
                      <button className="block w-full text-left px-2 py-1 rounded hover:bg-gray-100 text-sm font-normal">
                        Vendor Reviews
                      </button>
                      <button className="block w-full text-left px-2 py-1 rounded hover:bg-gray-100 text-sm font-normal">
                        DIY Decor
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
          {/* Enhanced Desktop Actions */}
          <div
            className="hidden lg:flex items-center gap-4 min-w-[200px] justify-end"
            onMouseEnter={() => {
              // Close menu when hovering over right-side actions
              if (openMenu) {
                menuCloseTimeout.current = setTimeout(() => setOpenMenu(null), 100);
              }
            }}
          >
            {/* Favorites Heart */}
            <Link href="/favorites" legacyBehavior>
              <a
                className="relative h-10 w-10 rounded-full hover:bg-gray-100 transition-colors flex items-center justify-center"
                aria-label="Favorites"
              >
                <Heart className={`h-5 w-5 ${favoritesCount > 0 ? 'text-red-500 fill-red-500' : 'text-gray-500'}`} />
                {favoritesCount > 0 && (
                  <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {favoritesCount > 99 ? '99+' : favoritesCount}
                  </span>
                )}
              </a>
            </Link>
            {/* Shopping Cart */}
            <Link href="/cart" className="relative flex items-center justify-center h-10 w-10 rounded-full hover:bg-gray-100 transition-colors z-50">
              <ShoppingCart className="h-5 w-5 text-gray-600" />
              {totalCartItems > 0 && (
                <span className="absolute -top-1 -right-1 h-5 w-5 bg-primary text-white text-xs rounded-full flex items-center justify-center font-medium z-60">
                  {totalCartItems > 99 ? '99+' : totalCartItems}
                </span>
              )}
            </Link>

            {isAuthenticated ? (
              <div 
                className="relative"
                onMouseEnter={handleProfileMouseEnter}
                onMouseLeave={handleProfileMouseLeave}
              >
                <button className="flex items-center justify-center h-10 w-10 rounded-full bg-primary/10 hover:bg-primary/20 transition-all duration-200 border-2 border-primary/20 hover:border-primary/40">
                  <User className="h-5 w-5 text-primary" />
                </button>
                
                {/* Profile Dropdown */}
                {profileDropdownOpen && (
                  <div 
                    className="fixed top-12 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 animate-in slide-in-from-top-2 duration-200"
                    onMouseEnter={handleProfileMouseEnter}
                    onMouseLeave={handleProfileMouseLeave}
                    style={{
                      right: '0px'
                    }}
                  >
                    {/* Invisible bridge to prevent gap */}
                    <div className="absolute -top-3 left-0 right-0 h-3 bg-transparent"></div>
                    
                    <Link href="/dashboard" className="flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors">
                      <User className="h-4 w-4" />
                      Dashboard
                    </Link>
                    
                    <button
                      onClick={handleLogout}
                      className="flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 w-full text-left transition-colors"
                    >
                      <LogOut className="h-4 w-4" />
                      Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center gap-3">
                <Link href="/login" className="flex items-center justify-center h-10 px-6 text-sm font-medium bg-primary hover:bg-primary/90 text-white transition-all duration-200 rounded-full shadow-lg hover:shadow-xl">
                  Login
                </Link>
                {/* <Link href="/register" className="flex items-center justify-center h-10 px-6 text-sm font-medium bg-primary hover:bg-primary/90 text-white transition-all duration-200 rounded-full shadow-lg hover:shadow-xl">
                  Sign Up
                </Link> */}
              </div>
            )}
          </div>
          {/* Enhanced Mobile Menu Button */}
          <div className="lg:hidden flex items-center gap-2">
            {/* Mobile Auth Quick Access */}
            {!isAuthenticated && (
              <Link href="/login" className="flex items-center justify-center h-8 px-3 text-xs bg-primary hover:bg-primary/90 text-white rounded-full shadow-md hover:shadow-lg transition-all duration-200">
                Login
              </Link>
            )}

            <button
              className="p-2 rounded-full hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary/20 transition-colors"
              aria-label="Open menu"
              onClick={() => setMobileMenuOpen(true)}
            >
              <Menu className="h-7 w-7 text-gray-700" />
            </button>
          </div>
        </Toolbar>
      </AppBar>
      {/* Mobile Menu Drawer */}
      {mobileMenuOpen && (
        <div className="fixed inset-0 z-[100] bg-black/40 flex">
          <div className="relative bg-white w-4/5 max-w-xs h-full shadow-xl flex flex-col animate-slide-in-left">
            <button
              className="absolute top-4 right-4 p-2 rounded focus:outline-none focus:ring-2 focus:ring-primary"
              aria-label="Close menu"
              onClick={() => setMobileMenuOpen(false)}
            >
              <X className="h-6 w-6 text-gray-700" />
            </button>


            <nav className="flex-1 flex flex-col gap-1 mt-4 px-6 overflow-y-auto">
              {!mobileVendorsOpen ? (
                <>
                  <div className="relative">
                    <button
                      className="py-3 px-4 w-full text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3 justify-between"
                      onClick={() => setMobileVendorsOpen(true)}
                      aria-expanded={mobileVendorsOpen}
                      aria-controls="mobile-vendors-list"
                    >
                      <span className="flex items-center gap-3">
                        <Users className="h-5 w-5" /> Vendors
                      </span>
                      <ChevronRight className="h-5 w-5 text-gray-400" />
                    </button>
                  </div>
                </>
              ) : (
                <div id="mobile-vendors-list" className="bg-white rounded-lg shadow-lg p-2 border border-gray-200">
                  <button className="flex items-center gap-2 mb-2 text-sm text-gray-500 hover:text-primary py-2 px-3 rounded hover:bg-gray-50 w-full" onClick={() => setMobileVendorsOpen(false)}>
                    <ChevronLeft className="h-4 w-4" /> Back to Menu
                  </button>
                  <div className="flex flex-col gap-1 mb-4">
                    <Link href="/vendors?category=photo-video" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Camera className="h-5 w-5" /> Photo & Videographers</Link>
                    <Link href="/vendors?category=catering" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Utensils className="h-5 w-5" /> Cooks & Caterings</Link>
                    <Link href="/vendors?category=makeup-mehandi" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Sparkles className="h-5 w-5" /> Makeup & Mehandi Artists</Link>
                    <Link href="/vendors?category=musical-artists" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Music className="h-5 w-5" /> Musical Artists</Link>
                    <Link href="/vendors?category=invitations" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Mail className="h-5 w-5" /> Invitations</Link>
                    <Link href="/vendors?category=wedding-jewellery" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Gem className="h-5 w-5" /> Wedding Jewellery Sets</Link>
                    <Link href="/vendors?category=marriage-outfits" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Shirt className="h-5 w-5" /> Marriage Outfits</Link>
                    <Link href="/vendors?category=astrologers" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Crown className="h-5 w-5" /> Astrologers</Link>
                    <Link href="/vendors?category=dj-music" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Zap className="h-5 w-5" /> DJ Music</Link>
                    <Link href="/vendors?category=decorators" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Palette className="h-5 w-5" /> Decorators</Link>
                    <Link href="/vendors?category=snacks-stall" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Gift className="h-5 w-5" /> Snacks Stall</Link>
                    <Link href="/vendors?category=event-organizers" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Calendar className="h-5 w-5" /> Event Organizers</Link>
                    <Link href="/vendors?category=iyer-pandit" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Users className="h-5 w-5" /> Iyer/Pandit</Link>
                    <Link href="/vendors?category=return-gift" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Gift className="h-5 w-5" /> Return Gift</Link>
                    <Link href="/vendors?category=flower-shops" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><Sparkles className="h-5 w-5" /> Flower Shops</Link>
                    <Link href="/vendors?category=travels" className="flex items-center gap-3 py-2 px-3 rounded hover:bg-primary/10 text-gray-800" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); }}><MapPin className="h-5 w-5" /> Travels</Link>
                  </div>
                  <div className="mt-4">
                    <div className="font-bold text-base mb-2 text-[#a31515]">Featured Vendors</div>
                    <div className="flex gap-4 justify-center">
                      <div className="text-center cursor-pointer" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); router.push('/vendors/featured-1'); }}>
                        <img src="/placeholder.svg" alt="Vendor 1" className="rounded mb-1 w-16 h-12 object-cover" />
                        <div className="text-xs font-semibold">Vendor One</div>
                      </div>
                      <div className="text-center cursor-pointer" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); router.push('/vendors/featured-2'); }}>
                        <img src="/placeholder.svg" alt="Vendor 2" className="rounded mb-1 w-16 h-12 object-cover" />
                        <div className="text-xs font-semibold">Vendor Two</div>
                      </div>
                      <div className="text-center cursor-pointer" onClick={() => { setMobileVendorsOpen(false); setMobileMenuOpen(false); router.push('/vendors/featured-3'); }}>
                        <img src="/placeholder.svg" alt="Vendor 3" className="rounded mb-1 w-16 h-12 object-cover" />
                        <div className="text-xs font-semibold">Vendor Three</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {!mobileVendorsOpen && (
                <>
                  <Link href="/venues" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3" onClick={() => setMobileMenuOpen(false)}>
                    <Building className="h-5 w-5" />
                    Venues
                  </Link>
                  <Link href="/shop" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3" onClick={() => setMobileMenuOpen(false)}>
                    <ShoppingBag className="h-5 w-5" />
                    Shop
                  </Link>
                  <Link href="/cart" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center justify-between" onClick={() => setMobileMenuOpen(false)}>
                    <div className="flex items-center gap-3">
                      <ShoppingCart className="h-5 w-5" />
                      <span>Cart</span>
                    </div>
                    {totalCartItems > 0 && (
                      <span className="bg-primary text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-medium">
                        {totalCartItems > 99 ? '99+' : totalCartItems}
                      </span>
                    )}
                  </Link>
                  <Link href="/planning/budget" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3" onClick={() => setMobileMenuOpen(false)}>
                    <Calculator className="h-5 w-5" />
                    Planning Tools
                  </Link>
                  <Link href="/community/forums" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3" onClick={() => setMobileMenuOpen(false)}>
                    <MessageSquare className="h-5 w-5" />
                    Community
                  </Link>
                  <Link href="/contact" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3" onClick={() => setMobileMenuOpen(false)}>
                    <Mail className="h-5 w-5" />
                    Contact Us
                  </Link>
                  <Link href="/help" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3" onClick={() => setMobileMenuOpen(false)}>
                    <HelpCircle className="h-5 w-5" />
                    Help Center
                  </Link>
                  <Link href="/reviews" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3" onClick={() => setMobileMenuOpen(false)}>
                    <Star className="h-5 w-5" />
                    Reviews
                  </Link>
                </>
              )}
              <Link href="/venues" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3" onClick={() => setMobileMenuOpen(false)}>
                <Building className="h-5 w-5" />
                Venues
              </Link>
              <Link href="/shop" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3" onClick={() => setMobileMenuOpen(false)}>
                <ShoppingBag className="h-5 w-5" />
                Shop
              </Link>
              <Link href="/cart" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center justify-between" onClick={() => setMobileMenuOpen(false)}>
                <div className="flex items-center gap-3">
                  <ShoppingCart className="h-5 w-5" />
                  <span>Cart</span>
                </div>
                {totalCartItems > 0 && (
                  <span className="bg-primary text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-medium">
                    {totalCartItems > 99 ? '99+' : totalCartItems}
                  </span>
                )}
              </Link>
              <Link href="/planning/budget" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3" onClick={() => setMobileMenuOpen(false)}>
                <Calculator className="h-5 w-5" />
                Planning Tools
              </Link>
              <Link href="/community/forums" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3" onClick={() => setMobileMenuOpen(false)}>
                <MessageSquare className="h-5 w-5" />
                Community
              </Link>
              <Link href="/contact" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3" onClick={() => setMobileMenuOpen(false)}>
                <Mail className="h-5 w-5" />
                Contact Us
              </Link>
              <Link href="/help" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3" onClick={() => setMobileMenuOpen(false)}>
                <HelpCircle className="h-5 w-5" />
                Help Center
              </Link>
              <Link href="/reviews" className="py-3 px-4 text-base font-medium text-gray-800 hover:text-primary hover:bg-primary/5 rounded-lg transition-colors flex items-center gap-3" onClick={() => setMobileMenuOpen(false)}>
                <Star className="h-5 w-5" />
                Reviews
              </Link>
            </nav>
            <div className="px-6 pb-8 flex flex-col gap-3">
              {isAuthenticated ? (
                <>
                  <Link href="/dashboard" onClick={() => setMobileMenuOpen(false)} className="w-full flex items-center justify-center py-2 px-4 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                    Dashboard
                  </Link>
                  <button
                    onClick={() => { handleLogout(); setMobileMenuOpen(false); }}
                    className="w-full flex items-center justify-center py-2 px-4 bg-red-50 hover:bg-red-100 text-red-600 border border-red-200 rounded-md transition-colors"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Logout
                  </button>
                </>
              ) : (
                <Link href="/login" onClick={() => setMobileMenuOpen(false)} className="w-full flex items-center justify-center py-2 px-4 bg-primary hover:bg-primary/90 text-white shadow-md hover:shadow-lg rounded-md transition-colors">
                  Login
                </Link>
              )}
            </div>
          </div>
          {/* Overlay click closes menu */}
          <div className="flex-1" onClick={() => setMobileMenuOpen(false)} />
        </div>
      )}
    </header>
  )
}

