'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Star, MessageSquare, TrendingUp, Users, Filter, Calendar, ThumbsUp, Check, X, Clock, Edit, RefreshCw } from 'lucide-react';
import { getCurrentUser } from 'aws-amplify/auth';
import VendorReviewService, { type VendorReviewData, type VendorReviewStats } from '@/lib/services/vendorReviewService';

interface VendorReviewsDashboardProps {
  className?: string;
}

export default function VendorReviewsDashboard({ className }: VendorReviewsDashboardProps) {
  const [reviews, setReviews] = useState<VendorReviewData[]>([]);
  const [stats, setStats] = useState<VendorReviewStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [responseText, setResponseText] = useState<{ [key: string]: string }>({});
  const [submittingResponse, setSubmittingResponse] = useState<{ [key: string]: boolean }>({});
  const [editingReview, setEditingReview] = useState<VendorReviewData | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [updating, setUpdating] = useState<string[]>([]);

  // Filters
  const [statusFilter, setStatusFilter] = useState('ALL');
  const [entityTypeFilter, setEntityTypeFilter] = useState('ALL');
  const [sortBy, setSortBy] = useState('newest');

  useEffect(() => {
    loadVendorReviews();
  }, [statusFilter, entityTypeFilter, sortBy]);

  const loadVendorReviews = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get current user
      const user = await getCurrentUser();
      const userId = user.sub || user.userId || user.username;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Fetch vendor reviews
      const result = await VendorReviewService.getVendorReviews(userId, {
        statusFilter,
        entityTypeFilter,
        sortBy,
        limit: 50
      });

      if (result.success) {
        setReviews(result.data || []);
        setStats(result.stats || null);
      } else {
        setError(result.error || 'Failed to load reviews');
      }

    } catch (err) {
      console.error('Error loading vendor reviews:', err);
      setError(err instanceof Error ? err.message : 'Failed to load reviews');
    } finally {
      setLoading(false);
    }
  };

  const handleAddResponse = async (reviewId: string) => {
    const response = responseText[reviewId];
    if (!response || !response.trim()) return;

    try {
      setSubmittingResponse(prev => ({ ...prev, [reviewId]: true }));

      const result = await VendorReviewService.addVendorResponse(reviewId, response);

      if (result.success) {
        // Update the review in the local state
        setReviews(prev => prev.map(review =>
          review.id === reviewId
            ? { ...review, vendorResponse: response, responseDate: new Date().toISOString(), hasResponse: true }
            : review
        ));

        // Clear the response text
        setResponseText(prev => ({ ...prev, [reviewId]: '' }));

        // Reload stats
        loadVendorReviews();
      } else {
        setError(result.error || 'Failed to add response');
      }

    } catch (err) {
      console.error('Error adding response:', err);
      setError(err instanceof Error ? err.message : 'Failed to add response');
    } finally {
      setSubmittingResponse(prev => ({ ...prev, [reviewId]: false }));
    }
  };

  const handleStatusUpdate = async (reviewId: string, newStatus: string) => {
    try {
      setUpdating(prev => [...prev, reviewId]);

      const result = await VendorReviewService.updateReviewStatus(reviewId, newStatus);

      if (result.success) {
        // Update the review in the local state
        setReviews(prev => prev.map(review =>
          review.id === reviewId
            ? { ...review, status: newStatus, updatedAt: new Date().toISOString() }
            : review
        ));

        // Reload to get fresh stats
        loadVendorReviews();
      } else {
        setError(result.error || 'Failed to update review status');
      }

    } catch (err) {
      console.error('Error updating review status:', err);
      setError(err instanceof Error ? err.message : 'Failed to update review status');
    } finally {
      setUpdating(prev => prev.filter(id => id !== reviewId));
    }
  };

  const handleEditReview = (review: VendorReviewData) => {
    setEditingReview(review);
    setEditDialogOpen(true);
  };

  const handleSaveEdit = async (updatedReview: VendorReviewData) => {
    try {
      setUpdating(prev => [...prev, updatedReview.id]);

      const result = await VendorReviewService.updateReview(updatedReview.id, {
        title: updatedReview.title,
        review: updatedReview.review,
        rating: updatedReview.rating,
        serviceRating: updatedReview.serviceRating,
        valueRating: updatedReview.valueRating,
        communicationRating: updatedReview.communicationRating,
        professionalismRating: updatedReview.professionalismRating,
        wouldRecommend: updatedReview.wouldRecommend
      });

      if (result.success) {
        setReviews(prev => prev.map(review =>
          review.id === updatedReview.id ? { ...review, ...updatedReview, updatedAt: new Date().toISOString() } : review
        ));
        setEditDialogOpen(false);
        setEditingReview(null);
        
        // Reload to get fresh stats
        loadVendorReviews();
      } else {
        setError(result.error || 'Failed to update review');
      }

    } catch (err) {
      console.error('Error updating review:', err);
      setError(err instanceof Error ? err.message : 'Failed to update review');
    } finally {
      setUpdating(prev => prev.filter(id => id !== updatedReview.id));
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'APPROVED': return 'bg-green-100 text-green-800';
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'REJECTED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'}`}
      />
    ));
  };

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-48 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={loadVendorReviews}>Try Again</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <h1 className="text-2xl sm:text-3xl font-semibold text-gray-900 tracking-tight">Vendor Reviews Dashboard</h1>
        <Button onClick={loadVendorReviews} variant="ghost" size="icon" className="rounded-full">
          <RefreshCw className="w-6 h-6" />
        </Button>
      </div>

      {/* Stats Row */}
      {stats && (
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-2">
          <div className="bg-white rounded-lg shadow-sm flex flex-col items-center py-4">
            <MessageSquare className="h-6 w-6 text-blue-500 mb-1" />
            <span className="text-lg font-semibold text-gray-900">{stats.totalReviews}</span>
            <span className="text-xs text-gray-500 mt-0.5">Total Reviews</span>
          </div>
          <div className="bg-white rounded-lg shadow-sm flex flex-col items-center py-4">
            <Star className="h-6 w-6 text-yellow-500 mb-1" />
            <span className="text-lg font-semibold text-yellow-600">{stats.averageRating.toFixed(1)}</span>
            <div className="flex mt-1">{renderStars(Math.round(stats.averageRating))}</div>
            <span className="text-xs text-gray-500 mt-0.5">Average Rating</span>
          </div>
          <div className="bg-white rounded-lg shadow-sm flex flex-col items-center py-4">
            <TrendingUp className="h-6 w-6 text-green-500 mb-1" />
            <span className="text-lg font-semibold text-green-600">{stats.responseRate.toFixed(0)}%</span>
            <span className="text-xs text-gray-500 mt-0.5">Response Rate</span>
          </div>
          <div className="bg-white rounded-lg shadow-sm flex flex-col items-center py-4">
            <ThumbsUp className="h-6 w-6 text-purple-500 mb-1" />
            <span className="text-lg font-semibold text-purple-600">{stats.recommendationRate.toFixed(0)}%</span>
            <span className="text-xs text-gray-500 mt-0.5">Recommendation</span>
          </div>
        </div>
      )}

      {/* Filters Bar */}
      <div className="flex flex-col sm:flex-row gap-3 mb-6 bg-gray-50 rounded-lg px-4 py-3 items-center shadow-sm">
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">Filters:</span>
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-40 bg-white rounded-md">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">All Status</SelectItem>
            <SelectItem value="PENDING">Pending</SelectItem>
            <SelectItem value="APPROVED">Approved</SelectItem>
            <SelectItem value="REJECTED">Rejected</SelectItem>
          </SelectContent>
        </Select>
        <Select value={entityTypeFilter} onValueChange={setEntityTypeFilter}>
          <SelectTrigger className="w-full sm:w-40 bg-white rounded-md">
            <SelectValue placeholder="Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">All Types</SelectItem>
            <SelectItem value="VENDOR">Vendors</SelectItem>
            <SelectItem value="VENUE">Venues</SelectItem>
            <SelectItem value="SHOP">Products</SelectItem>
          </SelectContent>
        </Select>
        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger className="w-full sm:w-40 bg-white rounded-md">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="newest">Newest First</SelectItem>
            <SelectItem value="oldest">Oldest First</SelectItem>
            <SelectItem value="highest-rating">Highest Rating</SelectItem>
            <SelectItem value="lowest-rating">Lowest Rating</SelectItem>
            <SelectItem value="most-helpful">Most Helpful</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Reviews List */}
      <div className="space-y-4">
        {reviews.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm p-8 text-center">
            <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Reviews Found</h3>
            <p className="text-gray-500">
              {statusFilter !== 'ALL' || entityTypeFilter !== 'ALL'
                ? 'No reviews match your current filters. Try adjusting the filters above.'
                : 'You haven\'t received any reviews yet. Reviews will appear here once customers start reviewing your services.'
              }
            </p>
          </div>
        ) : (
          reviews.map((review) => (
            <div key={review.id} className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6 flex flex-col gap-3">
              {/* Review Header */}
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-2">
                <div className="flex items-center gap-2 flex-wrap">
                  <span className="font-semibold text-base text-gray-900">{review.title}</span>
                  <Badge className={getStatusBadgeColor(review.status)}>{review.status}</Badge>
                  <Badge variant="outline">{review.entityType}</Badge>
                </div>
                <div className="flex items-center space-x-1">
                  {renderStars(review.rating)}
                  <span className="ml-1 text-sm font-medium text-gray-700">{review.rating}/5</span>
                </div>
              </div>
              <div className="flex flex-wrap gap-4 text-sm text-gray-600 mb-1">
                <span>By {review.name}</span>
                <span>•</span>
                <span>{review.entityName}</span>
                <span>•</span>
                <span>{formatDate(review.createdAt)}</span>
              </div>
              {/* Review Content */}
              <div className="space-y-2">
                <p className="text-gray-800">{review.review}</p>
                {review.wouldRecommend && (
                  <div className="flex items-center space-x-1 text-green-600">
                    <ThumbsUp className="h-4 w-4" />
                    <span className="text-sm">Would recommend</span>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-between pt-4 border-t">
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleEditReview(review)}
                    disabled={updating.includes(review.id)}
                    className="text-blue-600 border-blue-600 hover:bg-blue-50"
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  {review.status !== 'APPROVED' && (
                    <Button
                      size="sm"
                      onClick={() => handleStatusUpdate(review.id, 'APPROVED')}
                      disabled={updating.includes(review.id)}
                      className="bg-green-600 hover:bg-green-700 text-white"
                    >
                      {updating.includes(review.id) ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Check className="h-4 w-4" />
                      )}
                      <span className="ml-1">Approve</span>
                    </Button>
                  )}
                  {review.status !== 'REJECTED' && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleStatusUpdate(review.id, 'REJECTED')}
                      disabled={updating.includes(review.id)}
                      className="text-red-600 border-red-600 hover:bg-red-50"
                    >
                      {updating.includes(review.id) ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <X className="h-4 w-4" />
                      )}
                      <span className="ml-1">Reject</span>
                    </Button>
                  )}
                  {review.status !== 'PENDING' && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleStatusUpdate(review.id, 'PENDING')}
                      disabled={updating.includes(review.id)}
                      className="text-yellow-600 border-yellow-600 hover:bg-yellow-50"
                    >
                      {updating.includes(review.id) ? (
                        <RefreshCw className="h-4 w-4 animate-spin" />
                      ) : (
                        <Clock className="h-4 w-4" />
                      )}
                      <span className="ml-1">Pending</span>
                    </Button>
                  )}
                </div>
              </div>

              {/* Vendor Response Section */}
              {review.hasResponse ? (
                <div className="bg-blue-50 border border-blue-100 rounded-md p-4 mt-2">
                  <div className="flex items-center space-x-2 mb-2">
                    <MessageSquare className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-800">Your Response</span>
                    <span className="text-xs text-blue-600">{formatDate(review.responseDate || '')}</span>
                  </div>
                  <p className="text-blue-800">{review.vendorResponse}</p>
                </div>
              ) : (
                <div className="border-t pt-4 mt-2">
                  <div className="space-y-3">
                    <label className="text-sm font-medium text-gray-700">Add Your Response</label>
                    <Textarea
                      placeholder="Write a professional response to this review..."
                      value={responseText[review.id] || ''}
                      onChange={(e) => setResponseText(prev => ({
                        ...prev,
                        [review.id]: e.target.value
                      }))}
                      className="min-h-[80px] bg-white rounded-md"
                    />
                    <Button
                      onClick={() => handleAddResponse(review.id)}
                      disabled={!responseText[review.id]?.trim() || submittingResponse[review.id]}
                      size="sm"
                      className="rounded-full"
                    >
                      {submittingResponse[review.id] ? 'Submitting...' : 'Submit Response'}
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Edit Review Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Review</DialogTitle>
          </DialogHeader>
          {editingReview && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-title">Review Title</Label>
                  <Input
                    id="edit-title"
                    value={editingReview.title}
                    onChange={(e) => setEditingReview({...editingReview, title: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-rating">Overall Rating</Label>
                  <Select
                    value={editingReview.rating.toString()}
                    onValueChange={(value) => setEditingReview({...editingReview, rating: parseInt(value)})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 Star</SelectItem>
                      <SelectItem value="2">2 Stars</SelectItem>
                      <SelectItem value="3">3 Stars</SelectItem>
                      <SelectItem value="4">4 Stars</SelectItem>
                      <SelectItem value="5">5 Stars</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="edit-review">Review Content</Label>
                <Textarea
                  id="edit-review"
                  value={editingReview.review}
                  onChange={(e) => setEditingReview({...editingReview, review: e.target.value})}
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="edit-service">Service Rating</Label>
                  <Select
                    value={editingReview.serviceRating?.toString() || ""}
                    onValueChange={(value) => setEditingReview({...editingReview, serviceRating: value ? parseInt(value) : undefined})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1</SelectItem>
                      <SelectItem value="2">2</SelectItem>
                      <SelectItem value="3">3</SelectItem>
                      <SelectItem value="4">4</SelectItem>
                      <SelectItem value="5">5</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-value">Value Rating</Label>
                  <Select
                    value={editingReview.valueRating?.toString() || ""}
                    onValueChange={(value) => setEditingReview({...editingReview, valueRating: value ? parseInt(value) : undefined})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1</SelectItem>
                      <SelectItem value="2">2</SelectItem>
                      <SelectItem value="3">3</SelectItem>
                      <SelectItem value="4">4</SelectItem>
                      <SelectItem value="5">5</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-communication">Communication</Label>
                  <Select
                    value={editingReview.communicationRating?.toString() || ""}
                    onValueChange={(value) => setEditingReview({...editingReview, communicationRating: value ? parseInt(value) : undefined})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1</SelectItem>
                      <SelectItem value="2">2</SelectItem>
                      <SelectItem value="3">3</SelectItem>
                      <SelectItem value="4">4</SelectItem>
                      <SelectItem value="5">5</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-professionalism">Professionalism</Label>
                  <Select
                    value={editingReview.professionalismRating?.toString() || ""}
                    onValueChange={(value) => setEditingReview({...editingReview, professionalismRating: value ? parseInt(value) : undefined})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1</SelectItem>
                      <SelectItem value="2">2</SelectItem>
                      <SelectItem value="3">3</SelectItem>
                      <SelectItem value="4">4</SelectItem>
                      <SelectItem value="5">5</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={editingReview.wouldRecommend}
                  onCheckedChange={(checked) => setEditingReview({...editingReview, wouldRecommend: !!checked})}
                />
                <Label>Would recommend</Label>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => handleSaveEdit(editingReview)}>
                  Save Changes
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
