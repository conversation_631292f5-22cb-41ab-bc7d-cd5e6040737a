"use client"

import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

export default function LanguageSelectorModal() {
  const { i18n } = useTranslation();
  const [show, setShow] = useState(false);
  const [hydrated, setHydrated] = useState(false);

  useEffect(() => {
    setHydrated(true);
    const lang = typeof window !== 'undefined' && localStorage.getItem('selectedLanguage');
    if (!lang) setShow(true);
  }, []);

  const selectLanguage = async (lang: string) => {
    try {
      // Delay to ensure i18n is ready
      setTimeout(() => {
        if (typeof i18n.changeLanguage === 'function') {
          i18n.changeLanguage(lang);
        } else if (typeof window !== 'undefined' && (window as any).i18next && typeof (window as any).i18next.changeLanguage === 'function') {
          (window as any).i18next.changeLanguage(lang);
        }
      }, 100);
    } catch (e) {
      // fallback: dynamic import
      const i18next = (await import('i18next')).default;
      if (typeof i18next.changeLanguage === 'function') {
        i18next.changeLanguage(lang);
      }
    }
    localStorage.setItem('selectedLanguage', lang);
    setShow(false);
  };

  if (!hydrated || !show) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-xs w-full text-center">
        <h2 className="text-xl font-bold mb-4">Select Language / மொழியைத் தேர்ந்தெடுக்கவும்</h2>
        <div className="flex flex-col gap-4">
          <button
            className="py-2 px-4 rounded bg-primary text-primary-foreground font-semibold hover:bg-primary/90"
            onClick={() => selectLanguage('en')}
          >
            English
          </button>
          <button
            className="py-2 px-4 rounded bg-primary text-primary-foreground font-semibold hover:bg-primary/90"
            onClick={() => selectLanguage('ta')}
          >
            தமிழ்
          </button>
        </div>
      </div>
    </div>
  );
} 