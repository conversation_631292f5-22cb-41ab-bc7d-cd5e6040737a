"use client"

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Star, ThumbsUp, Calendar, MapPin, Verified, MessageSquare } from "lucide-react"
import EntityReviewService from '@/lib/services/entityReviewService'
import { useAuth } from '@/contexts/AuthContext'
import { showToast, toastMessages } from '@/lib/toast'
import { EntityReviewForm } from './entity-review-form'

interface EntityReviewsProps {
  entityType: 'SHOP' | 'VENUE' | 'VENDOR'
  entityId: string
  entityName: string
}

interface Review {
  id: string
  userId: string
  name: string
  location: string
  rating: number
  serviceRating?: number
  valueRating?: number
  communicationRating?: number
  professionalismRating?: number
  title: string
  review: string
  wouldRecommend: boolean
  verified: boolean
  helpfulCount: number
  images?: string[]
  createdAt: string
  weddingDate: string
}

interface ReviewStats {
  totalReviews: number
  averageRating: number
  averageServiceRating: number
  averageValueRating: number
  averageCommunicationRating: number
  averageProfessionalismRating: number
  recommendationRate: number
  ratingDistribution: { [key: number]: number }
}

export function EntityReviews({ entityType, entityId, entityName }: EntityReviewsProps) {
  const { isAuthenticated, user } = useAuth()
  const [reviews, setReviews] = useState<Review[]>([])
  const [stats, setStats] = useState<ReviewStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [showReviewForm, setShowReviewForm] = useState(false)
  const [activeTab, setActiveTab] = useState('reviews')
  const [userHasReviewed, setUserHasReviewed] = useState(false)
  const [userReview, setUserReview] = useState<Review | null>(null)

  useEffect(() => {
    loadReviews()
    loadStats()
    if (isAuthenticated && user) {
      checkUserReview()
    }
  }, [entityType, entityId, isAuthenticated, user])

  const loadReviews = async () => {
    try {
      setLoading(true)
      const result = await EntityReviewService.getEntityReviews(entityId, {
        filter: {
          entityType: { eq: entityType }
        }
      })
      setReviews(result.reviews)
    } catch (error) {
      console.error('Error loading reviews:', error)
      setReviews([])
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const result = await EntityReviewService.getEntityReviewStats(entityId)
      setStats(result)
    } catch (error) {
      console.error('Error loading review stats:', error)
      setStats({
        averageRating: 0,
        totalReviews: 0,
        averageServiceRating: 0,
        averageValueRating: 0,
        averageCommunicationRating: 0,
        averageProfessionalismRating: 0,
        recommendationRate: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
      })
    }
  }

  const checkUserReview = async () => {
    if (!user) return

    try {
      // Debug: Log user object structure
      console.log('User object in checkUserReview:', {
        user,
        userKeys: Object.keys(user),
        userType: user.constructor?.name,
        userId: user.userId,
        sub: user.sub,
        id: user.id,
        username: user.username
      });

      // TODO: Implement EntityReviewService
      // const result = await EntityReviewService.getUserEntityReview(user, entityType, entityId)
      // if (result.success) {
      //   setUserHasReviewed(result.hasReviewed)
      //   setUserReview(result.data)
      // }
      setUserHasReviewed(false) // Temporary default
      setUserReview(null) // Temporary default
    } catch (error) {
      console.error('Error checking user review:', error)
    }
  }

  const handleReviewSubmitted = () => {
    setShowReviewForm(false)
    loadReviews()
    loadStats()
    if (isAuthenticated && user) {
      checkUserReview()
    }
    setActiveTab('reviews')
  }

  const handleMarkHelpful = async (reviewId: string) => {
    if (!user) {
      showToast.error(toastMessages.auth.authRequired)
      return
    }

    try {
      // TODO: Implement EntityReviewService
      // const result = await EntityReviewService.markReviewHelpful(reviewId, user)
      const result = { success: true } // Temporary mock
      if (result.success) {
        // Update the review in the local state
        setReviews(prev => prev.map(review =>
          review.id === reviewId
            ? { ...review, helpfulCount: (review.helpfulCount || 0) + 1 }
            : review
        ))
      } else {
        showToast.error(result.error || 'Failed to mark review as helpful')
      }
    } catch (error) {
      console.error('Error marking review as helpful:', error)
      showToast.error('Failed to mark review as helpful')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const renderStars = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: 'h-3 w-3',
      md: 'h-4 w-4',
      lg: 'h-5 w-5'
    }
    
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`${sizeClasses[size]} ${
              i < Math.floor(rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
            }`}
          />
        ))}
      </div>
    )
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Review Summary */}
      {stats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Customer Reviews</span>
              {isAuthenticated && !userHasReviewed && (
                <Button
                  onClick={() => setShowReviewForm(true)}
                  className="bg-primary hover:bg-primary/90"
                >
                  Write a Review
                </Button>
              )}
              {isAuthenticated && userHasReviewed && (
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary" className="text-green-700 bg-green-100">
                    You've reviewed this
                  </Badge>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowReviewForm(true)}
                  >
                    Edit Review
                  </Button>
                </div>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Overall Rating */}
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-4xl font-bold text-primary mb-2">
                    {stats.averageRating.toFixed(1)}
                  </div>
                  {renderStars(stats.averageRating, 'lg')}
                  <p className="text-gray-600 mt-2">
                    Based on {stats.totalReviews} review{stats.totalReviews !== 1 ? 's' : ''}
                  </p>
                </div>
                
                {stats.recommendationRate > 0 && (
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {stats.recommendationRate}%
                    </div>
                    <p className="text-sm text-green-700">would recommend</p>
                  </div>
                )}
              </div>

              {/* Rating Distribution */}
              <div className="space-y-3">
                {[5, 4, 3, 2, 1].map((rating) => (
                  <div key={rating} className="flex items-center space-x-3">
                    <span className="text-sm font-medium w-8">{rating}</span>
                    {renderStars(rating, 'sm')}
                    <Progress 
                      value={(stats.ratingDistribution[rating] / stats.totalReviews) * 100} 
                      className="flex-1 h-2"
                    />
                    <span className="text-sm text-gray-600 w-8">
                      {stats.ratingDistribution[rating]}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* Detailed Ratings */}
            {(stats.averageServiceRating > 0 || stats.averageValueRating > 0) && (
              <div className="mt-6 pt-6 border-t">
                <h4 className="font-semibold mb-4">Detailed Ratings</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {stats.averageServiceRating > 0 && (
                    <div className="text-center">
                      <div className="font-semibold text-lg">{stats.averageServiceRating.toFixed(1)}</div>
                      {renderStars(stats.averageServiceRating, 'sm')}
                      <p className="text-xs text-gray-600 mt-1">Service Quality</p>
                    </div>
                  )}
                  {stats.averageValueRating > 0 && (
                    <div className="text-center">
                      <div className="font-semibold text-lg">{stats.averageValueRating.toFixed(1)}</div>
                      {renderStars(stats.averageValueRating, 'sm')}
                      <p className="text-xs text-gray-600 mt-1">Value for Money</p>
                    </div>
                  )}
                  {stats.averageCommunicationRating > 0 && (
                    <div className="text-center">
                      <div className="font-semibold text-lg">{stats.averageCommunicationRating.toFixed(1)}</div>
                      {renderStars(stats.averageCommunicationRating, 'sm')}
                      <p className="text-xs text-gray-600 mt-1">Communication</p>
                    </div>
                  )}
                  {stats.averageProfessionalismRating > 0 && (
                    <div className="text-center">
                      <div className="font-semibold text-lg">{stats.averageProfessionalismRating.toFixed(1)}</div>
                      {renderStars(stats.averageProfessionalismRating, 'sm')}
                      <p className="text-xs text-gray-600 mt-1">Professionalism</p>
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Review Form Modal */}
      {showReviewForm && (
        <EntityReviewForm
          entityType={entityType}
          entityId={entityId}
          entityName={entityName}
          onClose={() => setShowReviewForm(false)}
          onSubmitted={handleReviewSubmitted}
        />
      )}

      {/* Reviews List */}
      <Card>
        <CardHeader>
          <CardTitle>Reviews ({reviews.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {reviews.length === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-600 mb-2">No reviews yet</h3>
              <p className="text-gray-500 mb-4">Be the first to share your experience!</p>
              {isAuthenticated && (
                <Button onClick={() => setShowReviewForm(true)}>
                  Write the First Review
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-6">
              {reviews.map((review) => (
                <div key={review.id} className="border-b pb-6 last:border-b-0">
                  <div className="flex items-start space-x-4">
                    <Avatar>
                      <AvatarFallback>{review.name.charAt(0).toUpperCase()}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-semibold">{review.name}</h4>
                          {review.verified && (
                            <Badge className="bg-green-600 text-xs">
                              <Verified className="h-3 w-3 mr-1" />
                              Verified
                            </Badge>
                          )}
                        </div>
                        <span className="text-sm text-gray-500">
                          {formatDate(review.createdAt)}
                        </span>
                      </div>
                      
                      <div className="flex items-center space-x-4 mb-2">
                        {renderStars(review.rating)}
                        <span className="font-medium">{review.rating}/5</span>
                        {review.location && (
                          <div className="flex items-center text-sm text-gray-600">
                            <MapPin className="h-3 w-3 mr-1" />
                            {review.location}
                          </div>
                        )}
                      </div>

                      <h5 className="font-semibold mb-2">{review.title}</h5>
                      <p className="text-gray-700 mb-3">{review.review}</p>

                      {/* Detailed Ratings */}
                      {(review.serviceRating || review.valueRating || review.communicationRating || review.professionalismRating) && (
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mb-3 p-3 bg-gray-50 rounded-lg">
                          {review.serviceRating && (
                            <div className="text-center">
                              <div className="text-sm font-medium">{review.serviceRating}/5</div>
                              <p className="text-xs text-gray-600">Service</p>
                            </div>
                          )}
                          {review.valueRating && (
                            <div className="text-center">
                              <div className="text-sm font-medium">{review.valueRating}/5</div>
                              <p className="text-xs text-gray-600">Value</p>
                            </div>
                          )}
                          {review.communicationRating && (
                            <div className="text-center">
                              <div className="text-sm font-medium">{review.communicationRating}/5</div>
                              <p className="text-xs text-gray-600">Communication</p>
                            </div>
                          )}
                          {review.professionalismRating && (
                            <div className="text-center">
                              <div className="text-sm font-medium">{review.professionalismRating}/5</div>
                              <p className="text-xs text-gray-600">Professional</p>
                            </div>
                          )}
                        </div>
                      )}

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          {review.wouldRecommend && (
                            <Badge variant="outline" className="text-green-600 border-green-600">
                              Recommends
                            </Badge>
                          )}
                          {review.weddingDate && (
                            <div className="flex items-center text-sm text-gray-600">
                              <Calendar className="h-3 w-3 mr-1" />
                              Wedding: {formatDate(review.weddingDate)}
                            </div>
                          )}
                        </div>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMarkHelpful(review.id)}
                          className="text-gray-600 hover:text-primary"
                        >
                          <ThumbsUp className="h-4 w-4 mr-1" />
                          Helpful ({review.helpfulCount || 0})
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
