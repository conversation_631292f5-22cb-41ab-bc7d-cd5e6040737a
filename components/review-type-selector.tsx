'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, Building2, ShoppingBag, MapPin, MessageSquare, ArrowRight } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface ReviewTypeSelectorProps {
  onTypeSelected: (type: 'platform' | 'product', data?: any) => void;
  className?: string;
}

export default function ReviewTypeSelector({ onTypeSelected, className }: ReviewTypeSelectorProps) {
  const router = useRouter();
  const [selectedType, setSelectedType] = useState<'platform' | 'product' | null>(null);

  const handlePlatformReview = () => {
    setSelectedType('platform');
    onTypeSelected('platform');
  };

  const handleProductReview = () => {
    setSelectedType('product');
    // Redirect to browse products/services to select what to review
    router.push('/review-selector');
  };

  return (
    <div className={`max-w-4xl mx-auto space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold text-gray-900">Write a Review</h1>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Help other couples by sharing your experience. Choose the type of review you'd like to write.
        </p>
      </div>

      {/* Review Type Cards */}
      <div className="grid md:grid-cols-2 gap-6">
        {/* Platform Review Card */}
        <Card className="cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-primary/50">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
              <Star className="w-8 h-8 text-blue-600" />
            </div>
            <CardTitle className="text-xl">Platform Review</CardTitle>
            <Badge variant="outline" className="w-fit mx-auto">Goes to Admin</Badge>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600 text-center">
              Share your overall experience with Thirumanam360 platform, customer service, and wedding planning journey.
            </p>
            
            <div className="space-y-2">
              <h4 className="font-semibold text-sm text-gray-800">Review about:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Platform usability and features</li>
                <li>• Customer support experience</li>
                <li>• Overall wedding planning journey</li>
                <li>• Website functionality and design</li>
              </ul>
            </div>

            <div className="bg-blue-50 p-3 rounded-lg">
              <p className="text-xs text-blue-800">
                <strong>Note:</strong> Platform reviews are moderated by our admin team and published on the main reviews page.
              </p>
            </div>

            <Button 
              onClick={handlePlatformReview}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              Write Platform Review
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </CardContent>
        </Card>

        {/* Product/Service Review Card */}
        <Card className="cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-primary/50">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <div className="flex space-x-1">
                <ShoppingBag className="w-4 h-4 text-green-600" />
                <Building2 className="w-4 h-4 text-green-600" />
                <MapPin className="w-4 h-4 text-green-600" />
              </div>
            </div>
            <CardTitle className="text-xl">Product/Service Review</CardTitle>
            <Badge variant="outline" className="w-fit mx-auto">Goes to Vendor</Badge>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-600 text-center">
              Review specific products, vendors, or venues you've purchased or booked through our platform.
            </p>
            
            <div className="space-y-2">
              <h4 className="font-semibold text-sm text-gray-800">Review about:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Shop products (jewelry, decorations, etc.)</li>
                <li>• Vendor services (photography, catering, etc.)</li>
                <li>• Venue experiences and facilities</li>
                <li>• Service quality and value for money</li>
              </ul>
            </div>

            <div className="bg-green-50 p-3 rounded-lg">
              <p className="text-xs text-green-800">
                <strong>Note:</strong> Product/service reviews go directly to the vendor's dashboard and are published on their pages.
              </p>
            </div>

            <Button 
              onClick={handleProductReview}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              Select Product/Service
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Help Section */}
      <Card className="bg-gray-50">
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <MessageSquare className="w-6 h-6 text-gray-500 mt-1" />
            <div>
              <h3 className="font-semibold text-gray-900 mb-2">Need Help Choosing?</h3>
              <div className="text-sm text-gray-600 space-y-2">
                <p>
                  <strong>Choose Platform Review if:</strong> You want to review your overall experience with Thirumanam360, 
                  our customer service, website features, or general wedding planning support.
                </p>
                <p>
                  <strong>Choose Product/Service Review if:</strong> You want to review a specific vendor, venue, or product 
                  you purchased/booked through our platform.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
