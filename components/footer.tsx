"use client"

import { But<PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { useSafeTranslation } from '@/hooks/use-safe-translation'

export function Footer() {
  const { t } = useSafeTranslation()
  return (
    <footer className="bg-gray-900 text-white py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-xl font-bold mb-4">{t('footer.companyName', 'Thirumanam 360')}</h3>
            <p className="text-gray-400 mb-4">{t('footer.companyDescription', 'Your one-stop destination for all wedding planning needs.')}</p>
            <div className="flex space-x-4">
              <Link href="https://facebook.com/thirumanam360" target="_blank" rel="noopener noreferrer">
                <Button size="sm" variant="outline" className="text-white border-gray-600 bg-transparent hover:bg-white hover:text-gray-900">
                  {t('footer.socialMedia.facebook', 'Facebook')}
                </Button>
              </Link>
              <Link href="https://instagram.com/thirumanam360" target="_blank" rel="noopener noreferrer">
                <Button size="sm" variant="outline" className="text-white border-gray-600 bg-transparent hover:bg-white hover:text-gray-900">
                  {t('footer.socialMedia.instagram', 'Instagram')}
                </Button>
              </Link>
              <Link href="https://youtube.com/@thirumanam360" target="_blank" rel="noopener noreferrer">
                <Button size="sm" variant="outline" className="text-white border-gray-600 bg-transparent hover:bg-white hover:text-gray-900">
                  {t('footer.socialMedia.youtube', 'YouTube')}
                </Button>
              </Link>
            </div>
          </div>
          <div>
            <h4 className="font-semibold mb-4">{t('footer.weddingServices.title', 'Wedding Services')}</h4>
            <ul className="space-y-2 text-gray-400">
              <li>
                <Link href="/venues" className="hover:text-white">
                  {t('footer.weddingServices.venues', 'Venues')}
                </Link>
              </li>
              <li>
                <Link href="/vendors?category=photography" className="hover:text-white">
                  {t('footer.weddingServices.photography', 'Photography')}
                </Link>
              </li>
              <li>
                <Link href="/vendors?category=catering" className="hover:text-white">
                  {t('footer.weddingServices.catering', 'Catering')}
                </Link>
              </li>
              <li>
                <Link href="/vendors?category=decoration" className="hover:text-white">
                  {t('footer.weddingServices.decoration', 'Decoration')}
                </Link>
              </li>
              <li>
                <Link href="/vendors?category=music" className="hover:text-white">
                  {t('footer.weddingServices.music', 'Music & Entertainment')}
                </Link>
              </li>
              <li>
                <Link href="/vendors?category=travels" className="hover:text-white">
                  {t('footer.weddingServices.travels', 'Travels')}
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-4">{t('footer.planningTools.title', 'Planning Tools')}</h4>
            <ul className="space-y-2 text-gray-400">
              <li>
                <Link href="/planning/budget" className="hover:text-white">
                  {t('footer.planningTools.budgetCalculator', 'Budget Calculator')}
                </Link>
              </li>
              <li>
                <Link href="/planning/guest-list" className="hover:text-white">
                  {t('footer.planningTools.guestList', 'Guest List')}
                </Link>
              </li>
              <li>
                <Link href="/planning/checklist" className="hover:text-white">
                  {t('footer.planningTools.checklist', 'Checklist')}
                </Link>
              </li>
              <li>
                <Link href="/planning/timeline" className="hover:text-white">
                  {t('footer.planningTools.timeline', 'Timeline')}
                </Link>
              </li>
              <li>
                <Link href="/shop" className="hover:text-white">
                  {t('footer.planningTools.shopping', 'Wedding Shopping')}
                </Link>
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-4">{t('footer.support.title', 'Support')}</h4>
            <ul className="space-y-2 text-gray-400">
              <li>
                <Link href="/help" className="hover:text-white">
                  {t('footer.support.helpCenter', 'Help Center')}
                </Link>
              </li>
              <li>
                <Link href="/help/faq" className="hover:text-white">
                  {t('footer.support.faq', 'FAQ')}
                </Link>
              </li>
              <li>
                <Link href="/contact" className="hover:text-white">
                  {t('footer.support.contactUs', 'Contact Us')}
                </Link>
              </li>
              <li>
                <Link href="/reviews" className="hover:text-white">
                  {t('footer.support.reviews', 'Reviews')}
                </Link>
              </li>
              <li>
                <Link href="/terms" className="hover:text-white">
                  {t('footer.support.termsAndConditions', 'Terms & Conditions')}
                </Link>
              </li>
              <li>
                <Link href="/privacy" className="hover:text-white">
                  {t('footer.support.privacyAndPolicy', 'Privacy & Policy')}
                </Link>
              </li>
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
          <p>{t('footer.copyright', '© 2025 Thirumanam 360. All rights reserved.')}</p>
        </div>
      </div>
    </footer>
  )
}
