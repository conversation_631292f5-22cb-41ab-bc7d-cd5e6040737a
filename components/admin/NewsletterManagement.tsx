'use client';

import React, { useState, useEffect } from 'react';
import { 
  Mail, 
  Users, 
  TrendingUp, 
  Download, 
  Search, 
  Filter,
  MoreHorizontal,
  Eye,
  Trash2,
  UserX
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  newsletterService,
  NewsletterSubscriptionResponse,
  NewsletterStats
} from '@/lib/services/newsletterService';
import NewsletterCampaignManager from './NewsletterCampaignManager';

export default function NewsletterManagement() {
  const [subscriptions, setSubscriptions] = useState<NewsletterSubscriptionResponse[]>([]);
  const [stats, setStats] = useState<NewsletterStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [nextToken, setNextToken] = useState<string | undefined>();

  const itemsPerPage = 50;

  useEffect(() => {
    loadData();
  }, [currentPage, statusFilter]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load stats
      const statsData = await newsletterService.getStats();
      setStats(statsData);

      // Load subscriptions with filters
      const filter = statusFilter !== 'all' ? { status: { eq: statusFilter.toUpperCase() } } : undefined;
      const subscriptionsData = await newsletterService.getAllSubscriptions(
        itemsPerPage,
        currentPage === 1 ? undefined : nextToken,
        filter
      );

      setSubscriptions(subscriptionsData.items);
      setNextToken(subscriptionsData.nextToken);
    } catch (err: any) {
      setError(err.message || 'Failed to load newsletter data');
    } finally {
      setLoading(false);
    }
  };

  const handleUnsubscribe = async (email: string) => {
    try {
      await newsletterService.unsubscribe(email);
      await loadData(); // Reload data
    } catch (err: any) {
      setError(err.message || 'Failed to unsubscribe user');
    }
  };

  const exportSubscriptions = () => {
    const csvContent = [
      ['Email', 'Name', 'Phone', 'City', 'Status', 'Source', 'Subscribed Date'].join(','),
      ...filteredSubscriptions.map(sub => [
        sub.email,
        `${sub.firstName || ''} ${sub.lastName || ''}`.trim(),
        sub.phone || '',
        sub.city || '',
        sub.status,
        sub.source,
        new Date(sub.subscribedAt).toLocaleDateString()
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `newsletter-subscriptions-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const filteredSubscriptions = subscriptions.filter(sub => {
    const matchesSearch = searchTerm === '' || 
      sub.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      `${sub.firstName || ''} ${sub.lastName || ''}`.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    const variants: { [key: string]: 'default' | 'secondary' | 'destructive' } = {
      ACTIVE: 'default',
      UNSUBSCRIBED: 'secondary',
      BOUNCED: 'destructive',
      COMPLAINED: 'destructive',
      PENDING_CONFIRMATION: 'secondary'
    };
    
    return (
      <Badge variant={variants[status] || 'secondary'}>
        {status.toLowerCase().replace('_', ' ')}
      </Badge>
    );
  };

  if (loading && !stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading newsletter data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Newsletter Management</h1>
          <p className="text-gray-600 mt-1">Manage newsletter subscriptions, analytics, and campaigns</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => window.location.reload()}>
            <TrendingUp className="w-4 h-4 mr-2" />
            Refresh Data
          </Button>
          <Button onClick={exportSubscriptions} className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export CSV
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="subscribers" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="subscribers">Subscribers</TabsTrigger>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="subscribers" className="space-y-6">
          {/* Stats Cards */}
          {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Subscribers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalSubscribers}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Subscribers</CardTitle>
              <Mail className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.activeSubscribers}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Recent Subscriptions</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.recentSubscriptions}</div>
              <p className="text-xs text-muted-foreground">Last 30 days</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Unsubscribed</CardTitle>
              <UserX className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{stats.unsubscribedCount}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search by email or name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="unsubscribed">Unsubscribed</option>
          <option value="bounced">Bounced</option>
          <option value="complained">Complained</option>
          <option value="pending_confirmation">Pending Confirmation</option>
        </select>
      </div>

      {/* Subscriptions Table */}
      <Card>
        <CardHeader>
          <CardTitle>Newsletter Subscriptions ({filteredSubscriptions.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Source</TableHead>
                <TableHead>Subscribed</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSubscriptions.map((subscription) => (
                <TableRow key={subscription.id}>
                  <TableCell className="font-medium">{subscription.email}</TableCell>
                  <TableCell>
                    {subscription.firstName || subscription.lastName 
                      ? `${subscription.firstName || ''} ${subscription.lastName || ''}`.trim()
                      : '-'
                    }
                  </TableCell>
                  <TableCell>{subscription.phone || '-'}</TableCell>
                  <TableCell>
                    {subscription.city && subscription.state 
                      ? `${subscription.city}, ${subscription.state}`
                      : subscription.city || subscription.state || '-'
                    }
                  </TableCell>
                  <TableCell>{getStatusBadge(subscription.status)}</TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {subscription.source.toLowerCase().replace('_', ' ')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {new Date(subscription.subscribedAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </DropdownMenuItem>
                        {subscription.status === 'ACTIVE' && (
                          <DropdownMenuItem 
                            onClick={() => handleUnsubscribe(subscription.email)}
                            className="text-red-600"
                          >
                            <UserX className="mr-2 h-4 w-4" />
                            Unsubscribe
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredSubscriptions.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              No newsletter subscriptions found.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Top Sources and Interests */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Top Subscription Sources</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {stats.topSources.map((source, index) => (
                  <div key={source.source} className="flex justify-between items-center">
                    <span className="text-sm">{source.source.toLowerCase().replace('_', ' ')}</span>
                    <Badge variant="secondary">{source.count}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Top Interests</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {stats.topInterests.map((interest, index) => (
                  <div key={interest.interest} className="flex justify-between items-center">
                    <span className="text-sm">{interest.interest.toLowerCase()}</span>
                    <Badge variant="secondary">{interest.count}</Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-6">
          <NewsletterCampaignManager />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {/* Analytics Dashboard */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Open Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">24.5%</div>
                <p className="text-xs text-muted-foreground">Industry avg: 21.3%</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Click Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3.2%</div>
                <p className="text-xs text-muted-foreground">Industry avg: 2.6%</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Bounce Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1.8%</div>
                <p className="text-xs text-muted-foreground">Industry avg: 2.1%</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Unsubscribe Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">0.5%</div>
                <p className="text-xs text-muted-foreground">Industry avg: 0.8%</p>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Analytics */}
          <Card>
            <CardHeader>
              <CardTitle>Newsletter Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <TrendingUp className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <p>Detailed analytics coming soon...</p>
                <p className="text-sm">Track email performance, subscriber engagement, and campaign ROI</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
