"use client"

import React, { useState } from 'react'
import { generateClient } from 'aws-amplify/api'
import { createVendor, createShop, createVenue } from '@/src/graphql/mutations'
import { showToast } from '@/lib/toast'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Database,
  ShoppingBag,
  Users,
  MapPin,
  Play,
  CheckCircle,
  XCircle,
  Loader2,
  Shield
} from 'lucide-react'

const client = generateClient({ authMode: 'userPool' })

interface SeedingResult {
  type: 'shop' | 'vendor' | 'venue'
  name: string
  success: boolean
  id?: string
  error?: string
}

export function DataSeeder() {
  const { user } = useAuth()
  const [isSeeding, setIsSeeding] = useState(false)
  const [progress, setProgress] = useState(0)
  const [results, setResults] = useState<SeedingResult[]>([])
  const [currentStep, setCurrentStep] = useState('')

  // Generate test data with current user's ID
  const generateTestData = (userId: string) => ({
    shops: [
      {
        userId: userId,
        name: "Designer Bridal Lehenga - Royal Red",
        category: "Bridal Wear",
        price: "45000",
        originalPrice: "55000",
        discount: 18,
        stock: 5,
        sku: "BL-RR-001",
        brand: "Royal Threads",
        featured: true,
        description: "Exquisite handcrafted bridal lehenga with intricate zardozi work.",
        features: ["Hand-embroidered zardozi work", "Premium silk fabric"],
        sizes: ["S", "M", "L", "XL"],
        colors: ["Red", "Maroon"],
        images: ["https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=500"],
        specifications: {
          fabric: "Pure Silk with Zardozi Embroidery",
          work: "Hand Embroidered",
          occasion: "Wedding, Reception",
          care: "Dry Clean Only",
          delivery: "7-10 business days",
          returnPolicy: "15 days return policy"
        },
        rating: 4.8,
        reviewCount: 24,
        inStock: true,
        status: "active"
      },
      {
        userId: userId,
        name: "Groom's Sherwani Set - Golden Elegance",
        category: "Groom Wear",
        price: "25000",
        originalPrice: "30000",
        discount: 17,
        stock: 8,
        sku: "GS-GE-002",
        brand: "Maharaja Collection",
        featured: true,
        description: "Elegant golden sherwani with matching churidar and dupatta.",
        features: ["Premium brocade fabric", "Custom tailoring available"],
        sizes: ["S", "M", "L", "XL"],
        colors: ["Golden", "Cream Gold"],
        images: ["https://images.unsplash.com/photo-1506629905607-c28b47d3e7b0?w=500"],
        specifications: {
          fabric: "Silk Brocade",
          work: "Machine Embroidered",
          occasion: "Wedding, Engagement",
          care: "Dry Clean Only",
          delivery: "5-7 business days",
          returnPolicy: "10 days return policy"
        },
        rating: 4.6,
        reviewCount: 18,
        inStock: true,
        status: "active"
      },
      {
        userId: userId,
        name: "Wedding Jewelry Set - Kundan Collection",
        category: "Jewelry",
        price: "35000",
        originalPrice: "42000",
        discount: 17,
        stock: 3,
        sku: "WJ-KC-003",
        brand: "Heritage Jewels",
        featured: false,
        description: "Traditional Kundan jewelry set with necklace, earrings, and maang tikka.",
        features: ["Authentic Kundan work", "Gold-plated base"],
        sizes: ["One Size"],
        colors: ["Gold", "Antique Gold"],
        images: ["https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=500"],
        specifications: {
          fabric: "Gold Plated Metal",
          work: "Kundan Setting",
          occasion: "Wedding, Festival",
          care: "Store in jewelry box, avoid moisture",
          delivery: "3-5 business days",
          returnPolicy: "7 days return policy"
        },
        rating: 4.9,
        reviewCount: 12,
        inStock: true,
        status: "active"
      },
      {
        userId: userId,
        name: "Wedding Invitation Cards - Royal Design",
        category: "Invitations",
        price: "150",
        originalPrice: "200",
        discount: 25,
        stock: 500,
        sku: "WI-RD-004",
        brand: "Creative Cards",
        featured: false,
        description: "Elegant wedding invitation cards with royal design and gold foiling.",
        features: ["Premium cardstock", "Gold foil printing", "Customizable text"],
        sizes: ["5x7 inches", "6x8 inches"],
        colors: ["Cream", "Ivory", "Light Pink"],
        images: ["https://images.unsplash.com/photo-1511988617509-a57c8a288659?w=500"],
        specifications: {
          fabric: "Premium Cardstock",
          work: "Digital + Foil Printing",
          occasion: "Wedding Invitation",
          care: "Handle with care",
          delivery: "7-14 business days",
          returnPolicy: "No returns on customized items"
        },
        rating: 4.5,
        reviewCount: 45,
        inStock: true,
        status: "active"
      },
      {
        userId: userId,
        name: "Bridal Makeup Kit - Professional",
        category: "Beauty & Makeup",
        price: "8500",
        originalPrice: "10000",
        discount: 15,
        stock: 12,
        sku: "BMK-PR-005",
        brand: "Glamour Studio",
        featured: true,
        description: "Complete bridal makeup kit with premium cosmetics and brushes.",
        features: ["Professional grade cosmetics", "Complete brush set", "Long-lasting formula"],
        sizes: ["Standard Kit"],
        colors: ["Natural", "Bold", "Classic"],
        images: ["https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=500"],
        specifications: {
          fabric: "Cosmetic Grade Materials",
          work: "Professional Makeup",
          occasion: "Bridal, Special Events",
          care: "Store in cool, dry place",
          delivery: "3-5 business days",
          returnPolicy: "7 days return policy"
        },
        rating: 4.7,
        reviewCount: 28,
        inStock: true,
        status: "active"
      },
      {
        userId: userId,
        name: "Wedding Shoes - Designer Heels",
        category: "Footwear",
        price: "4500",
        originalPrice: "6000",
        discount: 25,
        stock: 15,
        sku: "WS-DH-006",
        brand: "Elegant Steps",
        featured: false,
        description: "Designer wedding heels with comfortable padding and elegant design.",
        features: ["Comfortable padding", "Anti-slip sole", "Elegant design"],
        sizes: ["5", "6", "7", "8", "9"],
        colors: ["Gold", "Silver", "Rose Gold"],
        images: ["https://images.unsplash.com/photo-1543163521-1bf539c55dd2?w=500"],
        specifications: {
          fabric: "Synthetic Leather",
          work: "Machine Crafted",
          occasion: "Wedding, Party",
          care: "Clean with soft cloth",
          delivery: "5-7 business days",
          returnPolicy: "10 days return policy"
        },
        rating: 4.4,
        reviewCount: 19,
        inStock: true,
        status: "active"
      }
    ],
    vendors: [
      {
        userId: userId,
        name: "Elegant Moments Photography",
        category: "Wedding Photography",
        description: "Professional wedding photography services with cinematic videography.",
        contact: "+91 **********",
        email: "<EMAIL>",
        address: "123 Photography Street",
        city: "Chennai",
        state: "Tamil Nadu",
        pincode: "600001",
        website: "www.elegantmoments.com",
        profilePhoto: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300",
        gallery: ["https://images.unsplash.com/photo-1519741497674-611481863552?w=500"],
        services: [
          { name: "Wedding Photography Package", price: "₹75,000" },
          { name: "Pre-Wedding Shoot", price: "₹25,000" }
        ],
        socialMedia: {
          facebook: "elegantmomentsphotography",
          instagram: "elegant_moments_photo",
          youtube: "elegantmomentsstudio"
        },
        experience: "8+ years",
        events: "200+ weddings",
        responseTime: "Within 2 hours",
        rating: 4.8,
        reviewCount: 45,
        verified: true,
        featured: true,
        availability: "Available",
        priceRange: "₹25,000 - ₹1,50,000",
        specializations: ["Wedding Photography", "Pre-Wedding Shoots"],
        awards: ["Best Wedding Photographer 2023"],
        languages: ["English", "Tamil", "Hindi"],
        coverage: ["Chennai", "Bangalore"],
        equipment: ["Canon 5D Mark IV", "Sony A7R IV"],
        status: "active"
      },
      {
        userId: userId,
        name: "Spice Garden Catering",
        category: "Catering Services",
        description: "Premium catering services specializing in South Indian and North Indian cuisines.",
        contact: "+91 **********",
        email: "<EMAIL>",
        address: "456 Catering Complex",
        city: "Bangalore",
        state: "Karnataka",
        pincode: "560001",
        website: "www.spicegardencatering.com",
        profilePhoto: "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=300",
        gallery: ["https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=500"],
        services: [
          { name: "Wedding Feast Package", price: "₹800 per person" },
          { name: "Reception Catering", price: "₹600 per person" }
        ],
        socialMedia: {
          facebook: "spicegardencatering",
          instagram: "spice_garden_catering",
          youtube: "spicegardenofficial"
        },
        experience: "12+ years",
        events: "500+ events",
        responseTime: "Within 1 hour",
        rating: 4.7,
        reviewCount: 78,
        verified: true,
        featured: true,
        availability: "Available",
        priceRange: "₹400 - ₹1,200 per person",
        specializations: ["South Indian Cuisine", "North Indian Cuisine"],
        awards: ["Best Catering Service 2022"],
        languages: ["English", "Kannada", "Tamil"],
        coverage: ["Bangalore", "Mysore"],
        equipment: ["Mobile Kitchen Units", "Serving Equipment"],
        status: "active"
      },
      {
        userId: userId,
        name: "Melodic Beats Entertainment",
        category: "DJ & Music",
        description: "Professional DJ services and live music entertainment for weddings.",
        contact: "+91 **********",
        email: "<EMAIL>",
        address: "789 Music Street",
        city: "Mumbai",
        state: "Maharashtra",
        pincode: "400001",
        website: "www.melodicbeats.com",
        profilePhoto: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300",
        gallery: ["https://images.unsplash.com/photo-1514525253161-7a46d19cd819?w=500"],
        services: [
          { name: "Wedding DJ Package", price: "₹35,000" },
          { name: "Live Band Performance", price: "₹75,000" }
        ],
        socialMedia: {
          facebook: "melodicbeatsent",
          instagram: "melodic_beats_dj",
          youtube: "melodicbeatsofficial"
        },
        experience: "6+ years",
        events: "150+ events",
        responseTime: "Within 3 hours",
        rating: 4.6,
        reviewCount: 32,
        verified: true,
        featured: false,
        availability: "Available",
        priceRange: "₹20,000 - ₹1,00,000",
        specializations: ["Wedding DJ", "Live Music"],
        awards: ["Best DJ Service 2023"],
        languages: ["English", "Hindi", "Marathi"],
        coverage: ["Mumbai", "Pune"],
        equipment: ["Pioneer DJ Equipment", "JBL Sound System"],
        status: "active"
      },
      {
        userId: userId,
        name: "Floral Dreams Decoration",
        category: "Decoration",
        description: "Creative wedding decoration services with fresh flowers and elegant designs.",
        contact: "+91 **********",
        email: "<EMAIL>",
        address: "321 Flower Market",
        city: "Delhi",
        state: "Delhi",
        pincode: "110001",
        website: "www.floraldreams.com",
        profilePhoto: "https://images.unsplash.com/photo-1519225421980-715cb0215aed?w=300",
        gallery: ["https://images.unsplash.com/photo-1464366400600-7168b8af9bc3?w=500"],
        services: [
          { name: "Wedding Mandap Decoration", price: "₹50,000" },
          { name: "Reception Hall Decoration", price: "₹30,000" }
        ],
        socialMedia: {
          facebook: "floraldreamsdecor",
          instagram: "floral_dreams_decor",
          youtube: "floraldreamsofficial"
        },
        experience: "10+ years",
        events: "300+ weddings",
        responseTime: "Within 4 hours",
        rating: 4.8,
        reviewCount: 67,
        verified: true,
        featured: true,
        availability: "Available",
        priceRange: "₹15,000 - ₹1,50,000",
        specializations: ["Floral Decoration", "Theme Decoration"],
        awards: ["Best Decorator 2022", "Creative Excellence Award"],
        languages: ["English", "Hindi", "Punjabi"],
        coverage: ["Delhi", "Gurgaon", "Noida"],
        equipment: ["Fresh Flowers", "Decoration Materials", "Lighting"],
        status: "active"
      },
      {
        userId: userId,
        name: "Royal Transport Services",
        category: "Transportation",
        description: "Luxury wedding transportation with decorated cars and professional drivers.",
        contact: "+91 **********",
        email: "<EMAIL>",
        address: "456 Transport Hub",
        city: "Jaipur",
        state: "Rajasthan",
        pincode: "302001",
        website: "www.royaltransport.com",
        profilePhoto: "https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=300",
        gallery: ["https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=500"],
        services: [
          { name: "Bridal Car Decoration", price: "₹15,000" },
          { name: "Guest Transportation", price: "₹25,000" }
        ],
        socialMedia: {
          facebook: "royaltransportservices",
          instagram: "royal_transport_jaipur",
          youtube: "royaltransportofficial"
        },
        experience: "15+ years",
        events: "500+ weddings",
        responseTime: "Within 2 hours",
        rating: 4.5,
        reviewCount: 89,
        verified: true,
        featured: false,
        availability: "Available",
        priceRange: "₹10,000 - ₹75,000",
        specializations: ["Luxury Cars", "Vintage Cars", "Decorated Vehicles"],
        awards: ["Best Transport Service 2023"],
        languages: ["English", "Hindi", "Rajasthani"],
        coverage: ["Jaipur", "Udaipur", "Jodhpur"],
        equipment: ["Luxury Cars", "Vintage Vehicles", "Decoration Materials"],
        status: "active"
      }
    ],
    venues: [
      {
        userId: userId,
        name: "Grand Palace Banquet Hall",
        description: "Luxurious banquet hall perfect for grand wedding celebrations.",
        type: "Banquet Hall",
        capacity: 500,
        location: "Anna Nagar",
        city: "Chennai",
        state: "Tamil Nadu",
        fullAddress: "123 Grand Palace Complex, Anna Nagar, Chennai - 600040",
        pincode: "600040",
        contactPhone: "+91 **********",
        contactEmail: "<EMAIL>",
        website: "www.grandpalacebanquet.com",
        price: "150000",
        priceRange: "₹1,00,000 - ₹2,50,000",
        images: ["https://images.unsplash.com/photo-1519167758481-83f29c8a4e0a?w=500"],
        amenities: ["Air Conditioning", "Parking for 200 cars", "Bridal Room"],
        spaces: [{
          name: "Main Hall",
          capacity: 500,
          area: "5000 sq ft",
          price: "100000",
          description: "Spacious main hall with elegant chandeliers",
          amenities: ["AC", "Sound System", "Stage"],
          images: ["https://images.unsplash.com/photo-1519167758481-83f29c8a4e0a?w=500"]
        }],
        packages: [{
          name: "Premium Wedding Package",
          price: "200000",
          duration: "Full Day",
          description: "Complete wedding package with decoration and catering",
          includes: ["Venue", "Decoration", "Catering", "Sound System"],
          excludes: ["Photography", "Transportation"],
          terms: "50% advance required"
        }],
        socialMedia: {
          facebook: "grandpalacebanquet",
          instagram: "grand_palace_venue",
          youtube: "grandpalaceofficial"
        },
        rating: 4.7,
        reviewCount: 56,
        bookings: 120,
        verified: true,
        featured: true,
        status: "active",
        availability: "Available",
        policies: {
          cancellation: "48 hours notice required",
          advance: "50% advance payment required",
          catering: "Outside catering allowed with permission",
          decoration: "Decoration team available",
          alcohol: "Alcohol permitted with license",
          music: "Music allowed till 11 PM",
          parking: "Complimentary valet parking"
        },
        coordinates: { latitude: 13.0827, longitude: 80.2707 },
        operatingHours: {
          monday: "9 AM - 11 PM",
          tuesday: "9 AM - 11 PM",
          wednesday: "9 AM - 11 PM",
          thursday: "9 AM - 11 PM",
          friday: "9 AM - 11 PM",
          saturday: "9 AM - 11 PM",
          sunday: "9 AM - 11 PM"
        }
      },
      {
        userId: userId,
        name: "Seaside Resort & Convention Center",
        description: "Beautiful beachside resort perfect for destination weddings.",
        type: "Resort",
        capacity: 300,
        location: "ECR",
        city: "Chennai",
        state: "Tamil Nadu",
        fullAddress: "456 East Coast Road, Mahabalipuram, Chennai - 603104",
        pincode: "603104",
        contactPhone: "+91 **********",
        contactEmail: "<EMAIL>",
        website: "www.seasideresort.com",
        price: "200000",
        priceRange: "₹1,50,000 - ₹3,50,000",
        images: ["https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=500"],
        amenities: ["Beach Access", "Swimming Pool", "Spa Services"],
        spaces: [{
          name: "Beachside Pavilion",
          capacity: 200,
          area: "3000 sq ft",
          price: "120000",
          description: "Open-air pavilion with ocean views",
          amenities: ["Ocean View", "Natural Lighting", "Beach Access"],
          images: ["https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=500"]
        }],
        packages: [{
          name: "Destination Wedding Package",
          price: "300000",
          duration: "2 Days",
          description: "Complete destination wedding with accommodation",
          includes: ["Venue", "Accommodation", "Meals", "Decoration"],
          excludes: ["Transportation", "Photography"],
          terms: "60% advance required"
        }],
        socialMedia: {
          facebook: "seasideresortchennai",
          instagram: "seaside_resort_ecr",
          youtube: "seasideresortofficial"
        },
        rating: 4.8,
        reviewCount: 34,
        bookings: 45,
        verified: true,
        featured: true,
        status: "active",
        availability: "Available",
        policies: {
          cancellation: "72 hours notice required",
          advance: "60% advance payment required",
          catering: "In-house catering only",
          decoration: "Decoration team available",
          alcohol: "Alcohol permitted",
          music: "Music allowed till 12 AM",
          parking: "Complimentary parking available"
        },
        coordinates: { latitude: 12.7767, longitude: 80.1962 },
        operatingHours: {
          monday: "24 Hours",
          tuesday: "24 Hours",
          wednesday: "24 Hours",
          thursday: "24 Hours",
          friday: "24 Hours",
          saturday: "24 Hours",
          sunday: "24 Hours"
        }
      },
      {
        userId: userId,
        name: "Heritage Palace Hotel",
        description: "Majestic heritage hotel with royal architecture for traditional weddings.",
        type: "Heritage Hotel",
        capacity: 400,
        location: "Rajaji Nagar",
        city: "Bangalore",
        state: "Karnataka",
        fullAddress: "789 Heritage Complex, Rajaji Nagar, Bangalore - 560010",
        pincode: "560010",
        contactPhone: "+91 **********",
        contactEmail: "<EMAIL>",
        website: "www.heritagepalacehotel.com",
        price: "180000",
        priceRange: "₹1,20,000 - ₹3,00,000",
        images: ["https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=500"],
        amenities: ["Royal Architecture", "Heritage Decor", "Luxury Suites"],
        spaces: [{
          name: "Royal Durbar Hall",
          capacity: 300,
          area: "4000 sq ft",
          price: "150000",
          description: "Grand hall with royal architecture and chandeliers",
          amenities: ["Royal Decor", "Chandeliers", "Stage"],
          images: ["https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=500"]
        }],
        packages: [{
          name: "Royal Wedding Package",
          price: "250000",
          duration: "Full Day",
          description: "Royal themed wedding with traditional elements",
          includes: ["Venue", "Royal Decoration", "Traditional Music"],
          excludes: ["Catering", "Photography"],
          terms: "50% advance required"
        }],
        socialMedia: {
          facebook: "heritagepalacehotel",
          instagram: "heritage_palace_blr",
          youtube: "heritagepalaceofficial"
        },
        rating: 4.9,
        reviewCount: 28,
        bookings: 67,
        verified: true,
        featured: false,
        status: "active",
        availability: "Available",
        policies: {
          cancellation: "48 hours notice required",
          advance: "50% advance payment required",
          catering: "In-house catering preferred",
          decoration: "Traditional decoration available",
          alcohol: "Alcohol permitted with license",
          music: "Traditional music encouraged",
          parking: "Valet parking available"
        },
        coordinates: { latitude: 12.9716, longitude: 77.5946 },
        operatingHours: {
          monday: "24 Hours",
          tuesday: "24 Hours",
          wednesday: "24 Hours",
          thursday: "24 Hours",
          friday: "24 Hours",
          saturday: "24 Hours",
          sunday: "24 Hours"
        }
      },
      {
        userId: userId,
        name: "Garden Paradise Resort",
        description: "Beautiful garden resort with lush greenery perfect for outdoor weddings.",
        type: "Garden Resort",
        capacity: 250,
        location: "Whitefield",
        city: "Bangalore",
        state: "Karnataka",
        fullAddress: "567 Garden Paradise, Whitefield, Bangalore - 560066",
        pincode: "560066",
        contactPhone: "+91 **********",
        contactEmail: "<EMAIL>",
        website: "www.gardenparadiseresort.com",
        price: "120000",
        priceRange: "₹80,000 - ₹2,00,000",
        images: ["https://images.unsplash.com/photo-1566073771259-6a8506099945?w=500"],
        amenities: ["Garden Setting", "Natural Ambiance", "Outdoor Spaces"],
        spaces: [{
          name: "Garden Lawn",
          capacity: 200,
          area: "5000 sq ft",
          price: "100000",
          description: "Spacious garden lawn with natural beauty",
          amenities: ["Natural Setting", "Garden Views", "Open Air"],
          images: ["https://images.unsplash.com/photo-1566073771259-6a8506099945?w=500"]
        }],
        packages: [{
          name: "Garden Wedding Package",
          price: "150000",
          duration: "Full Day",
          description: "Complete garden wedding with natural decoration",
          includes: ["Venue", "Garden Decoration", "Natural Lighting"],
          excludes: ["Catering", "Photography"],
          terms: "40% advance required"
        }],
        socialMedia: {
          facebook: "gardenparadiseresort",
          instagram: "garden_paradise_blr",
          youtube: "gardenparadiseofficial"
        },
        rating: 4.6,
        reviewCount: 42,
        bookings: 89,
        verified: true,
        featured: true,
        status: "active",
        availability: "Available",
        policies: {
          cancellation: "24 hours notice required",
          advance: "40% advance payment required",
          catering: "Outside catering allowed",
          decoration: "Garden decoration available",
          alcohol: "Alcohol permitted",
          music: "Music allowed till 10 PM",
          parking: "Free parking available"
        },
        coordinates: { latitude: 12.9698, longitude: 77.7500 },
        operatingHours: {
          monday: "6 AM - 11 PM",
          tuesday: "6 AM - 11 PM",
          wednesday: "6 AM - 11 PM",
          thursday: "6 AM - 11 PM",
          friday: "6 AM - 11 PM",
          saturday: "6 AM - 11 PM",
          sunday: "6 AM - 11 PM"
        }
      },
      {
        userId: userId,
        name: "City Convention Center",
        description: "Modern convention center with state-of-the-art facilities for large weddings.",
        type: "Convention Center",
        capacity: 800,
        location: "MG Road",
        city: "Pune",
        state: "Maharashtra",
        fullAddress: "890 Convention Complex, MG Road, Pune - 411001",
        pincode: "411001",
        contactPhone: "+91 **********",
        contactEmail: "<EMAIL>",
        website: "www.cityconventioncenter.com",
        price: "250000",
        priceRange: "₹2,00,000 - ₹5,00,000",
        images: ["https://images.unsplash.com/photo-1519167758481-83f29c8a4e0a?w=500"],
        amenities: ["Modern Facilities", "Large Capacity", "Professional Setup"],
        spaces: [{
          name: "Grand Convention Hall",
          capacity: 600,
          area: "8000 sq ft",
          price: "200000",
          description: "Massive convention hall with modern amenities",
          amenities: ["Modern Setup", "Professional Lighting", "Sound System"],
          images: ["https://images.unsplash.com/photo-1519167758481-83f29c8a4e0a?w=500"]
        }],
        packages: [{
          name: "Grand Convention Package",
          price: "400000",
          duration: "Full Day",
          description: "Complete convention setup for large weddings",
          includes: ["Venue", "Professional Setup", "Technical Support"],
          excludes: ["Catering", "Photography"],
          terms: "60% advance required"
        }],
        socialMedia: {
          facebook: "cityconventioncenter",
          instagram: "city_convention_pune",
          youtube: "cityconventionofficial"
        },
        rating: 4.4,
        reviewCount: 73,
        bookings: 156,
        verified: true,
        featured: true,
        status: "active",
        availability: "Available",
        policies: {
          cancellation: "72 hours notice required",
          advance: "60% advance payment required",
          catering: "Approved caterers only",
          decoration: "Professional decoration team",
          alcohol: "Alcohol permitted with license",
          music: "Music allowed till 11 PM",
          parking: "Multi-level parking available"
        },
        coordinates: { latitude: 18.5204, longitude: 73.8567 },
        operatingHours: {
          monday: "8 AM - 12 AM",
          tuesday: "8 AM - 12 AM",
          wednesday: "8 AM - 12 AM",
          thursday: "8 AM - 12 AM",
          friday: "8 AM - 12 AM",
          saturday: "8 AM - 12 AM",
          sunday: "8 AM - 12 AM"
        }
      }
    ]
  })

  const seedAllData = async () => {
    if (!user?.userId) {
      showToast.error('User not authenticated. Please log in first.')
      return
    }

    setIsSeeding(true)
    setProgress(0)
    setResults([])

    const testData = generateTestData(user.userId)
    const allResults: SeedingResult[] = []
    const totalItems = testData.shops.length + testData.vendors.length + testData.venues.length
    let completed = 0

    try {
      // Seed shops
      setCurrentStep('Creating shop products...')
      for (const shop of testData.shops) {
        try {
          const result = await client.graphql({
            query: createShop,
            variables: { input: shop }
          })
          
          allResults.push({
            type: 'shop',
            name: shop.name,
            success: true,
            id: result.data.createShop.id
          })
          
          showToast.success(`Created shop: ${shop.name}`)
        } catch (error: any) {
          allResults.push({
            type: 'shop',
            name: shop.name,
            success: false,
            error: error.message
          })
          
          showToast.error(`Failed to create shop: ${shop.name}`)
        }
        
        completed++
        setProgress((completed / totalItems) * 100)
        setResults([...allResults])
      }

      // Seed vendors
      setCurrentStep('Creating vendor services...')
      for (const vendor of testData.vendors) {
        try {
          const result = await client.graphql({
            query: createVendor,
            variables: { input: vendor }
          })
          
          allResults.push({
            type: 'vendor',
            name: vendor.name,
            success: true,
            id: result.data.createVendor.id
          })
          
          showToast.success(`Created vendor: ${vendor.name}`)
        } catch (error: any) {
          allResults.push({
            type: 'vendor',
            name: vendor.name,
            success: false,
            error: error.message
          })
          
          showToast.error(`Failed to create vendor: ${vendor.name}`)
        }
        
        completed++
        setProgress((completed / totalItems) * 100)
        setResults([...allResults])
      }

      // Seed venues
      setCurrentStep('Creating venue listings...')
      for (const venue of testData.venues) {
        try {
          const result = await client.graphql({
            query: createVenue,
            variables: { input: venue }
          })
          
          allResults.push({
            type: 'venue',
            name: venue.name,
            success: true,
            id: result.data.createVenue.id
          })
          
          showToast.success(`Created venue: ${venue.name}`)
        } catch (error: any) {
          allResults.push({
            type: 'venue',
            name: venue.name,
            success: false,
            error: error.message
          })
          
          showToast.error(`Failed to create venue: ${venue.name}`)
        }
        
        completed++
        setProgress((completed / totalItems) * 100)
        setResults([...allResults])
      }

      setCurrentStep('Seeding completed!')
      const successful = allResults.filter(r => r.success).length
      showToast.success(`Seeding completed! ${successful}/${totalItems} items created successfully.`)
      
    } catch (error: any) {
      showToast.error(`Seeding failed: ${error.message}`)
    } finally {
      setIsSeeding(false)
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'shop': return <ShoppingBag className="w-4 h-4" />
      case 'vendor': return <Users className="w-4 h-4" />
      case 'venue': return <MapPin className="w-4 h-4" />
      default: return <Database className="w-4 h-4" />
    }
  }

  const getTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'shop': return 'bg-blue-100 text-blue-800'
      case 'vendor': return 'bg-green-100 text-green-800'
      case 'venue': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Show authentication error if user is not logged in
  if (!user?.userId) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="pt-6">
          <div className="text-center">
            <Shield className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Authentication Required</h2>
            <p className="text-gray-600">You need to be logged in to seed test data.</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="w-5 h-5" />
          Test Data Seeder
        </CardTitle>
        <p className="text-gray-600">
          Seed the database with 10 test products across shops, vendors, and venues for testing purposes.
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Data Overview */}
        {user?.userId && (() => {
          const previewData = generateTestData(user.userId)
          return (
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <ShoppingBag className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                <div className="font-semibold text-blue-900">{previewData.shops.length} Shop Products</div>
                <div className="text-sm text-blue-600">Bridal wear, jewelry, makeup, shoes, invitations</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <Users className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <div className="font-semibold text-green-900">{previewData.vendors.length} Vendor Services</div>
                <div className="text-sm text-green-600">Photography, catering, DJ, decoration, transport</div>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <MapPin className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                <div className="font-semibold text-purple-900">{previewData.venues.length} Venue Listings</div>
                <div className="text-sm text-purple-600">Banquet halls, resorts, hotels, convention centers</div>
              </div>
            </div>
          )
        })()}

        {/* Seeding Controls */}
        <div className="flex items-center justify-between">
          <Button 
            onClick={seedAllData} 
            disabled={isSeeding}
            className="flex items-center gap-2"
          >
            {isSeeding ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Play className="w-4 h-4" />
            )}
            {isSeeding ? 'Seeding...' : 'Start Seeding'}
          </Button>
          
          {isSeeding && (
            <div className="flex-1 ml-4">
              <div className="text-sm text-gray-600 mb-1">{currentStep}</div>
              <Progress value={progress} className="w-full" />
            </div>
          )}
        </div>

        {/* Results */}
        {results.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-semibold">Seeding Results:</h3>
            <div className="max-h-60 overflow-y-auto space-y-2">
              {results.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    {getTypeIcon(result.type)}
                    <span className="font-medium">{result.name}</span>
                    <Badge className={getTypeBadgeColor(result.type)}>
                      {result.type}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    {result.success ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600" />
                    )}
                    {result.id && (
                      <Badge variant="outline" className="text-xs">
                        {result.id.slice(-8)}
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default DataSeeder
