'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Send, 
  Mail, 
  Users, 
  Eye, 
  Calendar,
  Target,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';

interface CampaignData {
  subject: string;
  content: string;
  targetAudience: string;
  interests: string[];
  scheduledDate?: string;
  isScheduled: boolean;
}

export default function NewsletterCampaignManager() {
  const [campaignData, setCampaignData] = useState<CampaignData>({
    subject: '',
    content: '',
    targetAudience: 'all',
    interests: [],
    isScheduled: false
  });
  
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [previewMode, setPreviewMode] = useState(false);

  const interestOptions = [
    'PHOTOGRAPHY', 'VIDEOGRAPHY', 'CATERING', 'DECORATION', 'MAKEUP',
    'VENUES', 'SHOPPING', 'PLANNING', 'HONEYMOON', 'JEWELRY',
    'INVITATIONS', 'MUSIC', 'TRANSPORTATION'
  ];

  const handleInputChange = (field: string, value: any) => {
    setCampaignData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleInterestToggle = (interest: string) => {
    setCampaignData(prev => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter(i => i !== interest)
        : [...prev.interests, interest]
    }));
  };

  const handleSendCampaign = async () => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Simulate API call for sending campaign
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation, this would call your email service
      console.log('Sending campaign:', campaignData);
      
      setSuccess(true);
      // Reset form
      setCampaignData({
        subject: '',
        content: '',
        targetAudience: 'all',
        interests: [],
        isScheduled: false
      });
    } catch (err: any) {
      setError(err.message || 'Failed to send campaign');
    } finally {
      setLoading(false);
    }
  };

  const getTargetAudienceCount = () => {
    // This would be calculated based on actual subscriber data
    switch (campaignData.targetAudience) {
      case 'all': return '1,234';
      case 'active': return '987';
      case 'recent': return '156';
      case 'interests': return campaignData.interests.length > 0 ? '543' : '0';
      default: return '0';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Newsletter Campaign Manager</h2>
          <p className="text-gray-600">Create and send newsletter campaigns to subscribers</p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => setPreviewMode(!previewMode)}
          >
            <Eye className="w-4 h-4 mr-2" />
            {previewMode ? 'Edit' : 'Preview'}
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            Newsletter campaign sent successfully!
          </AlertDescription>
        </Alert>
      )}

      {previewMode ? (
        <Card>
          <CardHeader>
            <CardTitle>Campaign Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Subject</Label>
                <div className="p-3 bg-gray-50 rounded border">
                  {campaignData.subject || 'No subject'}
                </div>
              </div>
              
              <div>
                <Label className="text-sm font-medium">Content</Label>
                <div className="p-4 bg-white border rounded min-h-[200px] whitespace-pre-wrap">
                  {campaignData.content || 'No content'}
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label>Target Audience</Label>
                  <p className="text-gray-600 capitalize">
                    {campaignData.targetAudience.replace('_', ' ')}
                  </p>
                </div>
                <div>
                  <Label>Estimated Recipients</Label>
                  <p className="text-gray-600">{getTargetAudienceCount()} subscribers</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Campaign Content */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="w-5 h-5" />
                  Campaign Content
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="subject">Email Subject *</Label>
                  <Input
                    id="subject"
                    value={campaignData.subject}
                    onChange={(e) => handleInputChange('subject', e.target.value)}
                    placeholder="Enter email subject line"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="content">Email Content *</Label>
                  <Textarea
                    id="content"
                    value={campaignData.content}
                    onChange={(e) => handleInputChange('content', e.target.value)}
                    placeholder="Write your newsletter content here..."
                    rows={12}
                    required
                  />
                </div>
              </CardContent>
            </Card>

            {/* Scheduling */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Scheduling
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="scheduled"
                    checked={campaignData.isScheduled}
                    onCheckedChange={(checked) => 
                      handleInputChange('isScheduled', checked as boolean)
                    }
                  />
                  <Label htmlFor="scheduled">Schedule for later</Label>
                </div>

                {campaignData.isScheduled && (
                  <div>
                    <Label htmlFor="scheduledDate">Scheduled Date & Time</Label>
                    <Input
                      id="scheduledDate"
                      type="datetime-local"
                      value={campaignData.scheduledDate || ''}
                      onChange={(e) => handleInputChange('scheduledDate', e.target.value)}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Targeting & Actions */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Target Audience
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>Audience Type</Label>
                  <Select 
                    value={campaignData.targetAudience} 
                    onValueChange={(value) => handleInputChange('targetAudience', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Subscribers</SelectItem>
                      <SelectItem value="active">Active Subscribers</SelectItem>
                      <SelectItem value="recent">Recent Subscribers (30 days)</SelectItem>
                      <SelectItem value="interests">By Interests</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {campaignData.targetAudience === 'interests' && (
                  <div>
                    <Label>Select Interests</Label>
                    <div className="grid grid-cols-1 gap-2 mt-2 max-h-40 overflow-y-auto">
                      {interestOptions.map((interest) => (
                        <div key={interest} className="flex items-center space-x-2">
                          <Checkbox
                            id={interest}
                            checked={campaignData.interests.includes(interest)}
                            onCheckedChange={() => handleInterestToggle(interest)}
                          />
                          <Label htmlFor={interest} className="text-sm capitalize">
                            {interest.toLowerCase().replace('_', ' ')}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="p-3 bg-blue-50 rounded">
                  <div className="flex items-center gap-2 text-blue-700">
                    <Users className="w-4 h-4" />
                    <span className="font-medium">Estimated Recipients</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-800">
                    {getTargetAudienceCount()}
                  </p>
                  <p className="text-sm text-blue-600">subscribers will receive this email</p>
                </div>
              </CardContent>
            </Card>

            {/* Send Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Send Campaign</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  onClick={handleSendCampaign}
                  disabled={loading || !campaignData.subject || !campaignData.content}
                  className="w-full"
                >
                  {loading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      {campaignData.isScheduled ? 'Scheduling...' : 'Sending...'}
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4 mr-2" />
                      {campaignData.isScheduled ? 'Schedule Campaign' : 'Send Now'}
                    </>
                  )}
                </Button>

                <p className="text-xs text-gray-500 text-center">
                  {campaignData.isScheduled 
                    ? 'Campaign will be sent at the scheduled time'
                    : 'Campaign will be sent immediately to all targeted subscribers'
                  }
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}
