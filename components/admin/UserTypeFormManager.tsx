"use client"

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  FileText, User, Briefcase, Shield, Crown, Calendar, 
  CheckCircle, XCircle, Clock, AlertTriangle, Plus, Trash2 
} from 'lucide-react';
import { UserTypeFormData, BusinessFormDetails, PersonalFormDetails, AdminFormDetails } from '@/lib/services/userRoleService';

interface UserTypeFormManagerProps {
  userId?: string;
  currentFormData?: UserTypeFormData;
  onFormSubmit?: (formData: UserTypeFormData) => void;
  onFormUpdate?: (formData: UserTypeFormData) => void;
  mode?: 'create' | 'edit' | 'view';
}

export default function UserTypeFormManager({
  userId,
  currentFormData,
  onFormSubmit,
  onFormUpdate,
  mode = 'create'
}: UserTypeFormManagerProps) {
  const [formData, setFormData] = useState<UserTypeFormData>({
    formType: 'customer',
    submissionDate: new Date().toISOString(),
    formVersion: '1.0',
    verificationStatus: 'pending',
    approvalStatus: 'pending',
    submittedBy: userId || 'system'
  });

  const [businessDetails, setBusinessDetails] = useState<BusinessFormDetails>({
    businessName: '',
    businessType: '',
    businessCategory: '',
    businessDescription: '',
    servicesOffered: [],
    experienceYears: 0,
    teamSize: 1
  });

  const [personalDetails, setPersonalDetails] = useState<PersonalFormDetails>({
    firstName: '',
    lastName: '',
    interests: [],
    preferences: []
  });

  const [adminDetails, setAdminDetails] = useState<AdminFormDetails>({
    adminLevel: 'admin',
    responsibilities: []
  });

  useEffect(() => {
    if (currentFormData) {
      setFormData(currentFormData);
      if (currentFormData.businessDetails) {
        setBusinessDetails(currentFormData.businessDetails);
      }
      if (currentFormData.personalDetails) {
        setPersonalDetails(currentFormData.personalDetails);
      }
      if (currentFormData.adminDetails) {
        setAdminDetails(currentFormData.adminDetails);
      }
    }
  }, [currentFormData]);

  const formTypes = [
    { value: 'customer', label: 'Customer Form', icon: User, description: 'Personal wedding planning account' },
    { value: 'vendor', label: 'Business Form', icon: Briefcase, description: 'Business service provider account' },
    { value: 'admin', label: 'Admin Form', icon: Shield, description: 'Administrative account' },
    { value: 'bulk_import', label: 'Bulk Import', icon: FileText, description: 'Imported from external source' },
    { value: 'migration', label: 'Migration', icon: FileText, description: 'Migrated from legacy system' }
  ];

  const verificationStatuses = [
    { value: 'pending', label: 'Pending', icon: Clock, color: 'bg-yellow-100 text-yellow-800' },
    { value: 'verified', label: 'Verified', icon: CheckCircle, color: 'bg-green-100 text-green-800' },
    { value: 'rejected', label: 'Rejected', icon: XCircle, color: 'bg-red-100 text-red-800' }
  ];

  const approvalStatuses = [
    { value: 'pending', label: 'Pending', icon: Clock, color: 'bg-yellow-100 text-yellow-800' },
    { value: 'approved', label: 'Approved', icon: CheckCircle, color: 'bg-green-100 text-green-800' },
    { value: 'rejected', label: 'Rejected', icon: XCircle, color: 'bg-red-100 text-red-800' }
  ];

  const businessCategories = [
    'photography', 'videography', 'catering', 'decoration', 'venue', 'music', 
    'makeup', 'mehendi', 'transportation', 'flowers', 'jewelry', 'clothing', 'other'
  ];

  const servicesList = [
    'Wedding Photography', 'Pre-wedding Shoots', 'Videography', 'Drone Photography',
    'Catering Services', 'Venue Decoration', 'Floral Arrangements', 'Music & DJ',
    'Live Band', 'Makeup Artist', 'Mehendi Artist', 'Transportation', 'Other'
  ];

  const adminLevels = [
    { value: 'admin', label: 'Administrator' },
    { value: 'super_admin', label: 'Super Administrator' },
    { value: 'moderator', label: 'Content Moderator' },
    { value: 'support', label: 'Customer Support' }
  ];

  const handleFormSubmit = () => {
    const completeFormData: UserTypeFormData = {
      ...formData,
      businessDetails: formData.formType === 'vendor' ? businessDetails : undefined,
      personalDetails: formData.formType === 'customer' ? personalDetails : undefined,
      adminDetails: formData.formType === 'admin' ? adminDetails : undefined
    };

    if (mode === 'create') {
      onFormSubmit?.(completeFormData);
    } else {
      onFormUpdate?.(completeFormData);
    }
  };

  const addToArray = (array: string[], value: string, setter: (arr: string[]) => void) => {
    if (value && !array.includes(value)) {
      setter([...array, value]);
    }
  };

  const removeFromArray = (array: string[], index: number, setter: (arr: string[]) => void) => {
    setter(array.filter((_, i) => i !== index));
  };

  const getStatusBadge = (status: string, type: 'verification' | 'approval') => {
    const statuses = type === 'verification' ? verificationStatuses : approvalStatuses;
    const statusConfig = statuses.find(s => s.value === status);
    if (!statusConfig) return null;

    const IconComponent = statusConfig.icon;
    return (
      <Badge className={statusConfig.color}>
        <IconComponent className="w-3 h-3 mr-1" />
        {statusConfig.label}
      </Badge>
    );
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="w-5 h-5" />
          User Type Form Management
        </CardTitle>
        <div className="flex gap-2">
          {getStatusBadge(formData.verificationStatus, 'verification')}
          {getStatusBadge(formData.approvalStatus, 'approval')}
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="basic" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="details">Form Details</TabsTrigger>
            <TabsTrigger value="review">Review</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          {/* Basic Information Tab */}
          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="formType">Form Type</Label>
                <Select 
                  value={formData.formType} 
                  onValueChange={(value) => setFormData({...formData, formType: value as any})}
                  disabled={mode === 'view'}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select form type" />
                  </SelectTrigger>
                  <SelectContent>
                    {formTypes.map((type) => {
                      const IconComponent = type.icon;
                      return (
                        <SelectItem key={type.value} value={type.value}>
                          <div className="flex items-center gap-2">
                            <IconComponent className="w-4 h-4" />
                            <div>
                              <div>{type.label}</div>
                              <div className="text-xs text-gray-500">{type.description}</div>
                            </div>
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="formVersion">Form Version</Label>
                <Input
                  id="formVersion"
                  value={formData.formVersion}
                  onChange={(e) => setFormData({...formData, formVersion: e.target.value})}
                  disabled={mode === 'view'}
                />
              </div>

              <div>
                <Label htmlFor="verificationStatus">Verification Status</Label>
                <Select 
                  value={formData.verificationStatus} 
                  onValueChange={(value) => setFormData({...formData, verificationStatus: value as any})}
                  disabled={mode === 'view'}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {verificationStatuses.map((status) => {
                      const IconComponent = status.icon;
                      return (
                        <SelectItem key={status.value} value={status.value}>
                          <div className="flex items-center gap-2">
                            <IconComponent className="w-4 h-4" />
                            {status.label}
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="approvalStatus">Approval Status</Label>
                <Select 
                  value={formData.approvalStatus} 
                  onValueChange={(value) => setFormData({...formData, approvalStatus: value as any})}
                  disabled={mode === 'view'}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {approvalStatuses.map((status) => {
                      const IconComponent = status.icon;
                      return (
                        <SelectItem key={status.value} value={status.value}>
                          <div className="flex items-center gap-2">
                            <IconComponent className="w-4 h-4" />
                            {status.label}
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="reviewNotes">Review Notes</Label>
              <Textarea
                id="reviewNotes"
                value={formData.reviewNotes || ''}
                onChange={(e) => setFormData({...formData, reviewNotes: e.target.value})}
                placeholder="Add review notes or comments..."
                disabled={mode === 'view'}
                rows={3}
              />
            </div>
          </TabsContent>

          {/* Form Details Tab */}
          <TabsContent value="details" className="space-y-4">
            {formData.formType === 'vendor' && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Business Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="businessName">Business Name</Label>
                      <Input
                        id="businessName"
                        value={businessDetails.businessName}
                        onChange={(e) => setBusinessDetails({...businessDetails, businessName: e.target.value})}
                        disabled={mode === 'view'}
                      />
                    </div>
                    <div>
                      <Label htmlFor="businessType">Business Type</Label>
                      <Input
                        id="businessType"
                        value={businessDetails.businessType}
                        onChange={(e) => setBusinessDetails({...businessDetails, businessType: e.target.value})}
                        disabled={mode === 'view'}
                      />
                    </div>
                    <div>
                      <Label htmlFor="businessCategory">Business Category</Label>
                      <Select 
                        value={businessDetails.businessCategory} 
                        onValueChange={(value) => setBusinessDetails({...businessDetails, businessCategory: value})}
                        disabled={mode === 'view'}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          {businessCategories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category.charAt(0).toUpperCase() + category.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="experienceYears">Experience (Years)</Label>
                      <Input
                        id="experienceYears"
                        type="number"
                        value={businessDetails.experienceYears}
                        onChange={(e) => setBusinessDetails({...businessDetails, experienceYears: parseInt(e.target.value) || 0})}
                        disabled={mode === 'view'}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="businessDescription">Business Description</Label>
                    <Textarea
                      id="businessDescription"
                      value={businessDetails.businessDescription}
                      onChange={(e) => setBusinessDetails({...businessDetails, businessDescription: e.target.value})}
                      disabled={mode === 'view'}
                      rows={3}
                    />
                  </div>
                </CardContent>
              </Card>
            )}

            {formData.formType === 'customer' && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Personal Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">First Name</Label>
                      <Input
                        id="firstName"
                        value={personalDetails.firstName}
                        onChange={(e) => setPersonalDetails({...personalDetails, firstName: e.target.value})}
                        disabled={mode === 'view'}
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input
                        id="lastName"
                        value={personalDetails.lastName}
                        onChange={(e) => setPersonalDetails({...personalDetails, lastName: e.target.value})}
                        disabled={mode === 'view'}
                      />
                    </div>
                    <div>
                      <Label htmlFor="weddingDate">Wedding Date</Label>
                      <Input
                        id="weddingDate"
                        type="date"
                        value={personalDetails.weddingDate}
                        onChange={(e) => setPersonalDetails({...personalDetails, weddingDate: e.target.value})}
                        disabled={mode === 'view'}
                      />
                    </div>
                    <div>
                      <Label htmlFor="budgetRange">Budget Range</Label>
                      <Input
                        id="budgetRange"
                        value={personalDetails.budgetRange}
                        onChange={(e) => setPersonalDetails({...personalDetails, budgetRange: e.target.value})}
                        disabled={mode === 'view'}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {formData.formType === 'admin' && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Admin Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="adminLevel">Admin Level</Label>
                      <Select 
                        value={adminDetails.adminLevel} 
                        onValueChange={(value) => setAdminDetails({...adminDetails, adminLevel: value})}
                        disabled={mode === 'view'}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {adminLevels.map((level) => (
                            <SelectItem key={level.value} value={level.value}>
                              {level.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="designation">Designation</Label>
                      <Input
                        id="designation"
                        value={adminDetails.designation}
                        onChange={(e) => setAdminDetails({...adminDetails, designation: e.target.value})}
                        disabled={mode === 'view'}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Review Tab */}
          <TabsContent value="review" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Form Review Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <strong>Form Type:</strong> {formData.formType}
                  </div>
                  <div>
                    <strong>Submission Date:</strong> {new Date(formData.submissionDate).toLocaleDateString()}
                  </div>
                  <div>
                    <strong>Submitted By:</strong> {formData.submittedBy}
                  </div>
                  <div>
                    <strong>Form Version:</strong> {formData.formVersion}
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex gap-4">
                  {getStatusBadge(formData.verificationStatus, 'verification')}
                  {getStatusBadge(formData.approvalStatus, 'approval')}
                </div>

                {formData.reviewNotes && (
                  <div>
                    <strong>Review Notes:</strong>
                    <p className="mt-1 text-gray-600">{formData.reviewNotes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* History Tab */}
          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Form History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3 text-sm">
                    <Calendar className="w-4 h-4 text-gray-500" />
                    <span>Form created: {new Date(formData.submissionDate).toLocaleString()}</span>
                  </div>
                  {formData.reviewDate && (
                    <div className="flex items-center gap-3 text-sm">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span>Last reviewed: {new Date(formData.reviewDate).toLocaleString()}</span>
                      {formData.reviewedBy && <span>by {formData.reviewedBy}</span>}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {mode !== 'view' && (
          <div className="flex gap-3 pt-6">
            <Button onClick={handleFormSubmit} className="flex-1">
              {mode === 'create' ? 'Create Form' : 'Update Form'}
            </Button>
            <Button variant="outline" onClick={() => window.history.back()}>
              Cancel
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
