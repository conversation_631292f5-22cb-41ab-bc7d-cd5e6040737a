"use client"

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Crown, Shield, Briefcase, User, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import UserRoleService, { UserRole, RoleUpdateResult, UserTypeFormData } from '@/lib/services/userRoleService';
import UserTypeFormManager from './UserTypeFormManager';
import toast from 'react-hot-toast';

interface UserRoleManagerProps {
  targetUserId?: string;
  targetUserEmail?: string;
  currentRole?: UserRole;
  onRoleUpdated?: (result: RoleUpdateResult) => void;
}

export default function UserRoleManager({ 
  targetUserId, 
  targetUserEmail, 
  currentRole = 'customer',
  onRoleUpdated 
}: UserRoleManagerProps) {
  const { user, userType } = useAuth();
  const [selectedRole, setSelectedRole] = useState<UserRole>(currentRole);
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchUserId, setSearchUserId] = useState(targetUserId || '');
  const [searchEmail, setSearchEmail] = useState(targetUserEmail || '');
  const [showFormManager, setShowFormManager] = useState(false);
  const [formData, setFormData] = useState<UserTypeFormData | undefined>();

  const roleOptions = [
    { value: 'customer', label: 'Customer', icon: User, color: 'bg-blue-100 text-blue-800' },
    { value: 'vendor', label: 'Vendor', icon: Briefcase, color: 'bg-green-100 text-green-800' },
    { value: 'admin', label: 'Administrator', icon: Shield, color: 'bg-orange-100 text-orange-800' },
    { value: 'super_admin', label: 'Super Administrator', icon: Crown, color: 'bg-purple-100 text-purple-800' },
  ];

  const getCurrentRoleBadge = (role: UserRole) => {
    const roleConfig = roleOptions.find(r => r.value === role);
    if (!roleConfig) return null;

    const IconComponent = roleConfig.icon;
    return (
      <Badge className={roleConfig.color}>
        <IconComponent className="w-3 h-3 mr-1" />
        {roleConfig.label}
      </Badge>
    );
  };

  const handleRoleUpdate = async () => {
    if (!user?.userId) {
      toast.error('You must be logged in to update user roles');
      return;
    }

    if (!searchUserId) {
      toast.error('Please enter a user ID');
      return;
    }

    if (selectedRole === currentRole) {
      toast.error('Please select a different role');
      return;
    }

    setLoading(true);

    try {
      // Check permissions first
      const canUpdate = await UserRoleService.canUpdateRole(user.userId, selectedRole);
      if (!canUpdate) {
        toast.error('You do not have permission to assign this role');
        setLoading(false);
        return;
      }

      let result: RoleUpdateResult;

      switch (selectedRole) {
        case 'super_admin':
          result = await UserRoleService.updateToSuperAdmin(searchUserId, user.userId, reason);
          break;
        case 'admin':
          result = await UserRoleService.updateToAdmin(searchUserId, user.userId, reason);
          break;
        case 'vendor':
          result = await UserRoleService.updateToVendor(searchUserId, user.userId, reason);
          break;
        case 'customer':
          result = await UserRoleService.updateToCustomer(searchUserId, user.userId, reason);
          break;
        default:
          throw new Error('Invalid role selected');
      }

      if (result.success) {
        toast.success(result.message);
        setReason('');
        onRoleUpdated?.(result);
      } else {
        toast.error(result.message);
      }

    } catch (error) {
      console.error('Error updating user role:', error);
      toast.error('Failed to update user role');
    } finally {
      setLoading(false);
    }
  };

  const isHighPrivilegeRole = (role: UserRole) => {
    return role === 'admin' || role === 'super_admin';
  };

  const canAssignRole = (role: UserRole) => {
    if (userType === 'super_admin') return true;
    if (userType === 'admin' && role !== 'super_admin') return true;
    return false;
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="w-5 h-5" />
          User Role Management
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* User Search Section */}
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="userId">User ID</Label>
              <Input
                id="userId"
                value={searchUserId}
                onChange={(e) => setSearchUserId(e.target.value)}
                placeholder="Enter user ID"
                disabled={!!targetUserId}
              />
            </div>
            <div>
              <Label htmlFor="userEmail">User Email (Reference)</Label>
              <Input
                id="userEmail"
                value={searchEmail}
                onChange={(e) => setSearchEmail(e.target.value)}
                placeholder="Enter user email"
                disabled={!!targetUserEmail}
              />
            </div>
          </div>
        </div>

        {/* Current Role Display */}
        {currentRole && (
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Current Role:</span>
            {getCurrentRoleBadge(currentRole)}
          </div>
        )}

        {/* Role Selection */}
        <div className="space-y-2">
          <Label htmlFor="newRole">New Role</Label>
          <Select value={selectedRole} onValueChange={(value) => setSelectedRole(value as UserRole)}>
            <SelectTrigger>
              <SelectValue placeholder="Select new role" />
            </SelectTrigger>
            <SelectContent>
              {roleOptions.map((role) => {
                const IconComponent = role.icon;
                const canAssign = canAssignRole(role.value as UserRole);
                
                return (
                  <SelectItem 
                    key={role.value} 
                    value={role.value}
                    disabled={!canAssign}
                  >
                    <div className="flex items-center gap-2">
                      <IconComponent className="w-4 h-4" />
                      <span>{role.label}</span>
                      {!canAssign && (
                        <Badge variant="outline" className="text-xs">
                          No Permission
                        </Badge>
                      )}
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>

        {/* Reason for Change */}
        <div className="space-y-2">
          <Label htmlFor="reason">Reason for Role Change</Label>
          <Textarea
            id="reason"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Enter reason for this role change (optional but recommended)"
            rows={3}
          />
        </div>

        {/* Warning for High Privilege Roles */}
        {isHighPrivilegeRole(selectedRole) && (
          <div className="flex items-start gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div className="text-sm">
              <p className="font-medium text-yellow-800">High Privilege Role Warning</p>
              <p className="text-yellow-700">
                You are about to assign {selectedRole === 'super_admin' ? 'Super Administrator' : 'Administrator'} privileges. 
                This will give the user significant control over the platform.
              </p>
            </div>
          </div>
        )}

        {/* Form Management Section */}
        <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between">
            <h3 className="font-medium text-gray-900">User Type Form Management</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFormManager(!showFormManager)}
            >
              {showFormManager ? 'Hide' : 'Show'} Form Details
            </Button>
          </div>

          {showFormManager && (
            <div className="mt-4">
              <UserTypeFormManager
                userId={searchUserId}
                currentFormData={formData}
                onFormSubmit={(data) => {
                  setFormData(data);
                  toast.success('Form data updated');
                }}
                onFormUpdate={(data) => {
                  setFormData(data);
                  toast.success('Form data updated');
                }}
                mode="edit"
              />
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                className="flex-1"
                disabled={loading || !searchUserId || selectedRole === currentRole}
              >
                {loading ? 'Updating...' : 'Update Role'}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Confirm Role Change</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to change this user's role from{' '}
                  <strong>{currentRole}</strong> to <strong>{selectedRole}</strong>?
                  {isHighPrivilegeRole(selectedRole) && (
                    <span className="block mt-2 text-yellow-600 font-medium">
                      This will grant significant administrative privileges.
                    </span>
                  )}
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleRoleUpdate}>
                  Confirm Update
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          <Button
            variant="outline"
            onClick={() => {
              setSelectedRole(currentRole);
              setReason('');
              setFormData(undefined);
            }}
          >
            Reset
          </Button>
        </div>

        {/* Permissions Info */}
        <div className="text-xs text-gray-500 space-y-1">
          <p><strong>Note:</strong> Role changes take effect immediately.</p>
          <p>Only Super Administrators can create other Super Administrators.</p>
          <p>All role changes are logged for security auditing.</p>
        </div>
      </CardContent>
    </Card>
  );
}

// Quick Role Update Buttons Component
export function QuickRoleButtons({ 
  userId, 
  currentRole, 
  onRoleUpdated 
}: { 
  userId: string; 
  currentRole: UserRole; 
  onRoleUpdated?: (result: RoleUpdateResult) => void;
}) {
  const { user, userType } = useAuth();
  const [loading, setLoading] = useState<string | null>(null);

  const handleQuickUpdate = async (newRole: UserRole) => {
    if (!user?.userId) return;

    setLoading(newRole);
    try {
      const canUpdate = await UserRoleService.canUpdateRole(user.userId, newRole);
      if (!canUpdate) {
        toast.error('You do not have permission to assign this role');
        return;
      }

      let result: RoleUpdateResult;
      const reason = `Quick role update to ${newRole}`;

      switch (newRole) {
        case 'super_admin':
          result = await UserRoleService.updateToSuperAdmin(userId, user.userId, reason);
          break;
        case 'admin':
          result = await UserRoleService.updateToAdmin(userId, user.userId, reason);
          break;
        case 'vendor':
          result = await UserRoleService.updateToVendor(userId, user.userId, reason);
          break;
        case 'customer':
          result = await UserRoleService.updateToCustomer(userId, user.userId, reason);
          break;
        default:
          return;
      }

      if (result.success) {
        toast.success(result.message);
        onRoleUpdated?.(result);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error('Failed to update role');
    } finally {
      setLoading(null);
    }
  };

  const quickRoles = [
    { role: 'customer' as UserRole, label: 'Customer', icon: User, color: 'bg-blue-600' },
    { role: 'vendor' as UserRole, label: 'Vendor', icon: Briefcase, color: 'bg-green-600' },
    { role: 'admin' as UserRole, label: 'Admin', icon: Shield, color: 'bg-orange-600' },
    { role: 'super_admin' as UserRole, label: 'Super Admin', icon: Crown, color: 'bg-purple-600' },
  ];

  return (
    <div className="flex gap-1">
      {quickRoles.map((roleConfig) => {
        const IconComponent = roleConfig.icon;
        const isCurrentRole = currentRole === roleConfig.role;
        const canAssign = userType === 'super_admin' || (userType === 'admin' && roleConfig.role !== 'super_admin');
        const isLoading = loading === roleConfig.role;

        return (
          <Button
            key={roleConfig.role}
            size="sm"
            variant={isCurrentRole ? "default" : "outline"}
            className={`${isCurrentRole ? roleConfig.color : ''} text-xs`}
            disabled={isCurrentRole || !canAssign || !!loading}
            onClick={() => handleQuickUpdate(roleConfig.role)}
          >
            <IconComponent className="w-3 h-3 mr-1" />
            {isLoading ? '...' : roleConfig.label}
          </Button>
        );
      })}
    </div>
  );
}
