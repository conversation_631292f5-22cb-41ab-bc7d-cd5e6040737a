"use client"

import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  MessageCircle, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  Calendar,
  Users,
  MapPin,
  DollarSign,
  Clock
} from 'lucide-react'
import { inquiryService, InquiryInput } from '@/lib/services/inquiryService'
import { showToast } from '@/lib/toast'
import { useAuth } from '@/contexts/AuthContext'

interface QuickInquiryFormProps {
  vendorUserId: string
  vendorId: string
  vendorName: string
  vendorCategory?: string
}

export function QuickInquiryForm({ vendorUserId, vendorId, vendorName, vendorCategory }: QuickInquiryFormProps) {
  const { user, isAuthenticated, userProfile } = useAuth()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [formData, setFormData] = useState({
    customerName: '',
    customerEmail: '',
    customerPhone: '',
    eventDate: '',
    message: '',
    inquiryType: 'VENDOR_INQUIRY' as const,
    budget: '',
    guestCount: '',
    venue: '',
    preferredContactTime: ''
  })

  // Pre-fill form with user profile data when authenticated
  React.useEffect(() => {
    if (isAuthenticated && userProfile) {
      setFormData(prev => ({
        ...prev,
        customerName: userProfile.fullName || userProfile.name || prev.customerName,
        customerEmail: userProfile.email || prev.customerEmail,
        customerPhone: userProfile.phone || prev.customerPhone
      }))
    }
  }, [isAuthenticated, userProfile])

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validation
    if (!formData.customerName.trim()) {
      showToast.error('Please enter your name')
      return
    }
    
    if (!formData.customerEmail.trim()) {
      showToast.error('Please enter your email')
      return
    }
    
    if (!formData.message.trim()) {
      showToast.error('Please enter your message')
      return
    }

    setIsSubmitting(true)

    try {
      // Debug: Log form data before submission
      console.log('Form data before submission:', {
        formData,
        inquiryType: formData.inquiryType,
        inquiryTypeType: typeof formData.inquiryType
      });

      const inquiryData: InquiryInput = {
        vendorUserId,
        vendorId,
        vendorName,
        customerUserId: user?.userId || undefined,
        customerName: formData.customerName.trim(),
        customerEmail: formData.customerEmail.trim(),
        customerPhone: formData.customerPhone.trim() || undefined,
        eventDate: formData.eventDate || undefined,
        message: formData.message.trim(),
        inquiryType: formData.inquiryType,
        budget: formData.budget.trim() || undefined,
        guestCount: formData.guestCount.trim() || undefined,
        venue: formData.venue.trim() || undefined,
        preferredContactTime: formData.preferredContactTime.trim() || undefined
      }

      console.log('Inquiry data to be submitted:', inquiryData);

      await inquiryService.createInquiry(inquiryData)
      
      setIsSubmitted(true)
      showToast.success('Inquiry sent successfully! The vendor will contact you soon.')
      
      // Reset form after 3 seconds
      setTimeout(() => {
        setIsSubmitted(false)
        setFormData({
          customerName: '',
          customerEmail: '',
          customerPhone: '',
          eventDate: '',
          message: '',
          inquiryType: 'VENDOR_INQUIRY',
          budget: '',
          guestCount: '',
          venue: '',
          preferredContactTime: ''
        })
      }, 3000)

    } catch (error) {
      console.error('Error submitting inquiry:', error)
      showToast.error('Failed to send inquiry. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isSubmitted) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2 text-green-800">Inquiry Sent!</h3>
            <p className="text-gray-600 mb-4">
              Thank you for your inquiry. {vendorName} will contact you soon.
            </p>
            <Badge className="bg-green-100 text-green-800">
              Response expected within 24 hours
            </Badge>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <MessageCircle className="w-5 h-5 text-primary mr-2" />
            <h3 className="text-xl font-semibold">Quick Inquiry</h3>
          </div>
          {isAuthenticated && (
            <Badge className="bg-green-100 text-green-800">
              <CheckCircle className="w-3 h-3 mr-1" />
              Logged In
            </Badge>
          )}
        </div>

        {isAuthenticated && userProfile && (
          <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
            <p className="text-sm text-blue-800 font-medium">
              Welcome back, {userProfile.fullName || userProfile.name || 'User'}!
            </p>
            <p className="text-xs text-blue-600">
              Your contact details have been pre-filled below.
            </p>
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="space-y-3">
            <Input
              placeholder="Your Name *"
              value={formData.customerName}
              onChange={(e) => handleInputChange('customerName', e.target.value)}
              required
              className="border-gray-300 focus:border-primary"
            />
            
            <Input
              placeholder="Your Email *"
              type="email"
              value={formData.customerEmail}
              onChange={(e) => handleInputChange('customerEmail', e.target.value)}
              required
              className="border-gray-300 focus:border-primary"
            />
            
            <Input
              placeholder="Your Phone"
              type="tel"
              value={formData.customerPhone}
              onChange={(e) => handleInputChange('customerPhone', e.target.value)}
              className="border-gray-300 focus:border-primary"
            />
          </div>

          {/* Event Details */}
          <div className="space-y-3">
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Event Date"
                type="date"
                value={formData.eventDate}
                onChange={(e) => handleInputChange('eventDate', e.target.value)}
                className="pl-10 border-gray-300 focus:border-primary"
              />
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div className="relative">
                <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Guest Count"
                  value={formData.guestCount}
                  onChange={(e) => handleInputChange('guestCount', e.target.value)}
                  className="pl-10 border-gray-300 focus:border-primary"
                />
              </div>
              
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Budget Range"
                  value={formData.budget}
                  onChange={(e) => handleInputChange('budget', e.target.value)}
                  className="pl-10 border-gray-300 focus:border-primary"
                />
              </div>
            </div>

            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Venue/Location"
                value={formData.venue}
                onChange={(e) => handleInputChange('venue', e.target.value)}
                className="pl-10 border-gray-300 focus:border-primary"
              />
            </div>
          </div>

          {/* Inquiry Type */}
          <Select 
            value={formData.inquiryType} 
            onValueChange={(value) => handleInputChange('inquiryType', value)}
          >
            <SelectTrigger className="border-gray-300 focus:border-primary">
              <SelectValue placeholder="Inquiry Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="VENDOR_INQUIRY">General Inquiry</SelectItem>
              <SelectItem value="SERVICE_QUOTE">Service Quote</SelectItem>
              <SelectItem value="AVAILABILITY_CHECK">Check Availability</SelectItem>
              <SelectItem value="GENERAL_QUESTION">General Question</SelectItem>
              <SelectItem value="BOOKING_REQUEST">Booking Request</SelectItem>
            </SelectContent>
          </Select>

          {/* Preferred Contact Time */}
          <div className="relative">
            <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Preferred Contact Time (e.g., Morning, Evening)"
              value={formData.preferredContactTime}
              onChange={(e) => handleInputChange('preferredContactTime', e.target.value)}
              className="pl-10 border-gray-300 focus:border-primary"
            />
          </div>

          {/* Message */}
          <Textarea
            placeholder="Your Message *"
            value={formData.message}
            onChange={(e) => handleInputChange('message', e.target.value)}
            rows={4}
            required
            className="border-gray-300 focus:border-primary resize-none"
          />

          {/* Submit Button */}
          <Button 
            type="submit"
            className="w-full bg-primary hover:bg-primary/90 text-white font-semibold py-3"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Sending Inquiry...
              </>
            ) : (
              <>
                <MessageCircle className="w-4 h-4 mr-2" />
                Send Inquiry
              </>
            )}
          </Button>

          {/* Help Text */}
          <div className="flex items-start gap-2 text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
            <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium text-blue-800">Quick Response Guaranteed</p>
              <p>The vendor typically responds within 2-4 hours during business hours.</p>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

export default QuickInquiryForm
