"use client"

import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  MessageCircle,
  Phone,
  Mail,
  Globe,
  LogIn,
  UserPlus,
  Lock,
  Shield,
  Heart,
  Star,
  CheckCircle,
  Share2,
  Bookmark,
  Calendar
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter, usePathname } from 'next/navigation'
import QuickInquiryForm from './QuickInquiryForm'

interface AuthenticatedContactVendorProps {
  vendor: {
    id: string
    userId: string
    name: string
    category?: string
    contact?: string
    email?: string
    website?: string
    rating?: number
    reviewCount?: number
    priceRange?: string
  }
  showInquiryForm?: boolean
}

export function AuthenticatedContactVendor({ 
  vendor, 
  showInquiryForm = true 
}: AuthenticatedContactVendorProps) {
  const { isAuthenticated, isLoading, user } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [showLoginPrompt, setShowLoginPrompt] = useState(false)

  // Handle contact actions for non-authenticated users
  const handleContactAction = (action: 'call' | 'message' | 'website' | 'book') => {
    if (!isAuthenticated) {
      setShowLoginPrompt(true)
      return
    }

    // Handle authenticated actions
    switch (action) {
      case 'book':
        // Navigate to booking page or open booking modal
        router.push(`/booking/vendor/${vendor.id}`)
        break
      case 'call':
        if (vendor.contact) {
          window.open(`tel:${vendor.contact}`, '_self')
        }
        break
      case 'message':
        // Scroll to inquiry form or open messaging interface
        const inquiryForm = document.getElementById('inquiry-form')
        if (inquiryForm) {
          inquiryForm.scrollIntoView({ behavior: 'smooth' })
        }
        break
      case 'website':
        if (vendor.website) {
          window.open(vendor.website, '_blank')
        }
        break
    }
  }

  // Login prompt component
  const LoginPrompt = () => (
    <Card className="border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-accent/5">
      <CardContent className="p-6">
        <div className="text-center">
          <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Lock className="w-8 h-8 text-primary" />
          </div>
          
          <h3 className="text-xl font-semibold mb-2 text-gray-800">
            Login Required to Contact Vendor
          </h3>
          
          <p className="text-gray-600 mb-6 leading-relaxed">
            To protect our vendors from spam and ensure quality inquiries, 
            you need to be logged in to contact {vendor.name}.
          </p>

          {/* Benefits of logging in */}
          <div className="bg-white rounded-lg p-4 mb-6 text-left">
            <h4 className="font-semibold text-gray-800 mb-3 flex items-center">
              <Shield className="w-4 h-4 mr-2 text-green-600" />
              Benefits of Creating an Account:
            </h4>
            <ul className="space-y-2 text-sm text-gray-600">
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                Direct contact with verified vendors
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                Save favorite vendors and venues
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                Track your inquiries and responses
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                Get personalized recommendations
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                Access exclusive deals and offers
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                Schedule consultations with vendors
              </li>
              <li className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-green-500" />
                Save vendors to favorites list
              </li>
            </ul>
          </div>

          {/* Action buttons */}
          <div className="flex justify-center">
            <Button
              onClick={() => router.push(`/login?redirect=${encodeURIComponent(pathname)}`)}
              className="w-full bg-primary hover:bg-primary/90 text-white font-semibold py-3"
            >
              <LogIn className="w-4 h-4 mr-2" />
              Login to Contact
            </Button>
          </div>

          {/* Quick stats to build trust */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <p className="text-xs text-gray-500 mb-2">Join thousands of happy couples</p>
            <div className="flex justify-center items-center gap-4 text-sm">
              <div className="flex items-center text-gray-600">
                <Heart className="w-4 h-4 mr-1 text-red-500" />
                <span>50,000+ Weddings</span>
              </div>
              <div className="flex items-center text-gray-600">
                <Star className="w-4 h-4 mr-1 text-yellow-500" />
                <span>4.8/5 Rating</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded mb-4"></div>
            <div className="space-y-3">
              <div className="h-12 bg-gray-200 rounded"></div>
              <div className="h-12 bg-gray-200 rounded"></div>
              <div className="h-12 bg-gray-200 rounded"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Show login prompt if not authenticated
  if (!isAuthenticated) {
    return <LoginPrompt />
  }

  // Authenticated user - show full contact options
  return (
    <div className="space-y-6">
      {/* Contact Card */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold">Contact Vendor</h3>
            <Badge className="bg-green-100 text-green-800">
              <CheckCircle className="w-3 h-3 mr-1" />
              Verified
            </Badge>
          </div>
          
          <div className="space-y-4">
            <Button
              onClick={() => handleContactAction('book')}
              className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-bold py-3 shadow-lg hover:shadow-xl transition-all duration-200"
            >
              <Calendar className="h-5 w-5 mr-2" />
              Book Now
            </Button>

            <Button
              onClick={() => handleContactAction('call')}
              className="w-full bg-primary hover:bg-primary/90 text-white font-semibold py-3"
              disabled={!vendor.contact}
            >
              <Phone className="h-5 w-5 mr-2" />
              {vendor.contact ? 'Call Now' : 'Phone Not Available'}
            </Button>

            <Button
              onClick={() => handleContactAction('message')}
              variant="outline"
              className="w-full border-2 border-primary text-primary hover:bg-primary hover:text-white font-semibold py-3"
            >
              <Mail className="h-5 w-5 mr-2" />
              Send Message
            </Button>

            {vendor.website && (
              <Button
                onClick={() => handleContactAction('website')}
                variant="outline"
                className="w-full font-semibold py-3"
              >
                <Globe className="h-5 w-5 mr-2" />
                Visit Website
              </Button>
            )}
          </div>

          {/* Contact Info */}
          <div className="mt-6 pt-6 border-t space-y-3">
            {vendor.contact && (
              <div className="flex items-center text-gray-600">
                <Phone className="h-4 w-4 mr-3 text-primary" />
                <span>{vendor.contact}</span>
              </div>
            )}
            {vendor.email && (
              <div className="flex items-center text-gray-600">
                <Mail className="h-4 w-4 mr-3 text-primary" />
                <span>{vendor.email}</span>
              </div>
            )}
            {vendor.website && (
              <div className="flex items-center text-gray-600">
                <Globe className="h-4 w-4 mr-3 text-primary" />
                <span className="truncate">{vendor.website}</span>
              </div>
            )}
          </div>

          {/* Vendor Stats */}
          <div className="mt-6 pt-4 border-t">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-primary">
                  {vendor.rating || 'N/A'}
                </div>
                <div className="text-sm text-gray-600">Rating</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-primary">
                  {vendor.reviewCount || 0}
                </div>
                <div className="text-sm text-gray-600">Reviews</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions Card */}
      <Card>
        <CardContent className="p-6">
          <h3 className="text-lg font-bold mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <Button
              variant="outline"
              className="w-full justify-start bg-transparent hover:bg-primary hover:text-white"
              onClick={() => {
                // Scroll to inquiry form
                const inquiryForm = document.getElementById('inquiry-form')
                if (inquiryForm) {
                  inquiryForm.scrollIntoView({ behavior: 'smooth' })
                }
              }}
            >
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Consultation
            </Button>

            <Button
              variant="outline"
              className="w-full justify-start bg-transparent hover:bg-primary hover:text-white"
              onClick={() => {
                // Add to favorites functionality
                console.log('Adding vendor to favorites:', vendor.name)
                // You can implement the actual favorites functionality here
              }}
            >
              <Bookmark className="h-4 w-4 mr-2" />
              Save to Favorites
            </Button>

            <Button
              variant="outline"
              className="w-full justify-start bg-transparent hover:bg-primary hover:text-white"
              onClick={() => {
                // Share vendor functionality
                if (navigator.share) {
                  navigator.share({
                    title: vendor.name,
                    text: `Check out this amazing vendor: ${vendor.name}`,
                    url: window.location.href
                  })
                } else {
                  // Fallback: copy to clipboard
                  navigator.clipboard.writeText(window.location.href)
                  console.log('Vendor link copied to clipboard')
                }
              }}
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share Vendor
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick Inquiry Form - Only for authenticated users */}
      {showInquiryForm && (
        <div id="inquiry-form">
          <QuickInquiryForm
            vendorUserId={vendor.userId}
            vendorId={vendor.id}
            vendorName={vendor.name}
            vendorCategory={vendor.category}
          />
        </div>
      )}
    </div>
  )
}

export default AuthenticatedContactVendor
