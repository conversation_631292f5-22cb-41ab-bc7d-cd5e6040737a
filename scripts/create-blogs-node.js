/**
 * Node.js script to create test blogs using Amplify API
 * Run with: node scripts/create-blogs-node.js
 */

const { Amplify } = require('aws-amplify');
const { generateClient } = require('aws-amplify/api');

// Try to load AWS config, fallback to manual configuration if not found
let awsconfig;
try {
  awsconfig = require('../src/aws-exports.js');
} catch (error) {
  console.log('⚠️  aws-exports.js not found, using manual configuration');
  awsconfig = {
    aws_project_region: process.env.AWS_REGION || 'us-east-1',
    aws_appsync_graphqlEndpoint: process.env.GRAPHQL_ENDPOINT,
    aws_appsync_region: process.env.AWS_REGION || 'us-east-1',
    aws_appsync_authenticationType: 'API_KEY',
    aws_appsync_apiKey: process.env.API_KEY,
  };
}

// Configure Amplify
Amplify.configure(awsconfig);

// Create client with API key for public operations
const client = generateClient({
  authMode: 'apiKey'
});

// GraphQL mutation for creating blogs
const createBlogMutation = `
  mutation CreateBlog($input: CreateBlogInput!) {
    createBlog(input: $input) {
      id
      title
      content
      excerpt
      category
      authorId
      authorName
      authorType
      featuredImage
      tags
      status
      views
      likes
      comments
      isPinned
      isFeatured
      publishedAt
      createdAt
      updatedAt
    }
  }
`;

// Test blog data
const testBlogs = [
  {
    title: "10 Essential Wedding Planning Tips for Indian Weddings",
    content: "Planning an Indian wedding can be overwhelming with its rich traditions, multiple ceremonies, and elaborate celebrations. Here are 10 essential tips to make your wedding planning journey smooth and memorable: 1. Start Early - Begin planning at least 8-12 months in advance. 2. Set a Realistic Budget - Allocate funds properly. 3. Choose the Right Venue - Consider capacity and location. 4. Plan the Guest List - Coordinate with both families. 5. Book Vendors Early - Good vendors get booked quickly.",
    excerpt: "Planning an Indian wedding can be overwhelming. Here are the top 10 tips to make your wedding planning journey smooth and memorable.",
    category: "WEDDING_PLANNING",
    authorId: "test-author-1",
    authorName: "Priya Sharma",
    authorType: "EXPERT",
    featuredImage: "/hero_image.png",
    tags: ["wedding planning", "indian wedding", "tips", "guide"],
    status: "PUBLISHED",
    views: 0,
    likes: 0,
    comments: 0,
    isPinned: true,
    isFeatured: true,
    publishedAt: new Date().toISOString()
  },
  {
    title: "How to Choose the Perfect Wedding Venue",
    content: "Selecting the perfect wedding venue is one of the most important decisions you'll make for your big day. Your venue sets the tone for your entire celebration and significantly impacts your budget. Consider your wedding style, capacity needs, location accessibility, and budget constraints when making this crucial decision.",
    excerpt: "Venue selection is crucial for your big day. Here's how to pick the best one for your style and budget.",
    category: "VENUE_SELECTION",
    authorId: "test-author-2",
    authorName: "Anjali Menon",
    authorType: "VENDOR",
    featuredImage: "/mahal.png",
    tags: ["venue selection", "wedding venue", "planning", "budget"],
    status: "PUBLISHED",
    views: 0,
    likes: 0,
    comments: 0,
    isPinned: false,
    isFeatured: true,
    publishedAt: new Date().toISOString()
  },
  {
    title: "Latest Bridal Lehenga Trends for 2024",
    content: "2024 is bringing exciting new trends in bridal fashion, especially for lehengas. Color trends include sage green, dusty pink, royal blue, and ivory with gold. Fabric innovations focus on sustainable materials and lightweight silks. Design elements feature minimalist embroidery, geometric patterns, and cape sleeves.",
    excerpt: "Discover the hottest bridal lehenga trends that are taking the wedding fashion world by storm this year.",
    category: "FASHION_STYLE",
    authorId: "test-author-3",
    authorName: "Kavya Patel",
    authorType: "VENDOR",
    featuredImage: "/wedding_jewels.png",
    tags: ["bridal fashion", "lehenga", "trends", "2024", "style"],
    status: "PUBLISHED",
    views: 0,
    likes: 0,
    comments: 0,
    isPinned: false,
    isFeatured: true,
    publishedAt: new Date().toISOString()
  },
  {
    title: "Capturing Perfect Wedding Memories: Photography Tips",
    content: "Your wedding photos will be treasured for generations. Here are expert tips to ensure your wedding photography captures every precious moment. Pre-wedding planning is crucial - meet with your photographer multiple times, share your vision, and create a comprehensive shot list.",
    excerpt: "Make your wedding album unforgettable with these expert photography tips from professional wedding photographers.",
    category: "PHOTOGRAPHY_VIDEOGRAPHY",
    authorId: "test-author-4",
    authorName: "Suresh Kumar",
    authorType: "VENDOR",
    featuredImage: "/photographer.png",
    tags: ["wedding photography", "tips", "memories", "professional"],
    status: "PUBLISHED",
    views: 0,
    likes: 0,
    comments: 0,
    isPinned: false,
    isFeatured: false,
    publishedAt: new Date().toISOString()
  },
  {
    title: "Top 5 Wedding Catering Trends in 2024",
    content: "Wedding catering is evolving with exciting new trends that focus on experience, sustainability, and personalization. Interactive food stations, sustainable sourcing, fusion cuisine, health-conscious options, and experiential dining are leading the way in 2024.",
    excerpt: "From fusion menus to live food stations, discover the latest trends in wedding catering that will wow your guests.",
    category: "CATERING_FOOD",
    authorId: "test-author-5",
    authorName: "Chef Arjun",
    authorType: "VENDOR",
    featuredImage: "/catering.png",
    tags: ["catering", "food trends", "wedding menu", "2024"],
    status: "PUBLISHED",
    views: 0,
    likes: 0,
    comments: 0,
    isPinned: false,
    isFeatured: false,
    publishedAt: new Date().toISOString()
  },
  {
    title: "Budget-Friendly Wedding Decoration Ideas",
    content: "Creating stunning wedding decorations doesn't have to break the bank. DIY centerpieces, creative lighting, fabric draping, natural elements, and smart repurposing can transform your venue beautifully while staying within budget.",
    excerpt: "Create stunning wedding decorations without breaking the bank. These creative ideas will transform your venue beautifully.",
    category: "DECORATIONS_THEMES",
    authorId: "test-author-6",
    authorName: "Rohit Gupta",
    authorType: "VENDOR",
    featuredImage: "/decorators.png",
    tags: ["decorations", "budget", "DIY", "wedding decor"],
    status: "PUBLISHED",
    views: 0,
    likes: 0,
    comments: 0,
    isPinned: false,
    isFeatured: false,
    publishedAt: new Date().toISOString()
  },
  {
    title: "Wedding Budget Planning: Save Without Sacrificing Style",
    content: "Planning a beautiful wedding on a budget requires smart strategies and creative thinking. Proper budget allocation, money-saving strategies, smart vendor choices, and knowing what costs to watch can help you create your dream wedding affordably.",
    excerpt: "Smart budgeting tips to help you have a beautiful wedding without breaking the bank.",
    category: "BUDGET_FINANCE",
    authorId: "test-author-7",
    authorName: "Meera Joshi",
    authorType: "EXPERT",
    featuredImage: "/return_gift.png",
    tags: ["budget", "wedding planning", "money saving", "finance"],
    status: "PUBLISHED",
    views: 0,
    likes: 0,
    comments: 0,
    isPinned: false,
    isFeatured: false,
    publishedAt: new Date().toISOString()
  },
  {
    title: "Real Wedding: Priya & Raj's Garden Celebration",
    content: "Love was in the air as Priya and Raj celebrated their union in a breathtaking garden wedding that perfectly blended tradition with modern elegance. Their celebration featured sage green and ivory colors, heritage venue, and personalized touches.",
    excerpt: "Follow Priya and Raj's journey as they plan their magical garden wedding, complete with budget breakdown and vendor tips.",
    category: "REAL_WEDDINGS",
    authorId: "test-author-8",
    authorName: "Wedding Stories Team",
    authorType: "ADMIN",
    featuredImage: "/hero_image.png",
    tags: ["real wedding", "garden wedding", "budget breakdown", "inspiration"],
    status: "PUBLISHED",
    views: 0,
    likes: 0,
    comments: 0,
    isPinned: false,
    isFeatured: false,
    publishedAt: new Date().toISOString()
  },
  {
    title: "Expert Tips: 5 Common Wedding Planning Mistakes",
    content: "After planning hundreds of weddings, we've identified the top 5 mistakes couples make: not setting realistic budgets, booking vendors too late, ignoring guest experience, micromanaging details, and forgetting about the marriage itself.",
    excerpt: "Learn from wedding planning experts about the top 5 mistakes couples make and how to avoid them for a stress-free celebration.",
    category: "EXPERT_TIPS",
    authorId: "test-author-9",
    authorName: "Dr. Sunita Reddy",
    authorType: "EXPERT",
    featuredImage: "/placeholder.jpg",
    tags: ["expert tips", "wedding planning", "mistakes", "advice"],
    status: "PUBLISHED",
    views: 0,
    likes: 0,
    comments: 0,
    isPinned: true,
    isFeatured: false,
    publishedAt: new Date().toISOString()
  },
  {
    title: "Vendor Spotlight: Mumbai's Top Wedding Photographer",
    content: "Meet Arjun Shah, one of Mumbai's most sought-after wedding photographers. With over 8 years of experience and 500+ weddings captured, Arjun specializes in photojournalistic and fine art photography with signature techniques.",
    excerpt: "Meet Arjun Shah, Mumbai's top wedding photographer, as he shares his journey, style, and tips for capturing perfect wedding moments.",
    category: "VENDOR_SPOTLIGHT",
    authorId: "test-author-10",
    authorName: "Editorial Team",
    authorType: "ADMIN",
    featuredImage: "/photographer.png",
    tags: ["vendor spotlight", "photographer", "Mumbai", "wedding photography"],
    status: "PUBLISHED",
    views: 0,
    likes: 0,
    comments: 0,
    isPinned: false,
    isFeatured: true,
    publishedAt: new Date().toISOString()
  }
];

// Function to create a single blog
async function createBlog(blogData) {
  try {
    const result = await client.graphql({
      query: createBlogMutation,
      variables: {
        input: blogData
      }
    });
    return result.data.createBlog;
  } catch (error) {
    console.error('Error creating blog:', error);
    throw error;
  }
}

// Function to create all test blogs
async function createAllBlogs() {
  console.log('🚀 Starting to create 10 test blogs...\n');
  
  const results = [];
  
  for (let i = 0; i < testBlogs.length; i++) {
    const blog = testBlogs[i];
    console.log(`📝 Creating blog ${i + 1}/10: "${blog.title}"`);
    
    try {
      const createdBlog = await createBlog(blog);
      console.log(`✅ Successfully created blog with ID: ${createdBlog.id}`);
      results.push({ success: true, blog: createdBlog });
    } catch (error) {
      console.error(`❌ Failed to create blog "${blog.title}":`, error.message);
      results.push({ success: false, error: error.message, title: blog.title });
    }
    
    // Add delay between requests to avoid rate limiting
    if (i < testBlogs.length - 1) {
      console.log('⏳ Waiting 2 seconds before next blog...\n');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  // Summary
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log('\n🎉 Blog creation completed!');
  console.log(`✅ Successfully created: ${successful} blogs`);
  console.log(`❌ Failed to create: ${failed} blogs`);
  
  if (failed > 0) {
    console.log('\nFailed blogs:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`- ${r.title}: ${r.error}`);
    });
  }
  
  return results;
}

// Run the script
if (require.main === module) {
  createAllBlogs()
    .then(() => {
      console.log('\n✨ All done! Check your blog management dashboard to see the created blogs.');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createAllBlogs, testBlogs };
