/**
 * Browser-compatible test data seeding script
 * Run this in the browser console when logged in to seed test data
 */

// Import required functions (these should be available in the browser)
import { generateClient } from 'aws-amplify/api';
import { createVendor, createShop, createVenue } from '../src/graphql/mutations.js';

const client = generateClient({ authMode: 'userPool' });

// Simplified test data for browser execution
const testData = {
  shops: [
    {
      userId: "test-user-1",
      name: "Designer Bridal Lehenga - Royal Red",
      category: "Bridal Wear",
      price: 45000,
      originalPrice: 55000,
      discount: 18,
      stock: 5,
      sku: "BL-RR-001",
      brand: "Royal Threads",
      featured: true,
      description: "Exquisite handcrafted bridal lehenga with intricate zardozi work.",
      features: ["Hand-embroidered zardozi work", "Premium silk fabric", "Custom fitting available"],
      sizes: ["S", "M", "L", "XL"],
      colors: ["Red", "Maroon"],
      images: ["https://images.unsplash.com/photo-1583391733956-6c78276477e2?w=500"],
      specifications: {
        fabric: "Pure Silk with Zardozi Embroidery",
        work: "Hand Embroidered",
        occasion: "Wedding, Reception",
        care: "Dry Clean Only",
        delivery: "7-10 business days",
        returnPolicy: "15 days return policy"
      },
      rating: 4.8,
      reviewCount: 24,
      inStock: true,
      status: "active"
    },
    {
      userId: "test-user-1",
      name: "Groom's Sherwani Set - Golden Elegance",
      category: "Groom Wear",
      price: 25000,
      originalPrice: 30000,
      discount: 17,
      stock: 8,
      sku: "GS-GE-002",
      brand: "Maharaja Collection",
      featured: true,
      description: "Elegant golden sherwani with matching churidar and dupatta.",
      features: ["Premium brocade fabric", "Custom tailoring available"],
      sizes: ["S", "M", "L", "XL"],
      colors: ["Golden", "Cream Gold"],
      images: ["https://images.unsplash.com/photo-1506629905607-c28b47d3e7b0?w=500"],
      specifications: {
        fabric: "Silk Brocade",
        work: "Machine Embroidered",
        occasion: "Wedding, Engagement",
        care: "Dry Clean Only",
        delivery: "5-7 business days",
        returnPolicy: "10 days return policy"
      },
      rating: 4.6,
      reviewCount: 18,
      inStock: true,
      status: "active"
    },
    {
      userId: "test-user-1",
      name: "Wedding Jewelry Set - Kundan Collection",
      category: "Jewelry",
      price: 35000,
      originalPrice: 42000,
      discount: 17,
      stock: 3,
      sku: "WJ-KC-003",
      brand: "Heritage Jewels",
      featured: false,
      description: "Traditional Kundan jewelry set with necklace, earrings, and maang tikka.",
      features: ["Authentic Kundan work", "Gold-plated base", "Certificate of authenticity"],
      sizes: ["One Size"],
      colors: ["Gold", "Antique Gold"],
      images: ["https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=500"],
      specifications: {
        fabric: "Gold Plated Metal",
        work: "Kundan Setting",
        occasion: "Wedding, Festival",
        care: "Store in jewelry box, avoid moisture",
        delivery: "3-5 business days",
        returnPolicy: "7 days return policy"
      },
      rating: 4.9,
      reviewCount: 12,
      inStock: true,
      status: "active"
    }
  ],
  
  vendors: [
    {
      userId: "test-user-2",
      name: "Elegant Moments Photography",
      category: "Wedding Photography",
      description: "Professional wedding photography services with cinematic videography.",
      contact: "+91 98765 43210",
      email: "<EMAIL>",
      address: "123 Photography Street",
      city: "Chennai",
      state: "Tamil Nadu",
      pincode: "600001",
      website: "www.elegantmoments.com",
      profilePhoto: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300",
      gallery: ["https://images.unsplash.com/photo-1519741497674-611481863552?w=500"],
      services: [
        {
          name: "Wedding Photography Package",
          price: "₹75,000"
        },
        {
          name: "Pre-Wedding Shoot",
          price: "₹25,000"
        }
      ],
      socialMedia: {
        facebook: "elegantmomentsphotography",
        instagram: "elegant_moments_photo",
        youtube: "elegantmomentsstudio"
      },
      experience: "8+ years",
      events: "200+ weddings",
      responseTime: "Within 2 hours",
      rating: 4.8,
      reviewCount: 45,
      verified: true,
      featured: true,
      availability: "Available",
      priceRange: "₹25,000 - ₹1,50,000",
      specializations: ["Wedding Photography", "Pre-Wedding Shoots"],
      awards: ["Best Wedding Photographer 2023"],
      languages: ["English", "Tamil", "Hindi"],
      coverage: ["Chennai", "Bangalore"],
      equipment: ["Canon 5D Mark IV", "Sony A7R IV"],
      status: "active"
    },
    {
      userId: "test-user-2",
      name: "Spice Garden Catering",
      category: "Catering Services",
      description: "Premium catering services specializing in South Indian and North Indian cuisines.",
      contact: "+91 87654 32109",
      email: "<EMAIL>",
      address: "456 Catering Complex",
      city: "Bangalore",
      state: "Karnataka",
      pincode: "560001",
      website: "www.spicegardencatering.com",
      profilePhoto: "https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=300",
      gallery: ["https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=500"],
      services: [
        {
          name: "Wedding Feast Package",
          price: "₹800 per person"
        },
        {
          name: "Reception Catering",
          price: "₹600 per person"
        }
      ],
      socialMedia: {
        facebook: "spicegardencatering",
        instagram: "spice_garden_catering",
        youtube: "spicegardenofficial"
      },
      experience: "12+ years",
      events: "500+ events",
      responseTime: "Within 1 hour",
      rating: 4.7,
      reviewCount: 78,
      verified: true,
      featured: true,
      availability: "Available",
      priceRange: "₹400 - ₹1,200 per person",
      specializations: ["South Indian Cuisine", "North Indian Cuisine"],
      awards: ["Best Catering Service 2022"],
      languages: ["English", "Kannada", "Tamil"],
      coverage: ["Bangalore", "Mysore"],
      equipment: ["Mobile Kitchen Units", "Serving Equipment"],
      status: "active"
    }
  ],
  
  venues: [
    {
      userId: "test-user-3",
      name: "Grand Palace Banquet Hall",
      description: "Luxurious banquet hall perfect for grand wedding celebrations.",
      type: "Banquet Hall",
      capacity: 500,
      location: "Anna Nagar",
      city: "Chennai",
      state: "Tamil Nadu",
      fullAddress: "123 Grand Palace Complex, Anna Nagar, Chennai - 600040",
      pincode: "600040",
      contactPhone: "+91 98765 12345",
      contactEmail: "<EMAIL>",
      website: "www.grandpalacebanquet.com",
      price: 150000,
      priceRange: "₹1,00,000 - ₹2,50,000",
      images: ["https://images.unsplash.com/photo-1519167758481-83f29c8a4e0a?w=500"],
      amenities: ["Air Conditioning", "Parking for 200 cars", "Bridal Room", "Catering Kitchen"],
      spaces: [
        {
          name: "Main Hall",
          capacity: 500,
          area: "5000 sq ft",
          price: 100000,
          description: "Spacious main hall with elegant chandeliers",
          amenities: ["AC", "Sound System", "Stage"],
          images: ["https://images.unsplash.com/photo-1519167758481-83f29c8a4e0a?w=500"]
        }
      ],
      packages: [
        {
          name: "Premium Wedding Package",
          price: 200000,
          duration: "Full Day",
          description: "Complete wedding package with decoration and catering",
          includes: ["Venue", "Decoration", "Catering", "Sound System"],
          excludes: ["Photography", "Transportation"],
          terms: "50% advance required"
        }
      ],
      socialMedia: {
        facebook: "grandpalacebanquet",
        instagram: "grand_palace_venue",
        youtube: "grandpalaceofficial"
      },
      rating: 4.7,
      reviewCount: 56,
      bookings: 120,
      verified: true,
      featured: true,
      status: "active",
      availability: "Available",
      policies: {
        cancellation: "48 hours notice required",
        advance: "50% advance payment required",
        catering: "Outside catering allowed with permission",
        decoration: "Decoration team available",
        alcohol: "Alcohol permitted with license",
        music: "Music allowed till 11 PM",
        parking: "Complimentary valet parking"
      },
      coordinates: {
        latitude: 13.0827,
        longitude: 80.2707
      },
      operatingHours: {
        monday: "9 AM - 11 PM",
        tuesday: "9 AM - 11 PM",
        wednesday: "9 AM - 11 PM",
        thursday: "9 AM - 11 PM",
        friday: "9 AM - 11 PM",
        saturday: "9 AM - 11 PM",
        sunday: "9 AM - 11 PM"
      }
    },
    {
      userId: "test-user-3",
      name: "Seaside Resort & Convention Center",
      description: "Beautiful beachside resort perfect for destination weddings.",
      type: "Resort",
      capacity: 300,
      location: "ECR",
      city: "Chennai",
      state: "Tamil Nadu",
      fullAddress: "456 East Coast Road, Mahabalipuram, Chennai - 603104",
      pincode: "603104",
      contactPhone: "+91 87654 32109",
      contactEmail: "<EMAIL>",
      website: "www.seasideresort.com",
      price: 200000,
      priceRange: "₹1,50,000 - ₹3,50,000",
      images: ["https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=500"],
      amenities: ["Beach Access", "Swimming Pool", "Spa Services", "Guest Rooms"],
      spaces: [
        {
          name: "Beachside Pavilion",
          capacity: 200,
          area: "3000 sq ft",
          price: 120000,
          description: "Open-air pavilion with ocean views",
          amenities: ["Ocean View", "Natural Lighting", "Beach Access"],
          images: ["https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=500"]
        }
      ],
      packages: [
        {
          name: "Destination Wedding Package",
          price: 300000,
          duration: "2 Days",
          description: "Complete destination wedding with accommodation",
          includes: ["Venue", "Accommodation", "Meals", "Decoration"],
          excludes: ["Transportation", "Photography"],
          terms: "60% advance required"
        }
      ],
      socialMedia: {
        facebook: "seasideresortchennai",
        instagram: "seaside_resort_ecr",
        youtube: "seasideresortofficial"
      },
      rating: 4.8,
      reviewCount: 34,
      bookings: 45,
      verified: true,
      featured: true,
      status: "active",
      availability: "Available",
      policies: {
        cancellation: "72 hours notice required",
        advance: "60% advance payment required",
        catering: "In-house catering only",
        decoration: "Decoration team available",
        alcohol: "Alcohol permitted",
        music: "Music allowed till 12 AM",
        parking: "Complimentary parking available"
      },
      coordinates: {
        latitude: 12.7767,
        longitude: 80.1962
      },
      operatingHours: {
        monday: "24 Hours",
        tuesday: "24 Hours",
        wednesday: "24 Hours",
        thursday: "24 Hours",
        friday: "24 Hours",
        saturday: "24 Hours",
        sunday: "24 Hours"
      }
    }
  ]
};

// Browser seeding function
window.seedTestData = async function() {
  console.log('🚀 Starting browser test data seeding...');
  
  const results = { shops: [], vendors: [], venues: [] };
  
  try {
    // Seed shops
    console.log('🛍️ Creating shop products...');
    for (const shop of testData.shops) {
      try {
        const result = await client.graphql({
          query: createShop,
          variables: { input: shop }
        });
        results.shops.push({ success: true, name: shop.name, id: result.data.createShop.id });
        console.log(`✅ Created shop: ${shop.name}`);
      } catch (error) {
        results.shops.push({ success: false, name: shop.name, error: error.message });
        console.error(`❌ Failed to create shop: ${shop.name}`, error);
      }
    }
    
    // Seed vendors
    console.log('🎯 Creating vendor services...');
    for (const vendor of testData.vendors) {
      try {
        const result = await client.graphql({
          query: createVendor,
          variables: { input: vendor }
        });
        results.vendors.push({ success: true, name: vendor.name, id: result.data.createVendor.id });
        console.log(`✅ Created vendor: ${vendor.name}`);
      } catch (error) {
        results.vendors.push({ success: false, name: vendor.name, error: error.message });
        console.error(`❌ Failed to create vendor: ${vendor.name}`, error);
      }
    }
    
    // Seed venues
    console.log('🏛️ Creating venue listings...');
    for (const venue of testData.venues) {
      try {
        const result = await client.graphql({
          query: createVenue,
          variables: { input: venue }
        });
        results.venues.push({ success: true, name: venue.name, id: result.data.createVenue.id });
        console.log(`✅ Created venue: ${venue.name}`);
      } catch (error) {
        results.venues.push({ success: false, name: venue.name, error: error.message });
        console.error(`❌ Failed to create venue: ${venue.name}`, error);
      }
    }
    
    // Summary
    const total = results.shops.length + results.vendors.length + results.venues.length;
    const successful = [...results.shops, ...results.vendors, ...results.venues].filter(r => r.success).length;
    
    console.log('\n📊 SEEDING SUMMARY:');
    console.log(`Total: ${total}, Successful: ${successful}, Failed: ${total - successful}`);
    console.log(`Shops: ${results.shops.filter(r => r.success).length}/${results.shops.length}`);
    console.log(`Vendors: ${results.vendors.filter(r => r.success).length}/${results.vendors.length}`);
    console.log(`Venues: ${results.venues.filter(r => r.success).length}/${results.venues.length}`);
    console.log('🎉 Test data seeding completed!');
    
    return results;
    
  } catch (error) {
    console.error('💥 Fatal error during seeding:', error);
    return { error: error.message };
  }
};

console.log('📝 Browser seeding script loaded! Run seedTestData() to start seeding.');
