// State-specific vendor categories and specialties
export interface StateSpecificVendor {
  category: string
  subcategories: string[]
  specialties: string[]
  culturalItems?: string[]
  popularServices?: string[]
}

export interface StateVendorConfig {
  stateCode: string
  stateName: string
  language: string
  vendors: StateSpecificVendor[]
  popularVenues: string[]
  traditionalWear: {
    bridal: string[]
    groom: string[]
  }
  culturalSpecialties: string[]
}

export const STATE_VENDOR_CONFIGS: StateVendorConfig[] = [
  {
    stateCode: 'TN',
    stateName: 'Tamil Nadu',
    language: 'Tamil',
    vendors: [
      {
        category: 'Photography',
        subcategories: ['Wedding Photographers', 'Pre-Wedding Photographers', 'Candid Photography'],
        specialties: ['Traditional Tamil Wedding Photography', 'Temple Wedding Photography', 'Brahmin Wedding Photography'],
        popularServices: ['Muhurtham Photography', 'Reception Photography', 'Engagement Photography']
      },
      {
        category: 'Makeup & Beauty',
        subcategories: ['Bridal Makeup', 'Family Makeup', 'Groom Grooming'],
        specialties: ['Traditional Tamil Bridal Makeup', 'South Indian Bridal Look', 'Temple Jewelry Styling'],
        culturalItems: ['Maang Tikka Styling', 'Traditional Hair Decoration', 'Jasmine Flower Arrangement']
      },
      {
        category: 'Traditional Wear',
        subcategories: ['Bridal Sarees', 'Silk Sarees', 'Lehengas'],
        specialties: ['Kanchipuram Silk Sarees', 'Pattu Sarees', 'Traditional Borders'],
        culturalItems: ['Temple Jewelry', 'Gold Jewelry', 'Traditional Blouse Designs']
      },
      {
        category: 'Music & Entertainment',
        subcategories: ['Nadaswaram Artists', 'Classical Musicians', 'Folk Dancers'],
        specialties: ['Carnatic Music', 'Bharatanatyam Performances', 'Traditional Tamil Songs'],
        culturalItems: ['Thavil Players', 'Shehnai Artists', 'Traditional Band']
      },
      {
        category: 'Catering',
        subcategories: ['Traditional Tamil Catering', 'Brahmin Catering', 'Multi-Cuisine'],
        specialties: ['Banana Leaf Meals', 'Traditional Sweets', 'Filter Coffee Service'],
        culturalItems: ['Sambar & Rasam', 'Payasam Varieties', 'Traditional Snacks']
      }
    ],
    popularVenues: ['Kalyana Mandapam', 'Temple Halls', 'Heritage Hotels', 'Beach Resorts'],
    traditionalWear: {
      bridal: ['Kanchipuram Sarees', 'Pattu Pavadai', 'Traditional Blouses', 'Temple Jewelry'],
      groom: ['Silk Dhoti', 'Traditional Shirt', 'Angavastram', 'Traditional Footwear']
    },
    culturalSpecialties: ['Muhurtham Timing', 'Traditional Rituals', 'Tamil Customs', 'Temple Ceremonies']
  },
  {
    stateCode: 'KA',
    stateName: 'Karnataka',
    language: 'Kannada',
    vendors: [
      {
        category: 'Photography',
        subcategories: ['Wedding Photographers', 'Pre-Wedding Photographers', 'Traditional Photography'],
        specialties: ['Mysore Palace Style Photography', 'Traditional Kannada Wedding Photography', 'Heritage Photography'],
        popularServices: ['Engagement Photography', 'Haldi Ceremony Photography', 'Reception Photography']
      },
      {
        category: 'Makeup & Beauty',
        subcategories: ['Bridal Makeup', 'Traditional Makeup', 'Groom Grooming'],
        specialties: ['Mysore Traditional Bridal Look', 'Karnataka Bridal Makeup', 'Traditional Hair Styling'],
        culturalItems: ['Traditional Jewelry Styling', 'Flower Decoration', 'Mysore Silk Draping']
      },
      {
        category: 'Traditional Wear',
        subcategories: ['Mysore Silk Sarees', 'Traditional Lehengas', 'Groom Wear'],
        specialties: ['Mysore Silk', 'Ilkal Sarees', 'Traditional Designs'],
        culturalItems: ['Gold Jewelry', 'Traditional Accessories', 'Mysore Peta']
      },
      {
        category: 'Music & Entertainment',
        subcategories: ['Classical Musicians', 'Folk Artists', 'Traditional Dancers'],
        specialties: ['Carnatic Music', 'Yakshagana Performances', 'Traditional Kannada Songs'],
        culturalItems: ['Dollu Kunitha', 'Traditional Instruments', 'Folk Performances']
      }
    ],
    popularVenues: ['Palace Venues', 'Heritage Hotels', 'Garden Venues', 'Traditional Halls'],
    traditionalWear: {
      bridal: ['Mysore Silk Sarees', 'Traditional Jewelry', 'Ilkal Sarees', 'Traditional Blouses'],
      groom: ['Silk Dhoti', 'Traditional Kurta', 'Mysore Peta', 'Traditional Accessories']
    },
    culturalSpecialties: ['Traditional Rituals', 'Kannada Customs', 'Mysore Traditions', 'Heritage Ceremonies']
  },
  {
    stateCode: 'KL',
    stateName: 'Kerala',
    language: 'Malayalam',
    vendors: [
      {
        category: 'Photography',
        subcategories: ['Wedding Photographers', 'Backwater Photography', 'Traditional Photography'],
        specialties: ['Kerala Traditional Wedding Photography', 'Backwater Wedding Photography', 'Coconut Grove Photography'],
        popularServices: ['Houseboat Photography', 'Beach Wedding Photography', 'Traditional Ceremony Photography']
      },
      {
        category: 'Makeup & Beauty',
        subcategories: ['Bridal Makeup', 'Traditional Kerala Makeup', 'Ayurvedic Beauty'],
        specialties: ['Kerala Traditional Bridal Look', 'Kasavu Saree Styling', 'Natural Makeup'],
        culturalItems: ['Traditional Hair Oiling', 'Flower Decoration', 'Ayurvedic Treatments']
      },
      {
        category: 'Traditional Wear',
        subcategories: ['Kasavu Sarees', 'Traditional Mundu', 'Kerala Jewelry'],
        specialties: ['Kasavu Sarees', 'Traditional Gold Jewelry', 'Kerala Designs'],
        culturalItems: ['Traditional Gold Sets', 'Kasavu Borders', 'Kerala Accessories']
      }
    ],
    popularVenues: ['Backwater Venues', 'Beach Resorts', 'Traditional Homes', 'Heritage Properties'],
    traditionalWear: {
      bridal: ['Kasavu Sarees', 'Traditional Gold Jewelry', 'Kerala Blouses', 'Traditional Accessories'],
      groom: ['Kasavu Mundu', 'Traditional Shirt', 'Kerala Accessories', 'Traditional Footwear']
    },
    culturalSpecialties: ['Traditional Rituals', 'Malayalam Customs', 'Backwater Traditions', 'Ayurvedic Ceremonies']
  },
  {
    stateCode: 'AP',
    stateName: 'Andhra Pradesh',
    language: 'Telugu',
    vendors: [
      {
        category: 'Photography',
        subcategories: ['Wedding Photographers', 'Traditional Photography', 'Temple Photography'],
        specialties: ['Telugu Traditional Wedding Photography', 'Temple Wedding Photography', 'Heritage Photography'],
        popularServices: ['Pellikuthuru Photography', 'Engagement Photography', 'Reception Photography']
      },
      {
        category: 'Makeup & Beauty',
        subcategories: ['Bridal Makeup', 'Traditional Telugu Makeup', 'Family Makeup'],
        specialties: ['Telugu Bridal Look', 'Traditional Jewelry Styling', 'Temple Makeup'],
        culturalItems: ['Traditional Hair Styling', 'Flower Decoration', 'Gold Jewelry Styling']
      }
    ],
    popularVenues: ['Temple Venues', 'Heritage Hotels', 'Traditional Halls', 'Palace Venues'],
    traditionalWear: {
      bridal: ['Pochampally Sarees', 'Traditional Jewelry', 'Telugu Blouses', 'Traditional Accessories'],
      groom: ['Silk Dhoti', 'Traditional Kurta', 'Telugu Accessories', 'Traditional Footwear']
    },
    culturalSpecialties: ['Traditional Rituals', 'Telugu Customs', 'Temple Traditions', 'Heritage Ceremonies']
  },
  {
    stateCode: 'MH',
    stateName: 'Maharashtra',
    language: 'Marathi',
    vendors: [
      {
        category: 'Photography',
        subcategories: ['Wedding Photographers', 'Traditional Photography', 'Destination Photography'],
        specialties: ['Marathi Traditional Wedding Photography', 'Maharashtrian Ceremony Photography', 'Heritage Photography'],
        popularServices: ['Haldi Photography', 'Engagement Photography', 'Reception Photography']
      },
      {
        category: 'Makeup & Beauty',
        subcategories: ['Bridal Makeup', 'Traditional Marathi Makeup', 'Family Makeup'],
        specialties: ['Maharashtrian Bridal Look', 'Traditional Jewelry Styling', 'Nauvari Saree Styling'],
        culturalItems: ['Traditional Hair Styling', 'Flower Decoration', 'Maharashtrian Jewelry']
      }
    ],
    popularVenues: ['Heritage Venues', 'Palace Hotels', 'Traditional Halls', 'Garden Venues'],
    traditionalWear: {
      bridal: ['Nauvari Sarees', 'Paithani Sarees', 'Traditional Jewelry', 'Maharashtrian Blouses'],
      groom: ['Dhoti Kurta', 'Traditional Accessories', 'Maharashtrian Footwear', 'Traditional Headwear']
    },
    culturalSpecialties: ['Traditional Rituals', 'Marathi Customs', 'Maharashtrian Traditions', 'Heritage Ceremonies']
  },
  {
    stateCode: 'GJ',
    stateName: 'Gujarat',
    language: 'Gujarati',
    vendors: [
      {
        category: 'Photography',
        subcategories: ['Wedding Photographers', 'Traditional Photography', 'Destination Photography'],
        specialties: ['Gujarati Traditional Wedding Photography', 'Heritage Photography', 'Cultural Photography'],
        popularServices: ['Garba Photography', 'Engagement Photography', 'Reception Photography']
      },
      {
        category: 'Makeup & Beauty',
        subcategories: ['Bridal Makeup', 'Traditional Gujarati Makeup', 'Family Makeup'],
        specialties: ['Gujarati Bridal Look', 'Traditional Jewelry Styling', 'Cultural Makeup'],
        culturalItems: ['Traditional Hair Styling', 'Flower Decoration', 'Gujarati Jewelry']
      }
    ],
    popularVenues: ['Heritage Venues', 'Palace Hotels', 'Traditional Halls', 'Garden Venues'],
    traditionalWear: {
      bridal: ['Gujarati Sarees', 'Traditional Jewelry', 'Gujarati Blouses', 'Traditional Accessories'],
      groom: ['Dhoti Kurta', 'Traditional Accessories', 'Gujarati Footwear', 'Traditional Headwear']
    },
    culturalSpecialties: ['Traditional Rituals', 'Gujarati Customs', 'Cultural Traditions', 'Heritage Ceremonies']
  }
]

export function getVendorsForState(stateCode: string): StateVendorConfig | null {
  return STATE_VENDOR_CONFIGS.find(config => config.stateCode === stateCode) || null
}

export function getPopularServicesForState(stateCode: string, category: string): string[] {
  const stateConfig = getVendorsForState(stateCode)
  if (!stateConfig) return []
  
  const vendor = stateConfig.vendors.find(v => v.category === category)
  return vendor?.popularServices || []
}

export function getCulturalSpecialtiesForState(stateCode: string, category: string): string[] {
  const stateConfig = getVendorsForState(stateCode)
  if (!stateConfig) return []
  
  const vendor = stateConfig.vendors.find(v => v.category === category)
  return vendor?.culturalItems || []
}
