'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, SelectGroup, SelectLabel } from "@/components/ui/select"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Search, MapPin, Calendar, Users, Heart, Star, Camera, Music, Utensils, Palette, ChevronLeft, ChevronRight, X } from "lucide-react"
import Image from "next/image"
import { LayoutWrapper } from "@/components/layout-wrapper"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useRef, useState, useEffect } from "react"
import { TranslatedText, TranslatedHeading } from '@/components/translation-wrapper'
import { ClientOnly } from '@/components/client-only'
import { useSafeTranslation } from '@/hooks/use-safe-translation'
import { useStateContext, INDIAN_STATES } from '@/contexts/StateContext'
import { FeaturedShopProducts } from '@/components/FeaturedShopProducts'
import { Carousel } from '@/components/ui/carousel'
import { SimpleSEO } from '@/components/seo/SimpleSEO'
import { PopularDestinationsAd } from '@/components/PopularDestinationsAd'
import NewsletterSubscription from '@/components/newsletter/NewsletterSubscription'

const serviceButtonClass = "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 w-full md:w-auto";

export default function HomePage() {
  const router = useRouter();
  const carouselRef = useRef<HTMLDivElement>(null);
  const servicesCarouselRef = useRef<HTMLDivElement>(null);
  const { t } = useSafeTranslation();
  const { selectedState } = useStateContext();

  // Search state
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLocation, setSelectedLocation] = useState("");
  const [locationInput, setLocationInput] = useState("");
  const [showLocationSuggestions, setShowLocationSuggestions] = useState(false);
  const [filteredCities, setFilteredCities] = useState<Array<{name: string, state: string}>>([]);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [isSearching, setIsSearching] = useState(false);
  const locationInputRef = useRef<HTMLInputElement>(null);

  // Create a flat list of all cities for searching
  const allCities = INDIAN_STATES.flatMap(state =>
    state.cities.map(city => ({
      name: t(city.nameKey, city.nameKey.split('.').pop()),
      state: state.name
    }))
  );



  // Handle location input change and filtering
  const handleLocationInputChange = (value: string) => {
    setLocationInput(value);
    setSelectedSuggestionIndex(-1); // Reset selection

    if (value.length > 0) {
      const filtered = allCities.filter(city =>
        city.name.toLowerCase().includes(value.toLowerCase()) ||
        city.state.toLowerCase().includes(value.toLowerCase())
      ).slice(0, 8); // Limit to 8 suggestions

      setFilteredCities(filtered);
      setShowLocationSuggestions(true);
    } else {
      setFilteredCities([]);
      setShowLocationSuggestions(false);
    }
  };

  // Handle keyboard navigation in location suggestions
  const handleLocationKeyDown = (e: React.KeyboardEvent) => {
    if (!showLocationSuggestions || filteredCities.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev =>
          prev < filteredCities.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedSuggestionIndex >= 0 && selectedSuggestionIndex < filteredCities.length) {
          handleLocationSelect(filteredCities[selectedSuggestionIndex].name);
        } else if (locationInput) {
          handleLocationSelect(locationInput);
        }
        break;
      case 'Escape':
        setShowLocationSuggestions(false);
        setSelectedSuggestionIndex(-1);
        break;
    }
  };

  // Handle location selection from suggestions
  const handleLocationSelect = (cityName: string) => {
    setLocationInput(cityName);
    setSelectedLocation(cityName);
    setShowLocationSuggestions(false);
  };

  // Handle location input blur
  const handleLocationBlur = () => {
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => {
      setShowLocationSuggestions(false);
      // If user typed a value, use it as selected location
      if (locationInput && !selectedLocation) {
        setSelectedLocation(locationInput);
      }
    }, 200);
  };

  // Handle search functionality
  const handleSearch = async () => {
    console.log('Search triggered with term:', searchTerm); // Debug log

    setIsSearching(true);

    try {
      // Determine search type based on keywords
      const searchLower = searchTerm.toLowerCase();
      let targetPage = '/vendors'; // Default to vendors

      if (searchLower.includes('venue') || searchLower.includes('hall') || searchLower.includes('resort') || searchLower.includes('hotel')) {
        targetPage = '/venues';
      } else if (searchLower.includes('shop') || searchLower.includes('dress') || searchLower.includes('jewelry') || searchLower.includes('decoration')) {
        targetPage = '/shop';
      }

      console.log('Target page determined:', targetPage); // Debug log

      // Navigate with search parameters
      const params = new URLSearchParams();
      params.set('search', searchTerm);

      // Use locationInput if available, otherwise use selectedLocation
      const locationToUse = locationInput || selectedLocation;
      if (locationToUse) {
        params.set('location', locationToUse);
      }
      if (selectedDate) {
        params.set('date', selectedDate.toISOString().split('T')[0]);
      }

      const finalUrl = `${targetPage}?${params.toString()}`;
      console.log('Navigating to:', finalUrl); // Debug log

      await router.push(finalUrl);
    } catch (error) {
      console.error('Navigation error:', error);
    } finally {
      setIsSearching(false);
    }
  };

  // Helper function to get background gradient for cities
  const getCityBackground = (index: number) => {
    const gradients = [
      'linear-gradient(135deg, rgba(97, 15, 19, 0.8), rgba(220, 38, 38, 0.6))', // Red
      'linear-gradient(135deg, rgba(34, 197, 94, 0.8), rgba(22, 163, 74, 0.6))', // Green
      'linear-gradient(135deg, rgba(246, 194, 68, 0.8), rgba(251, 146, 60, 0.6))', // Orange
      'linear-gradient(135deg, rgba(236, 72, 153, 0.8), rgba(219, 39, 119, 0.6))', // Pink
    ];
    return gradients[index % gradients.length];
  };

  // Helper function to get city icon
  const getCityIcon = (index: number) => {
    const icons = [
      <path key="icon1" d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>,
      <path key="icon2" d="M14 6l-3.75 5 2.85 3.8-1.6 1.2C9.81 13.75 7 10 7 10l-6 8h22L14 6z"/>,
      <path key="icon3" d="M12 1L3 5v6c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V5l-9-4z M12 7L8 10v7h8v-7l-4-3z"/>,
      <path key="icon4" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>,
    ];
    return icons[index % icons.length];
  };

  // Testimonial carousel state
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const totalTestimonials = 4;

  // Services carousel state
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const [canScrollServicesLeft, setCanScrollServicesLeft] = useState(false);
  const [canScrollServicesRight, setCanScrollServicesRight] = useState(true);

  // Testimonial carousel functions
  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % totalTestimonials);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + totalTestimonials) % totalTestimonials);
  };

  // Auto-advance testimonials
  useEffect(() => {
    const interval = setInterval(nextTestimonial, 5000); // Change every 5 seconds
    return () => clearInterval(interval);
  }, []);

  // Set up scroll event listener for services carousel
  useEffect(() => {
    const carousel = carouselRef.current;
    if (carousel) {
      // Initial check with delay to ensure DOM is ready
      setTimeout(checkScrollPosition, 100);

      // Add scroll event listener
      carousel.addEventListener('scroll', checkScrollPosition);

      // Add resize event listener to recheck on window resize
      window.addEventListener('resize', checkScrollPosition);

      return () => {
        carousel.removeEventListener('scroll', checkScrollPosition);
        window.removeEventListener('resize', checkScrollPosition);
      };
    }
  }, []);

  // Set up scroll event listener for services carousel
  useEffect(() => {
    const servicesCarousel = servicesCarouselRef.current;
    if (servicesCarousel) {
      // Initial check with delay to ensure DOM is ready
      setTimeout(checkServicesScrollPosition, 100);

      // Add scroll event listener
      servicesCarousel.addEventListener('scroll', checkServicesScrollPosition);

      // Add resize event listener to recheck on window resize
      window.addEventListener('resize', checkServicesScrollPosition);

      return () => {
        servicesCarousel.removeEventListener('scroll', checkServicesScrollPosition);
        window.removeEventListener('resize', checkServicesScrollPosition);
      };
    }
  }, []);

  // Fallback: Always allow scrolling if state detection fails
  useEffect(() => {
    // Ensure right button is enabled initially after component mounts
    const timer = setTimeout(() => {
      if (carouselRef.current) {
        const { scrollWidth, clientWidth } = carouselRef.current;
        if (scrollWidth > clientWidth) {
          setCanScrollRight(true);
        }
      }
    }, 200);

    return () => clearTimeout(timer);
  }, []);

  // Check scroll position and update button states
  const checkScrollPosition = () => {
    if (carouselRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = carouselRef.current;
      setCanScrollLeft(scrollLeft > 5); // Small threshold to account for rounding
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 5);
    }
  };

  const scrollLeft = () => {
    console.log('Scroll left clicked');
    if (carouselRef.current) {
      const scrollAmount = 256; // Card width (240px) + gap (16px)
      console.log('Scrolling left by', scrollAmount);
      carouselRef.current.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth'
      });
      // Update button states after scroll
      setTimeout(checkScrollPosition, 100);
    } else {
      console.log('carouselRef.current is null');
    }
  };

  const scrollRight = () => {
    console.log('Scroll right clicked');
    if (carouselRef.current) {
      const scrollAmount = 256; // Card width (240px) + gap (16px)
      console.log('Scrolling right by', scrollAmount);
      carouselRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });
      // Update button states after scroll
      setTimeout(checkScrollPosition, 100);
    } else {
      console.log('carouselRef.current is null');
    }
  };

  // Services carousel functions
  const checkServicesScrollPosition = () => {
    if (servicesCarouselRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = servicesCarouselRef.current;
      setCanScrollServicesLeft(scrollLeft > 5);
      setCanScrollServicesRight(scrollLeft < scrollWidth - clientWidth - 5);
    }
  };

  const scrollServicesLeft = () => {
    if (servicesCarouselRef.current) {
      const scrollAmount = 256; // Card width (240px) + gap (16px)
      servicesCarouselRef.current.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth'
      });
      setTimeout(checkServicesScrollPosition, 100);
    }
  };

  const scrollServicesRight = () => {
    if (servicesCarouselRef.current) {
      const scrollAmount = 256; // Card width (240px) + gap (16px)
      servicesCarouselRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });
      setTimeout(checkServicesScrollPosition, 100);
    }
  };

  return (
    <>
      <SimpleSEO
        title="Thirumanam 360 - Your Dream Wedding Starts Here"
        description="Discover the best wedding vendors, venues, and inspiration for your perfect day. Plan your dream wedding with Thirumanam 360 - India's premier wedding planning platform."
        keywords="wedding planning, wedding vendors, wedding venues, Tamil wedding, Indian wedding, marriage planning, wedding services, wedding marketplace"
        image="/hero_image_1.webp"
        url="/"
      />
      <LayoutWrapper>
        <div className="min-h-screen bg-white pt-[44px]">
        <style jsx>{`
          .scrollbar-hide {
            -ms-overflow-style: none;
            scrollbar-width: none;
          }
          .scrollbar-hide::-webkit-scrollbar {
            display: none;
          }
        `}</style>

      {/* Hero Section */}
      <section className="relative overflow-hidden" style={{ zIndex: 10 }}>
        {/* Hero Carousel */}
        <div className="relative w-full h-[544px] md:h-[644px]">
          <Carousel
            images={[
              {
                src: "/hero_image_1.webp",
                alt: "Beautiful wedding celebration with traditional Tamil Nadu elements"
              },
              {
                src: "/hero_image_2.webp",
                alt: "Elegant wedding venue with traditional decorations"
              },
              {
                src: "/hero_image_3.webp",
                alt: "Traditional wedding ceremony with cultural elements"
              }
            ]}
            autoPlayInterval={3000}
            showArrows={false}
            showDots={false}
            className="w-full h-full"
          />
        </div>
        {/* Hero Content Overlay */}
        <div className="absolute inset-0 z-20 flex items-end justify-center h-[544px] md:h-[644px] pb-8 md:pb-12" style={{ overflow: 'visible' }}>
          <div className="container mx-auto px-4" style={{ overflow: 'visible' }}>
            <div className="text-center max-w-4xl mx-auto" style={{ overflow: 'visible' }}>
              <TranslatedHeading
                translationKey="hero.title"
                fallback="Plan Your Dream Wedding With Us!"
                className="text-3xl md:text-5xl font-bold text-white mb-4 md:mb-6 drop-shadow-lg"
                level={1}
              />
              {/* Search Bar */}
              <div className="bg-white rounded-lg shadow-lg p-2 md:p-3 max-w-full md:max-w-4xl mx-auto relative">
                <form onSubmit={(e) => { e.preventDefault(); handleSearch(); }} className="grid grid-cols-1 md:grid-cols-3 gap-2">
                  {/* Removed the search term input field as requested */}
                  <div className="relative">
                    <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400 z-10" />
                    <Input
                      ref={locationInputRef}
                      type="text"
                      placeholder={t('hero.locationPlaceholder', 'Enter city name...')}
                      value={locationInput}
                      onChange={(e) => handleLocationInputChange(e.target.value)}
                      onKeyDown={handleLocationKeyDown}
                      onBlur={handleLocationBlur}
                      onFocus={() => {
                        if (locationInput && filteredCities.length > 0) {
                          setShowLocationSuggestions(true);
                        }
                      }}
                      className="pl-10 h-10"
                      autoComplete="off"
                    />

                    {/* Location Suggestions Dropdown */}
                    {showLocationSuggestions && (filteredCities.length > 0 || locationInput) && (
                      <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-md shadow-xl max-h-60 overflow-y-auto z-50">
                        {filteredCities.map((city, index) => (
                          <button
                            key={`${city.name}-${city.state}`}
                            type="button"
                            className={`w-full text-left px-4 py-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors ${
                              index === selectedSuggestionIndex ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                            }`}
                            onClick={() => handleLocationSelect(city.name)}
                            onMouseEnter={() => setSelectedSuggestionIndex(index)}
                          >
                            <div className="flex items-center space-x-3">
                              <MapPin className="h-4 w-4 text-gray-400 flex-shrink-0" />
                              <div>
                                <div className="font-medium">{city.name}</div>
                                <div className="text-sm text-gray-500">{city.state}</div>
                              </div>
                            </div>
                          </button>
                        ))}

                        {/* Show "Use typed value" option if user typed something not in suggestions */}
                        {locationInput && !filteredCities.some(city => city.name.toLowerCase() === locationInput.toLowerCase()) && (
                          <button
                            type="button"
                            className="w-full text-left px-4 py-3 hover:bg-gray-50 border-b border-gray-100 focus:bg-gray-50 focus:outline-none bg-blue-50"
                            onClick={() => handleLocationSelect(locationInput)}
                          >
                            <div className="flex items-center space-x-3">
                              <Search className="h-4 w-4 text-blue-500 flex-shrink-0" />
                              <div>
                                <div className="font-medium text-blue-700">Search for "{locationInput}"</div>
                                <div className="text-sm text-blue-500">Use this location</div>
                              </div>
                            </div>
                          </button>
                        )}
                      </div>
                    )}

                  </div>

                  {/* Date Filter */}
                  <div className="relative">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={`w-full h-10 pl-10 pr-8 text-left font-normal justify-start ${
                            !selectedDate && "text-muted-foreground"
                          }`}
                        >
                          <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                          {selectedDate ? (
                            <>
                              {selectedDate.toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })}
                              <X
                                className="absolute right-2 top-3 h-4 w-4 text-gray-400 hover:text-gray-600 cursor-pointer"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setSelectedDate(undefined);
                                }}
                              />
                            </>
                          ) : (
                            <span>{t('hero.datePlaceholder', 'Select Date')}</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <CalendarComponent
                          mode="single"
                          selected={selectedDate}
                          onSelect={setSelectedDate}
                          disabled={(date) =>
                            date < new Date(new Date().setHours(0, 0, 0, 0))
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>

                  <Button
                    type="submit"
                    className="bg-primary hover:bg-primary/90 w-full h-10 md:col-span-1"
                    onClick={handleSearch}
                    disabled={isSearching}
                  >
                    {isSearching ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Searching...
                      </>
                    ) : (
                      t('hero.searchButton', 'Search')
                    )}
                  </Button>
                </form>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-10 md:py-16 bg-white relative overflow-hidden">
        {/* Decorative Elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/20 to-transparent rounded-full translate-x-16 -translate-y-16"></div>
        <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-tr from-accent/10 to-transparent rounded-full -translate-x-20 translate-y-20"></div>

        <div className="container mx-auto px-4 relative">
          <div className="text-center mb-5">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-pink-600 rounded-full mb-4 shadow-lg">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <h2 className="text-2xl md:text-3xl font-bold text-primary">{t('services.title', 'Popular Wedding Services')}</h2>
          </div>

          <div className="flex items-center justify-end mb-5">
            <div className="flex gap-2">
              <button
                onClick={scrollServicesLeft}
                className={`p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200 hover:shadow-md active:scale-95 cursor-pointer ${
                  !canScrollServicesLeft ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                aria-label="Scroll left"
                type="button"
                disabled={!canScrollServicesLeft}
              >
                <ChevronLeft className="w-5 h-5 text-gray-600" />
              </button>
              <button
                onClick={scrollServicesRight}
                className={`p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-all duration-200 hover:shadow-md active:scale-95 cursor-pointer ${
                  !canScrollServicesRight ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                aria-label="Scroll right"
                type="button"
                disabled={!canScrollServicesRight}
              >
                <ChevronRight className="w-5 h-5 text-gray-600" />
              </button>
            </div>
          </div>
          <div
            ref={servicesCarouselRef}
            className="flex gap-4 overflow-x-auto scrollbar-hide scroll-smooth pb-2"
            style={{
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
              scrollBehavior: 'smooth'
            }}
          >
            {/* Marriage Mahal */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=marriage-mahal')} tabIndex={0}>
                <img src="/mahal.webp" alt="Marriage Mahal" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Marriage Mahal</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=marriage-mahal')}>
                Marriage Mahal
              </button>
            </div>
            {/* Photo & Videographers */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=photo-videographers')} tabIndex={0}>
                <img src="/photographer.webp" alt="Photo & Videographers" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Photo & Videographers</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=photo-videographers')}>
                Photo & Videographers
              </button>
            </div>
            {/* Cooks & Caterings */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=cooks-caterings')} tabIndex={0}>
                <img src="/catering.webp" alt="Cooks & Caterings" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Cooks & Caterings</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=cooks-caterings')}>
                Cooks & Caterings
              </button>
            </div>
            {/* Makeup & Mehandi Artists */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=bridal-makeup-artists')} tabIndex={0}>
                <img src="/bride_makeup.webp" alt="Bridal Makeup Artists" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Bridal Makeup Artists</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=bridal-makeup-artists')}>
                Makeup & Mehandi Artists
              </button>
            </div>
            {/* Musical Artists */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=musical-artists')} tabIndex={0}>
                <img src="/nadasvaram.webp" alt="Musical Artists" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Musical Artists</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=musical-artists')}>
                Musical Artists
              </button>
            </div>
            {/* Invitations */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=invitations')} tabIndex={0}>
                <img src="/invitations.webp" alt="Invitations" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Invitations</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=invitations')}>
                Invitations
              </button>
            </div>
            {/* Wedding Jewellery Sets */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=wedding-jewellery-sets')} tabIndex={0}>
                <img src="/wedding_jewels.webp" alt="Wedding Jewellery Sets" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Wedding Jewellery Sets</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=wedding-jewellery-sets')}>
                Wedding Jewellery Sets
              </button>
            </div>
            {/* Marriage Outfits */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=marriage-outfits')} tabIndex={0}>
                <img src="/dress_store.webp" alt="Marriage Outfits" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Marriage Outfits</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=marriage-outfits')}>
                Marriage Outfits
              </button>
            </div>
            {/* Astrologer */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=astrologer')} tabIndex={0}>
                <img src="/astrologer.webp" alt="Astrologer" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Astrologers</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=astrologer')}>
                Astrologers
              </button>
            </div>
            {/* DJ Music */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=dj-music')} tabIndex={0}>
                <img src="/dj_music.webp" alt="DJ Music" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">DJ Music</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=dj-music')}>
                DJ Music
              </button>
            </div>
            {/* Decorators */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=decorators')} tabIndex={0}>
                <img src="/decorators.webp" alt="Decorators" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Decorators</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=decorators')}>
                Decorators
              </button>
            </div>
            {/* Snacks Stall */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=snacks-shops')} tabIndex={0}>
                <img src="/snacks_shop.webp" alt="Snacks Stall" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Snacks Stall</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=snacks-shops')}>
                Snacks Stall
              </button>
            </div>
            {/* Event Organizers */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=event-organizers')} tabIndex={0}>
                <img src="/event_organizers.webp" alt="Event Organizers" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Event Organizers</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=event-organizers')}>
                Event Organizers
              </button>
            </div>
            {/* Iyer/Pandit */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=iyer-pandit')} tabIndex={0}>
                <img src="/iyer_image.webp" alt="Iyer/Pandit" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Iyer/Pandit</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=iyer-pandit')}>
                Iyer/Pandit
              </button>
            </div>
            {/* Return Gift */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=return-gift')} tabIndex={0}>
                <img src="/return_gift.webp" alt="Return Gift" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Return Gift</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=return-gift')}>
                Return Gift
              </button>
            </div>
            {/* Flower Shops */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=flower-shops')} tabIndex={0}>
                <img src="/flower_shops.webp" alt="Flower Shops" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Flower Shops</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=flower-shops')}>
                Flower Shops
              </button>
            </div>
            {/* Travels */}
            <div className="flex-none w-60 h-80 flex flex-col rounded-xl overflow-hidden bg-white shadow-sm hover:shadow-lg transition group">
              <button className="w-full flex-1 bg-gray-100 focus:outline-none group/image relative" onClick={() => router.push('/vendors?category=travels')} tabIndex={0}>
                <img src="/transportations.webp" alt="Travels" className="object-cover w-full h-full rounded-t-xl transition-transform duration-300 group-hover/image:scale-105 group-hover/image:-translate-y-1 group-hover/image:shadow-lg" />
                <span className="sr-only">Travels</span>
              </button>
              <button className={serviceButtonClass + " rounded-t-none rounded-b-xl border-t-0 text-sm h-16 flex items-center justify-center"} onClick={() => router.push('/vendors?category=travels')}>
                Travels
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Shop Products */}
      <section className="py-10 md:py-16 bg-gradient-to-br from-pink-50 via-white to-purple-50 relative overflow-hidden">
        {/* Decorative Elements */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-primary/20 to-transparent rounded-full translate-x-16 -translate-y-16"></div>
        <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-tr from-accent/10 to-transparent rounded-full -translate-x-20 translate-y-20"></div>

        <div className="container mx-auto px-4 relative">
          <div className="text-center mb-5">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-pink-600 rounded-full mb-4 shadow-lg">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
              </svg>
            </div>
            <h2 className="text-2xl md:text-3xl font-bold text-primary">{t('shop.title', 'Wedding Shopping')}</h2>
          </div>

          <FeaturedShopProducts />

          <div className="text-center mt-8">
            <Link href="/shop">
              <Button className="bg-primary hover:bg-primary/90 text-white px-8 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                {t('shop.viewAll', 'View All Products')}
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Success Stories & Statistics */}
      <section className="py-10 md:py-16 bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 relative overflow-hidden">
        {/* Decorative Elements */}
        <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-accent/20 to-transparent rounded-full -translate-x-16 -translate-y-16"></div>
        <div className="absolute bottom-0 right-0 w-40 h-40 bg-gradient-to-tl from-primary/10 to-transparent rounded-full translate-x-20 translate-y-20"></div>

        <div className="container mx-auto px-4 relative">
          <div className="text-center mb-8 md:mb-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-accent to-yellow-500 rounded-full mb-4 shadow-lg">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <h2 className="text-2xl md:text-3xl font-bold mb-4 text-primary">{t('statistics.title', 'Trusted by Thousands of Couples')}</h2>
            <p className="text-gray-700 max-w-2xl mx-auto leading-relaxed">{t('statistics.subtitle', 'Join the growing community of happy couples who found their perfect wedding vendors through Thirumanam 360')}</p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8 mb-12">
            <div className="text-center group">
              <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-accent/20 group-hover:border-accent/40">
                <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-primary to-red-600 bg-clip-text text-transparent mb-2">50,000+</div>
                <div className="text-gray-600 text-sm md:text-base font-medium">{t('statistics.happyCouples', 'Happy Couples')}</div>
                <div className="w-12 h-1 bg-gradient-to-r from-accent to-yellow-500 rounded-full mx-auto mt-3"></div>
              </div>
            </div>
            <div className="text-center group">
              <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-accent/20 group-hover:border-accent/40">
                <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-primary to-red-600 bg-clip-text text-transparent mb-2">15,000+</div>
                <div className="text-gray-600 text-sm md:text-base font-medium">{t('statistics.verifiedVendors', 'Verified Vendors')}</div>
                <div className="w-12 h-1 bg-gradient-to-r from-accent to-yellow-500 rounded-full mx-auto mt-3"></div>
              </div>
            </div>
            <div className="text-center group">
              <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-accent/20 group-hover:border-accent/40">
                <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-primary to-red-600 bg-clip-text text-transparent mb-2">2,500+</div>
                <div className="text-gray-600 text-sm md:text-base font-medium">{t('statistics.weddingVenues', 'Wedding Venues')}</div>
                <div className="w-12 h-1 bg-gradient-to-r from-accent to-yellow-500 rounded-full mx-auto mt-3"></div>
              </div>
            </div>
            <div className="text-center group">
              <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-accent/20 group-hover:border-accent/40">
                <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-primary to-red-600 bg-clip-text text-transparent mb-2">98%</div>
                <div className="text-gray-600 text-sm md:text-base font-medium">{t('statistics.satisfactionRate', 'Satisfaction Rate')}</div>
                <div className="w-12 h-1 bg-gradient-to-r from-accent to-yellow-500 rounded-full mx-auto mt-3"></div>
              </div>
            </div>
          </div>

          {/* Testimonial Carousel */}
          <div className="bg-gradient-to-br from-white to-amber-50 rounded-3xl p-6 md:p-8 shadow-xl border-2 border-accent/20 relative overflow-hidden">
            {/* Decorative Pattern */}
            <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-bl from-accent/10 to-transparent rounded-full translate-x-12 -translate-y-12"></div>
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-primary/5 to-transparent rounded-full -translate-x-16 translate-y-16"></div>

            <div className="text-center relative">
              <div className="flex justify-center mb-6">
                {[1,2,3,4,5].map((star) => (
                  <Star key={star} className="h-6 w-6 fill-accent text-accent drop-shadow-sm" />
                ))}
              </div>

              {/* Carousel Container */}
              <div className="relative" ref={carouselRef}>
                <div className="overflow-hidden">
                  <div
                    className="flex transition-transform duration-500 ease-in-out"
                    style={{ transform: `translateX(-${currentTestimonial * 100}%)` }}
                  >
                    {/* Testimonial 1 */}
                    <div className="w-full flex-shrink-0">
                      <div className="text-6xl text-accent/20 mb-4">"</div>
                      <blockquote className="text-lg md:text-xl text-gray-700 mb-6 italic font-medium leading-relaxed max-w-3xl mx-auto">
                        {t('testimonials.testimonial1.quote', 'Thirumanam 360 made our wedding planning so much easier! We found amazing vendors, beautiful venues, and everything was perfectly organized. Highly recommended!')}
                      </blockquote>
                      <div className="flex items-center justify-center space-x-4">
                        <div className="w-16 h-16 bg-gradient-to-br from-primary to-red-600 rounded-full flex items-center justify-center shadow-lg border-4 border-white">
                          <span className="text-white font-bold text-lg">R&S</span>
                        </div>
                        <div className="text-left">
                          <div className="font-bold text-primary text-lg">{t('testimonials.testimonial1.name', 'Rajesh & Sita')}</div>
                          <div className="text-gray-600 text-sm font-medium">{t('testimonials.testimonial1.location', 'Chennai')} • {t('testimonials.testimonial1.year', 'Married in 2024')}</div>
                          <div className="flex items-center mt-1">
                            <div className="w-2 h-2 bg-accent rounded-full mr-2"></div>
                            <span className="text-xs text-gray-500">{t('testimonials.verifiedCustomer', 'Verified Customer')}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Testimonial 2 */}
                    <div className="w-full flex-shrink-0">
                      <div className="text-6xl text-accent/20 mb-4">"</div>
                      <blockquote className="text-lg md:text-xl text-gray-700 mb-6 italic font-medium leading-relaxed max-w-3xl mx-auto">
                        {t('testimonials.testimonial2.quote', 'The platform connected us with the most talented photographers and decorators. Our wedding photos are absolutely stunning, and the venue decoration was beyond our dreams!')}
                      </blockquote>
                      <div className="flex items-center justify-center space-x-4">
                        <div className="w-16 h-16 bg-gradient-to-br from-secondary to-green-600 rounded-full flex items-center justify-center shadow-lg border-4 border-white">
                          <span className="text-white font-bold text-lg">A&P</span>
                        </div>
                        <div className="text-left">
                          <div className="font-bold text-primary text-lg">{t('testimonials.testimonial2.name', 'Arjun & Priya')}</div>
                          <div className="text-gray-600 text-sm font-medium">{t('testimonials.testimonial2.location', 'Coimbatore')} • {t('testimonials.testimonial2.year', 'Married in 2024')}</div>
                          <div className="flex items-center mt-1">
                            <div className="w-2 h-2 bg-accent rounded-full mr-2"></div>
                            <span className="text-xs text-gray-500">{t('testimonials.verifiedCustomer', 'Verified Customer')}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Testimonial 3 */}
                    <div className="w-full flex-shrink-0">
                      <div className="text-6xl text-accent/20 mb-4">"</div>
                      <blockquote className="text-lg md:text-xl text-gray-700 mb-6 italic font-medium leading-relaxed max-w-3xl mx-auto">
                        {t('testimonials.testimonial3.quote', 'From budget planning to vendor coordination, everything was seamless. The 24/7 support team helped us throughout our journey. Thank you for making our special day perfect!')}
                      </blockquote>
                      <div className="flex items-center justify-center space-x-4">
                        <div className="w-16 h-16 bg-gradient-to-br from-accent to-orange-500 rounded-full flex items-center justify-center shadow-lg border-4 border-white">
                          <span className="text-white font-bold text-lg">V&M</span>
                        </div>
                        <div className="text-left">
                          <div className="font-bold text-primary text-lg">{t('testimonials.testimonial3.name', 'Vikram & Meera')}</div>
                          <div className="text-gray-600 text-sm font-medium">{t('testimonials.testimonial3.location', 'Madurai')} • {t('testimonials.testimonial3.year', 'Married in 2023')}</div>
                          <div className="flex items-center mt-1">
                            <div className="w-2 h-2 bg-accent rounded-full mr-2"></div>
                            <span className="text-xs text-gray-500">{t('testimonials.verifiedCustomer', 'Verified Customer')}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Testimonial 4 */}
                    <div className="w-full flex-shrink-0">
                      <div className="text-6xl text-accent/20 mb-4">"</div>
                      <blockquote className="text-lg md:text-xl text-gray-700 mb-6 italic font-medium leading-relaxed max-w-3xl mx-auto">
                        {t('testimonials.testimonial4.quote', 'The variety of vendors and competitive pricing saved us so much money! We got the best deals for catering, music, and jewelry. Highly satisfied with the service quality.')}
                      </blockquote>
                      <div className="flex items-center justify-center space-x-4">
                        <div className="w-16 h-16 bg-gradient-to-br from-pink-500 to-red-500 rounded-full flex items-center justify-center shadow-lg border-4 border-white">
                          <span className="text-white font-bold text-lg">K&D</span>
                        </div>
                        <div className="text-left">
                          <div className="font-bold text-primary text-lg">{t('testimonials.testimonial4.name', 'Karthik & Divya')}</div>
                          <div className="text-gray-600 text-sm font-medium">{t('testimonials.testimonial4.location', 'Salem')} • {t('testimonials.testimonial4.year', 'Married in 2023')}</div>
                          <div className="flex items-center mt-1">
                            <div className="w-2 h-2 bg-accent rounded-full mr-2"></div>
                            <span className="text-xs text-gray-500">{t('testimonials.verifiedCustomer', 'Verified Customer')}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Navigation Arrows */}
                <button
                  onClick={prevTestimonial}
                  className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 w-12 h-12 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center border-2 border-accent/20 hover:border-accent/40 group"
                  aria-label="Previous testimonial"
                >
                  <ChevronLeft className="h-6 w-6 text-primary group-hover:text-accent transition-colors" />
                </button>
                <button
                  onClick={nextTestimonial}
                  className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 w-12 h-12 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center border-2 border-accent/20 hover:border-accent/40 group"
                  aria-label="Next testimonial"
                >
                  <ChevronRight className="h-6 w-6 text-primary group-hover:text-accent transition-colors" />
                </button>
              </div>

              {/* Dots Indicator */}
              <div className="flex justify-center space-x-2 mt-8">
                {[0, 1, 2, 3].map((index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentTestimonial(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      currentTestimonial === index
                        ? 'bg-accent shadow-lg scale-125'
                        : 'bg-gray-300 hover:bg-accent/50'
                    }`}
                    aria-label={`Go to testimonial ${index + 1}`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-10 md:py-16 bg-gradient-to-br from-red-50 via-orange-50 to-amber-50 relative">
        {/* Traditional Pattern Background */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-10 w-20 h-20 border-2 border-primary rounded-full"></div>
          <div className="absolute top-32 right-20 w-16 h-16 border-2 border-accent rounded-full"></div>
          <div className="absolute bottom-20 left-32 w-12 h-12 border-2 border-primary rounded-full"></div>
          <div className="absolute bottom-40 right-10 w-24 h-24 border-2 border-accent rounded-full"></div>
        </div>

        <div className="container mx-auto px-4 relative">
          <div className="text-center mb-8 md:mb-12">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-primary to-red-600 rounded-full mb-6 shadow-xl">
              <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
            </div>
            <h2 className="text-2xl md:text-3xl font-bold mb-4 text-primary">{t('whyChooseUs.title', 'Why Choose Thirumanam 360?')}</h2>
            <p className="text-gray-700 max-w-2xl mx-auto leading-relaxed">{t('whyChooseUs.subtitle', 'Experience the difference with India\'s most trusted wedding planning platform')}</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8">
            <div className="group">
              <div className="text-center p-8 rounded-3xl bg-white hover:bg-gradient-to-br hover:from-white hover:to-primary/5 shadow-lg hover:shadow-2xl transition-all duration-300 border-2 border-transparent hover:border-primary/30 relative overflow-hidden">
                {/* Decorative corner */}
                <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-primary/10 to-transparent rounded-bl-3xl"></div>

                <div className="w-20 h-20 bg-gradient-to-br from-primary to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-primary">{t('whyChooseUs.verifiedVendors.title', 'Verified Vendors')}</h3>
                <p className="text-gray-600 leading-relaxed">{t('whyChooseUs.verifiedVendors.description', 'All our vendors are thoroughly verified with genuine reviews and quality assurance')}</p>
                <div className="w-16 h-1 bg-gradient-to-r from-primary to-pink-500 rounded-full mx-auto mt-4"></div>
              </div>
            </div>

            <div className="group">
              <div className="text-center p-8 rounded-3xl bg-white hover:bg-gradient-to-br hover:from-white hover:to-accent/5 shadow-lg hover:shadow-2xl transition-all duration-300 border-2 border-transparent hover:border-accent/30 relative overflow-hidden">
                {/* Decorative corner */}
                <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-accent/10 to-transparent rounded-bl-3xl"></div>

                <div className="w-20 h-20 bg-gradient-to-br from-accent to-yellow-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-primary">{t('whyChooseUs.bestPrices.title', 'Best Prices')}</h3>
                <p className="text-gray-600 leading-relaxed">{t('whyChooseUs.bestPrices.description', 'Compare prices, get exclusive deals, and save up to 30% on your wedding expenses')}</p>
                <div className="w-16 h-1 bg-gradient-to-r from-accent to-yellow-500 rounded-full mx-auto mt-4"></div>
              </div>
            </div>

            <div className="group">
              <div className="text-center p-8 rounded-3xl bg-white hover:bg-gradient-to-br hover:from-white hover:to-primary/5 shadow-lg hover:shadow-2xl transition-all duration-300 border-2 border-transparent hover:border-primary/30 relative overflow-hidden">
                {/* Decorative corner */}
                <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-primary/10 to-transparent rounded-bl-3xl"></div>

                <div className="w-20 h-20 bg-gradient-to-br from-primary to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-primary">{t('whyChooseUs.support.title', '24/7 Support')}</h3>
                <p className="text-gray-600 leading-relaxed">{t('whyChooseUs.support.description', 'Dedicated wedding planning support team available round the clock for assistance')}</p>
                <div className="w-16 h-1 bg-gradient-to-r from-primary to-pink-500 rounded-full mx-auto mt-4"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Popular Wedding Destinations Ad */}
      <PopularDestinationsAd
        showPromotionalBadges={true}
        showSpecialOffers={true}
        enableRedirects={true}
        redirectToVendors={true}
        onDestinationClick={(cityName, cityData) => {
          // Custom handler for additional tracking or actions
          console.log('Destination clicked:', cityName, cityData);
          // The component will handle navigation automatically when enableRedirects=true
        }}
      />
      {/* Newsletter - moved to just above Footer */}
      <section className="py-10 md:py-16 bg-primary">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-2xl md:text-3xl font-bold text-primary-foreground mb-4">{t('newsletter.title', 'Get Wedding Updates & Offers')}</h2>
          <p className="text-primary-foreground/80 mb-8 max-w-2xl mx-auto">
            {t('newsletter.subtitle', 'Subscribe your email to get wedding updates, top vendors, and special offers.')}
          </p>
          <div className="max-w-md mx-auto">
            <NewsletterSubscription
              source="HOMEPAGE"
              variant="compact"
              className="[&_input]:bg-white [&_button]:bg-white [&_button]:text-primary [&_button]:hover:bg-gray-100"
            />
          </div>
        </div>
      </section>

        </div>
      </LayoutWrapper>


    </>
  )
}