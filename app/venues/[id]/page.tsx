"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { TopHeader } from "@/components/simple-top-header"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { EntityReviews } from "@/components/entity-reviews"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

import { Star, MapPin, Phone, Mail, Users, Heart, Share2, Calendar, CheckCircle, Bed, Loader2, ArrowLeft, Facebook, Instagram, Youtube } from "lucide-react"
import Link from "next/link"

import { venueService, type VenueResponse } from "@/lib/services/venueService"
import { showToast, toastMessages } from '@/lib/toast'
import VenueInquiryForm from '@/components/VenueInquiryForm'
import AuthenticatedContactVenue from '@/components/AuthenticatedContactVenue'
import FavoriteButton from '@/components/FavoriteButton'

export default function VenueDetailsPage() {
  const params = useParams()
  const venueId = params.id as string

  const [venue, setVenue] = useState<VenueResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load venue data on component mount
  useEffect(() => {
    if (venueId) {
      loadVenue()
    }
  }, [venueId])

  const loadVenue = async () => {
    try {
      setLoading(true)
      setError(null)
      const venueData = await venueService.getVenue(venueId)
      setVenue(venueData)
    } catch (err) {
      console.error('Error loading venue:', err)
      setError('Failed to load venue details. Please try again.')
    } finally {
      setLoading(false)
    }
  }



  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <TopHeader />
        <Header />
        <div className="flex justify-center items-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2 text-gray-600">Loading venue details...</span>
        </div>
        <Footer />
      </div>
    )
  }

  // Error state
  if (error || !venue) {
    return (
      <div className="min-h-screen bg-white">
        <TopHeader />
        <Header />
        <div className="text-center py-20">
          <p className="text-red-600 mb-4">{error || 'Venue not found'}</p>
          <Button onClick={loadVenue} className="bg-primary hover:bg-primary/90">
            Try Again
          </Button>
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <TopHeader />
      <Header />

      {/* Enhanced Hero Section */}
      <section className="relative">
        <div className="h-96 bg-gradient-to-r from-primary/20 to-primary/10 relative overflow-hidden">
          {venue.images && venue.images.length > 0 ? (
            <img
              src={venue.images[0]}
              alt={venue.name}
              className="absolute inset-0 w-full h-full object-cover"
            />
          ) : (
            <div className="flex items-center justify-center h-full bg-gradient-to-br from-primary/20 to-primary/10">
              <div className="text-center">
                <div className="h-24 w-24 mx-auto mb-4 bg-white/20 rounded-full flex items-center justify-center">
                  <span className="text-4xl">🏛️</span>
                </div>
                <p className="text-white/60">No image available</p>
              </div>
            </div>
          )}
          <div className="absolute inset-0 bg-black/30"></div>



          {/* Enhanced Info Overlay */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-8">
            <div className="container mx-auto">
              <div className="flex items-end justify-between text-white">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h1 className="text-4xl font-bold">{venue.name}</h1>
                  </div>
                  <div className="flex items-center text-white/80 mb-4">
                    <MapPin className="h-5 w-5 mr-2" />
                    <span className="text-lg">{venue.location}, {venue.city}, {venue.state}</span>
                  </div>

                  {/* Quick Stats */}
                  <div className="flex items-center gap-6 text-white/90">
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      <span className="text-sm">{venue.capacity}</span>
                    </div>
                    {venue.totalArea && (
                      <div className="flex items-center gap-1">
                        <span className="text-sm">{venue.totalArea}</span>
                      </div>
                    )}
                    {venue.availableRooms && (
                      <div className="flex items-center gap-1">
                        <Bed className="h-4 w-4" />
                        <span className="text-sm">{venue.availableRooms} rooms</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="text-right">
                  <div className="text-3xl font-bold text-white mb-1">
                    {venue.price}
                  </div>
                  {venue.priceRange && (
                    <div className="text-lg text-white/80 mb-2">
                      Range: {venue.priceRange}
                    </div>
                  )}
                  <div className="flex items-center gap-2 justify-end">
                    <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                    <span className="text-lg font-semibold">{venue.rating || "N/A"}</span>
                    <span className="text-white/80">({venue.reviewCount || 0} reviews)</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">

              {/* About Section */}
              <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                  <div className="flex-1">
                    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between lg:mb-4">
                      <h2 className="text-2xl font-bold mb-4 lg:mb-0">About {venue.name}</h2>

                      {/* Like, Share & Social Media Icons */}
                      <div className="flex gap-3 justify-center lg:justify-end">
                        {/* Like and Share Icons */}
                        <FavoriteButton
                          entityId={venue.id}
                          entityType="VENUE"
                          entityData={{
                            name: venue.name,
                            image: venue.images?.[0],
                            price: venue.price,
                            location: venue.location,
                            city: venue.city,
                            state: venue.state,
                            rating: venue.rating,
                            reviewCount: venue.reviewCount,
                            description: venue.description
                          }}
                          variant="outline"
                          size="icon"
                          className="rounded-full"
                        />
                        <Button size="icon" variant="outline" className="rounded-full hover:bg-primary/10 hover:text-primary hover:border-primary transition-all duration-200">
                          <Share2 className="h-4 w-4" />
                        </Button>

                        {/* Social Media Icons */}
                        {venue.socialMedia && (
                          <>
                            {venue.socialMedia.facebook && (
                              <Button
                                size="icon"
                                variant="outline"
                                className="rounded-full hover:bg-blue-600 hover:text-white hover:border-blue-600 transition-all duration-200"
                                onClick={() => window.open(venue.socialMedia.facebook, '_blank')}
                              >
                                <Facebook className="h-4 w-4" />
                              </Button>
                            )}
                            {venue.socialMedia.instagram && (
                              <Button
                                size="icon"
                                variant="outline"
                                className="rounded-full hover:bg-pink-600 hover:text-white hover:border-pink-600 transition-all duration-200"
                                onClick={() => window.open(venue.socialMedia.instagram, '_blank')}
                              >
                                <Instagram className="h-4 w-4" />
                              </Button>
                            )}
                            {venue.socialMedia.youtube && (
                              <Button
                                size="icon"
                                variant="outline"
                                className="rounded-full hover:bg-red-600 hover:text-white hover:border-red-600 transition-all duration-200"
                                onClick={() => window.open(venue.socialMedia.youtube, '_blank')}
                              >
                                <Youtube className="h-4 w-4" />
                              </Button>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                    <p className="text-gray-700 leading-relaxed">{venue.description}</p>
                  </div>
                </div>
              </div>

              {/* Tabs */}
              <Tabs defaultValue="gallery" className="bg-white rounded-lg shadow-sm">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="gallery">Gallery</TabsTrigger>
                  <TabsTrigger value="spaces">Spaces</TabsTrigger>
                  <TabsTrigger value="amenities">Amenities</TabsTrigger>
                  <TabsTrigger value="packages">Packages</TabsTrigger>
                  <TabsTrigger value="reviews">Reviews</TabsTrigger>
                </TabsList>

                <TabsContent value="gallery" className="p-6">
                  {venue.images && venue.images.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {venue.images.map((image, index) => (
                        <div key={index} className="relative group cursor-pointer">
                          <img
                            src={image || "/placeholder.svg"}
                            alt={`${venue.name} - Image ${index + 1}`}
                            className="w-full h-64 object-cover rounded-lg"
                          />
                          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                            <Button variant="secondary">View Full Size</Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <p className="text-gray-600">No images available for this venue.</p>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="spaces" className="p-6">
                  {venue.spaces && venue.spaces.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {venue.spaces.map((space, index) => (
                        <Card key={index} className="border-2 hover:border-accent transition-colors">
                          <CardContent className="p-6">
                            <h3 className="text-xl font-bold mb-2">{space.name}</h3>
                            <p className="text-2xl font-bold text-primary mb-2">{space.price}</p>
                            <div className="space-y-2 text-sm text-gray-600">
                              <div className="flex items-center">
                                <Users className="h-4 w-4 mr-2" />
                                {space.capacity}
                              </div>
                              {space.area && (
                                <div className="flex items-center">
                                  <span className="w-4 h-4 mr-2 text-center">📐</span>
                                  {space.area}
                                </div>
                              )}
                              {space.description && (
                                <p className="text-gray-600 mt-2">{space.description}</p>
                              )}
                            </div>
                            <Button className="w-full mt-4 bg-primary hover:bg-primary/90">Book This Space</Button>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <p className="text-gray-600">No space information available for this venue.</p>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="amenities" className="p-6">
                  {venue.amenities && venue.amenities.length > 0 ? (
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {venue.amenities.map((amenity, index) => (
                        <div key={index} className="flex items-center p-3 bg-gray-50 rounded-lg">
                          <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                          <span className="text-sm font-medium">{amenity}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <p className="text-gray-600">No amenities information available for this venue.</p>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="packages" className="p-6">
                  {venue.packages && venue.packages.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {venue.packages.map((pkg, index) => (
                        <Card key={index} className="border-2 hover:border-accent transition-colors">
                          <CardContent className="p-6">
                            <h3 className="text-xl font-bold mb-2">{pkg.name}</h3>
                            <p className="text-3xl font-bold text-primary mb-2">{pkg.price}</p>
                            {pkg.duration && <p className="text-gray-600 mb-4">{pkg.duration}</p>}
                            {pkg.description && <p className="text-gray-600 mb-4">{pkg.description}</p>}
                            {pkg.includes && pkg.includes.length > 0 && (
                              <div className="mb-4">
                                <h4 className="font-semibold mb-2">Includes:</h4>
                                <ul className="space-y-2">
                                  {pkg.includes.map((feature, idx) => (
                                    <li key={idx} className="flex items-center text-sm">
                                      <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                                      {feature}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                            {pkg.excludes && pkg.excludes.length > 0 && (
                              <div className="mb-4">
                                <h4 className="font-semibold mb-2">Excludes:</h4>
                                <ul className="space-y-2">
                                  {pkg.excludes.map((feature, idx) => (
                                    <li key={idx} className="flex items-center text-sm text-red-600">
                                      <span className="h-4 w-4 mr-2">✗</span>
                                      {feature}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                            {pkg.terms && (
                              <p className="text-xs text-gray-500 mb-4">{pkg.terms}</p>
                            )}
                            <Button className="w-full mt-4 bg-primary hover:bg-primary/90">Select Package</Button>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <p className="text-gray-600">No packages available for this venue.</p>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="reviews" className="p-6">
                  <EntityReviews
                    entityType="VENUE"
                    entityId={venue.id}
                    entityName={venue.name}
                  />
                </TabsContent>
              </Tabs>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Authenticated Contact Venue Component */}
              <AuthenticatedContactVenue
                venue={{
                  id: venue.id,
                  userId: venue.userId,
                  name: venue.name,
                  type: venue.type,
                  capacity: venue.capacity,
                  contactPhone: venue.contactPhone,
                  contactEmail: venue.contactEmail,
                  website: venue.website,
                  rating: venue.rating,
                  reviewCount: venue.reviewCount,
                  priceRange: venue.priceRange,
                  city: venue.city,
                  state: venue.state
                }}
                showInquiryForm={true}
              />

              {/* Location */}
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-bold mb-4">Location</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-start">
                      <MapPin className="h-4 w-4 text-gray-500 mr-2 mt-0.5" />
                      <div>
                        <p>{venue.location}</p>
                        <p>{venue.city}, {venue.state}</p>
                        {venue.pincode && <p>{venue.pincode}</p>}
                        {venue.fullAddress && <p className="mt-1">{venue.fullAddress}</p>}
                      </div>
                    </div>
                  </div>
                  <div className="mt-4 h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                    <span className="text-gray-500">Map View</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}
