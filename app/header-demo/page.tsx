'use client'

import { LayoutWrapper } from "@/components/layout-wrapper"
import { MultiLanguageHeaderTest } from "@/components/multi-language-header-test"

export default function HeaderDemoPage() {
  return (
    <LayoutWrapper>
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Multi-Language Header Translation Test
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Test the header component translations across all supported Indian languages. 
              Switch between languages to see real-time translation updates.
            </p>
          </div>
          
          <MultiLanguageHeaderTest />
          
          <div className="mt-12 text-center">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 max-w-4xl mx-auto">
              <h2 className="text-2xl font-bold text-blue-900 mb-4">
                🌐 Comprehensive Language Support
              </h2>
              <p className="text-blue-800 mb-4">
                The header component now supports <strong>12 Indian languages</strong> with comprehensive translations for:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                <div>
                  <h3 className="font-semibold text-blue-900 mb-2">✅ Implemented Languages:</h3>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• English (en)</li>
                    <li>• Tamil (ta) - தமிழ்</li>
                    <li>• Hindi (hi) - हिंदी</li>
                    <li>• Kannada (kn) - ಕನ್ನಡ</li>
                    <li>• Malayalam (ml) - മലയാളം</li>
                    <li>• Telugu (te) - తెలుగు</li>
                    <li>• Gujarati (gu) - ગુજરાતી</li>
                    <li>• Marathi (mr) - मराठी</li>
                    <li>• Bengali (bn) - বাংলা</li>
                    <li>• Punjabi (pa) - ਪੰਜਾਬੀ</li>
                    <li>• Urdu (ur) - اردو</li>
                    <li>• Odia (or) - ଓଡ଼ିଆ</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-semibold text-blue-900 mb-2">🎯 Translation Coverage:</h3>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Main Navigation (5 items)</li>
                    <li>• Vendor Categories (10+ categories)</li>
                    <li>• Venue Categories (8+ categories)</li>
                    <li>• Service Badges (6 badges)</li>
                    <li>• Subcategory Items (50+ items)</li>
                    <li>• Cultural Context Preserved</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </LayoutWrapper>
  )
}
