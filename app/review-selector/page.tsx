'use client';

import React, { useState, useEffect } from 'react';
import { TopHeader } from '@/components/top-header';
import { Header } from '@/components/header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, ShoppingBag, Building2, MapPin, Star, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

interface Entity {
  id: string;
  name: string;
  type: 'SHOP' | 'VENDOR' | 'VENUE';
  image?: string;
  rating?: number;
  reviewCount?: number;
  location?: string;
  category?: string;
}

export default function ReviewSelectorPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [entities, setEntities] = useState<Entity[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('shop');

  // Mock data - replace with actual API calls
  const mockEntities: Entity[] = [
    {
      id: '1',
      name: 'Wedding Jewelry Set - Kundan Collection',
      type: 'SHOP',
      image: '/placeholder.svg',
      rating: 4.5,
      reviewCount: 23,
      category: 'Jewelry'
    },
    {
      id: '2',
      name: 'Elegant Photography Studio',
      type: 'VENDOR',
      image: '/placeholder.svg',
      rating: 4.8,
      reviewCount: 156,
      location: 'Chennai',
      category: 'Photography'
    },
    {
      id: '3',
      name: 'Heritage Palace Wedding Hall',
      type: 'VENUE',
      image: '/placeholder.svg',
      rating: 4.6,
      reviewCount: 89,
      location: 'Bangalore',
      category: 'Wedding Halls'
    }
  ];

  useEffect(() => {
    // Load entities based on active tab
    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      const filteredEntities = mockEntities.filter(entity => 
        entity.type === activeTab.toUpperCase() &&
        entity.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setEntities(filteredEntities);
      setLoading(false);
    }, 500);
  }, [activeTab, searchTerm]);

  const handleEntitySelect = (entity: Entity) => {
    // Redirect to the entity page with review form open
    const entityPath = entity.type === 'SHOP' ? 'shop' : 
                      entity.type === 'VENDOR' ? 'vendors' : 'venues';
    router.push(`/${entityPath}/${entity.id}?openReview=true`);
  };

  const getEntityIcon = (type: string) => {
    switch (type) {
      case 'SHOP': return <ShoppingBag className="w-5 h-5" />;
      case 'VENDOR': return <Building2 className="w-5 h-5" />;
      case 'VENUE': return <MapPin className="w-5 h-5" />;
      default: return <ShoppingBag className="w-5 h-5" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary to-primary-dark text-white py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <Button 
              variant="ghost" 
              className="text-white hover:bg-white/10 mb-4"
              onClick={() => router.back()}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Review Type Selection
            </Button>
            <h1 className="text-3xl md:text-4xl font-bold mb-4">
              Select Product or Service to Review
            </h1>
            <p className="text-lg opacity-90">
              Choose the specific product, vendor, or venue you'd like to review
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          
          {/* Search Bar */}
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  placeholder="Search for products, vendors, or venues..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardContent>
          </Card>

          {/* Entity Type Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="shop" className="flex items-center space-x-2">
                <ShoppingBag className="w-4 h-4" />
                <span>Shop Products</span>
              </TabsTrigger>
              <TabsTrigger value="vendor" className="flex items-center space-x-2">
                <Building2 className="w-4 h-4" />
                <span>Vendors</span>
              </TabsTrigger>
              <TabsTrigger value="venue" className="flex items-center space-x-2">
                <MapPin className="w-4 h-4" />
                <span>Venues</span>
              </TabsTrigger>
            </TabsList>

            {/* Shop Products Tab */}
            <TabsContent value="shop">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold">Shop Products</h2>
                  <Link href="/shop">
                    <Button variant="outline" size="sm">
                      Browse All Products
                    </Button>
                  </Link>
                </div>
                
                {loading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {[...Array(6)].map((_, i) => (
                      <Card key={i} className="animate-pulse">
                        <CardContent className="p-4">
                          <div className="h-32 bg-gray-200 rounded mb-4"></div>
                          <div className="h-4 bg-gray-200 rounded mb-2"></div>
                          <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {entities.map((entity) => (
                      <Card key={entity.id} className="cursor-pointer hover:shadow-lg transition-shadow">
                        <CardContent className="p-4">
                          <div className="aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                            {getEntityIcon(entity.type)}
                          </div>
                          <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                            {entity.name}
                          </h3>
                          <div className="flex items-center space-x-2 mb-3">
                            <div className="flex items-center">
                              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                              <span className="text-sm text-gray-600 ml-1">
                                {entity.rating} ({entity.reviewCount} reviews)
                              </span>
                            </div>
                          </div>
                          <Badge variant="outline" className="mb-3">
                            {entity.category}
                          </Badge>
                          <Button 
                            onClick={() => handleEntitySelect(entity)}
                            className="w-full"
                            size="sm"
                          >
                            Write Review
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            </TabsContent>

            {/* Vendors Tab */}
            <TabsContent value="vendor">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold">Vendors</h2>
                  <Link href="/vendors">
                    <Button variant="outline" size="sm">
                      Browse All Vendors
                    </Button>
                  </Link>
                </div>
                
                <div className="text-center py-8">
                  <Building2 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">
                    Search for vendors or <Link href="/vendors" className="text-primary hover:underline">browse all vendors</Link>
                  </p>
                </div>
              </div>
            </TabsContent>

            {/* Venues Tab */}
            <TabsContent value="venue">
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h2 className="text-xl font-semibold">Venues</h2>
                  <Link href="/venues">
                    <Button variant="outline" size="sm">
                      Browse All Venues
                    </Button>
                  </Link>
                </div>
                
                <div className="text-center py-8">
                  <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">
                    Search for venues or <Link href="/venues" className="text-primary hover:underline">browse all venues</Link>
                  </p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
