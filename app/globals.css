@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .animate-spin-slow {
    animation: spin 3s linear infinite;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 357 70% 21%; /* maroon #610f13 */
    --primary-foreground: 36 53% 98%;
    --secondary: 90 34% 45%; /* leaf green */
    --secondary-foreground: 36 53% 98%;
    --muted: 36 53% 85%;
    --muted-foreground: 25 60% 40%;
    --accent: 44 89% 62%; /* gold #F6C244 */
    --accent-foreground: 25 60% 20%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 36 53% 98%;
    --border: 36 53% 80%;
    --input: 36 53% 80%;
    --ring: 347 69% 34%;
    --radius: 0.5rem;
    --chart-1: 347 69% 34%; /* maroon */
    --chart-2: 90 34% 45%;
    --chart-3: 25 60% 40%;
    --chart-4: 36 53% 91%;
    --chart-5: 44 89% 62%; /* gold */
  }

  .dark {
    --background: 25 60% 20%;
    --foreground: 36 53% 98%;
    --card: 25 60% 25%;
    --card-foreground: 36 53% 98%;
    --popover: 25 60% 25%;
    --popover-foreground: 36 53% 98%;
    --primary: 347 69% 34%; /* maroon #9B1C31 */
    --primary-foreground: 36 53% 98%;
    --secondary: 90 34% 30%;
    --secondary-foreground: 36 53% 98%;
    --muted: 25 60% 25%;
    --muted-foreground: 36 53% 91%;
    --accent: 44 89% 62%; /* gold #F6C244 */
    --accent-foreground: 25 60% 20%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 36 53% 98%;
    --border: 25 60% 25%;
    --input: 25 60% 25%;
    --ring: 347 69% 34%;
    --chart-1: 347 69% 34%; /* maroon */
    --chart-2: 90 34% 45%;
    --chart-3: 25 60% 40%;
    --chart-4: 36 53% 91%;
    --chart-5: 44 89% 62%; /* gold */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Add smooth scale and shadow for card images on hover */
  .group:hover .group-hover\:scale-105 {
    transform: scale(1.05);
  }
  .group:hover .group-hover\:-translate-y-1 {
    transform: translateY(-0.25rem);
  }
  .group:hover .group-hover\:shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -4px rgba(0,0,0,0.1);
  }
}
