import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import '@/styles/nprogress.css'
import PageLoader from '@/components/PageLoader'
import { ClientRoot } from "@/components/ClientRoot"

// Force dynamic rendering to avoid useSearchParams issues
export const dynamic = 'force-dynamic'


const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Thirumanam 360 - Your Dream Wedding Starts Here",
  description:
    "Discover the best wedding vendors, venues, and inspiration for your perfect day. Plan your dream wedding with Thirumanam 360.",
  keywords: "wedding, vendors, venues, photography, catering, decoration, bridal wear, wedding planning, Thirumanam 360",
  generator: 'v0.dev',
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://thirumanam360.com'),
  alternates: {
    canonical: '/',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/Thirumanam360_favicon.png" type="image/png" />
        <link rel="apple-touch-icon" href="/Thirumanam360_favicon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#F6C244" />
        <meta name="msapplication-TileColor" content="#F6C244" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        {/* Razorpay Script */}
        <script src="https://checkout.razorpay.com/v1/checkout.js" async></script>
      </head>
      <body className={inter.className}>
        <PageLoader />
        <ClientRoot>
          {children}
        </ClientRoot>
      </body>
    </html>
  )
}