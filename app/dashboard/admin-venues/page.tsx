"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  MapPin, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Filter, 
  Download, 
  Upload,
  Eye,
  CheckCircle,
  XCircle,
  Star,
  Mail,
  Phone,
  Globe,
  Calendar,
  Users,
  Building
} from 'lucide-react';
import { AdminOnlyRoute } from '@/components/RouteProtection';
import AdminContentService, { ContentFilters } from '@/lib/services/adminContentService';
import toast from 'react-hot-toast';

export default function AdminVenuesPage() {
  const [venues, setVenues] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedVenues, setSelectedVenues] = useState<string[]>([]);
  const [filters, setFilters] = useState<ContentFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [showVenueDetails, setShowVenueDetails] = useState(false);
  const [selectedVenue, setSelectedVenue] = useState<any>(null);
  const [venueStats, setVenueStats] = useState<any>(null);
  const [nextToken, setNextToken] = useState<string | undefined>();
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    loadVenues();
    loadVenueStats();
  }, []);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      loadVenues();
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, filters]);

  const loadVenues = async (loadMore: boolean = false) => {
    try {
      setLoading(true);
      
      const searchFilters: ContentFilters = {
        ...filters,
        searchTerm: searchTerm || undefined
      };

      const result = await AdminContentService.getAllVenues(
        searchFilters, 
        50, 
        loadMore ? nextToken : undefined
      );

      if (result.success && result.data) {
        if (loadMore) {
          setVenues(prev => [...prev, ...result.data!.venues]);
        } else {
          setVenues(result.data.venues);
        }
        setNextToken(result.data.nextToken);
        setHasMore(!!result.data.nextToken);
      } else {
        toast.error(result.error || 'Failed to load venues');
      }
    } catch (error) {
      console.error('Error loading venues:', error);
      toast.error('Failed to load venues');
    } finally {
      setLoading(false);
    }
  };

  const loadVenueStats = async () => {
    try {
      const result = await AdminContentService.getContentStatistics();
      if (result.success) {
        setVenueStats(result.data);
      }
    } catch (error) {
      console.error('Error loading venue stats:', error);
    }
  };

  const handleDeleteVenue = async (venueId: string) => {
    if (!confirm('Are you sure you want to delete this venue? This action cannot be undone.')) {
      return;
    }

    try {
      const result = await AdminContentService.deleteVenue(venueId);
      if (result.success) {
        toast.success('Venue deleted successfully');
        loadVenues();
        loadVenueStats();
      } else {
        toast.error(result.error || 'Failed to delete venue');
      }
    } catch (error) {
      console.error('Error deleting venue:', error);
      toast.error('Failed to delete venue');
    }
  };

  const handleBulkAction = async (action: 'delete' | 'activate' | 'deactivate' | 'feature' | 'unfeature') => {
    if (selectedVenues.length === 0) {
      toast.error('Please select venues first');
      return;
    }

    if (action === 'delete' && !confirm(`Are you sure you want to delete ${selectedVenues.length} venues? This action cannot be undone.`)) {
      return;
    }

    try {
      if (action === 'delete') {
        const result = await AdminContentService.bulkDelete('venue', selectedVenues);
        if (result.success) {
          toast.success(`${selectedVenues.length} venues deleted successfully`);
        } else {
          toast.error(result.error || 'Failed to delete venues');
        }
      } else {
        const updateData: any = {};
        if (action === 'activate') updateData.status = 'active';
        if (action === 'deactivate') updateData.status = 'inactive';
        if (action === 'feature') updateData.featured = true;
        if (action === 'unfeature') updateData.featured = false;

        const updatePromises = selectedVenues.map(venueId => 
          AdminContentService.updateVenue(venueId, updateData)
        );
        await Promise.all(updatePromises);
        toast.success(`${selectedVenues.length} venues updated successfully`);
      }
      
      setSelectedVenues([]);
      loadVenues();
      loadVenueStats();
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error);
      toast.error(`Failed to ${action} venues`);
    }
  };

  const handleSelectAll = () => {
    if (selectedVenues.length === venues.length) {
      setSelectedVenues([]);
    } else {
      setSelectedVenues(venues.map(venue => venue.id));
    }
  };

  const handleVenueSelect = (venueId: string) => {
    setSelectedVenues(prev => 
      prev.includes(venueId) 
        ? prev.filter(id => id !== venueId)
        : [...prev, venueId]
    );
  };

  const applyFilters = (newFilters: ContentFilters) => {
    setFilters(newFilters);
    setNextToken(undefined);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setNextToken(undefined);
  };

  const openVenueDetails = (venue: any) => {
    setSelectedVenue(venue);
    setShowVenueDetails(true);
  };

  const getStatusBadge = (venue: any) => {
    if (venue.status === 'active') {
      return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>;
    }
    return <Badge variant="secondary"><XCircle className="w-3 h-3 mr-1" />Inactive</Badge>;
  };

  const getFeaturedBadge = (venue: any) => {
    if (venue.featured) {
      return <Badge className="bg-blue-100 text-blue-800"><Star className="w-3 h-3 mr-1" />Featured</Badge>;
    }
    return null;
  };

  return (
    <AdminOnlyRoute>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Venue Management</h1>
            <p className="text-gray-600">Manage venue listings and approval status</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Venue
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        {venueStats && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Building className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Venues</p>
                    <p className="text-2xl font-bold text-gray-900">{venueStats.totalVenues}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Active</p>
                    <p className="text-2xl font-bold text-gray-900">{venueStats.activeVenues}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Calendar className="h-8 w-8 text-indigo-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Recent</p>
                    <p className="text-2xl font-bold text-gray-900">{venueStats.recentContent}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Search and Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              {/* Search Bar */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search venues by name, location, or category..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Advanced Filters */}
              {showFilters && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <Select value={filters.category || ''} onValueChange={(value) => applyFilters({...filters, category: value || undefined})}>
                      <SelectTrigger>
                        <SelectValue placeholder="All Categories" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Categories</SelectItem>
                        <SelectItem value="Banquet Hall">Banquet Hall</SelectItem>
                        <SelectItem value="Resort">Resort</SelectItem>
                        <SelectItem value="Hotel">Hotel</SelectItem>
                        <SelectItem value="Garden">Garden</SelectItem>
                        <SelectItem value="Beach">Beach</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <Select value={filters.status || ''} onValueChange={(value) => applyFilters({...filters, status: value || undefined})}>
                      <SelectTrigger>
                        <SelectValue placeholder="All Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Status</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Featured</label>
                    <Select value={filters.featured?.toString() || ''} onValueChange={(value) => applyFilters({...filters, featured: value ? value === 'true' : undefined})}>
                      <SelectTrigger>
                        <SelectValue placeholder="All Venues" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Venues</SelectItem>
                        <SelectItem value="true">Featured Only</SelectItem>
                        <SelectItem value="false">Non-Featured</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button variant="outline" onClick={clearFilters} className="w-full">
                      Clear Filters
                    </Button>
                  </div>
                </div>
              )}

              {/* Bulk Actions */}
              {selectedVenues.length > 0 && (
                <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                  <span className="text-sm font-medium text-blue-900">
                    {selectedVenues.length} venue(s) selected
                  </span>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => handleBulkAction('activate')}>
                      <CheckCircle className="w-4 h-4 mr-1" />
                      Activate
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => handleBulkAction('feature')}>
                      <Star className="w-4 h-4 mr-1" />
                      Feature
                    </Button>
                    <Button size="sm" variant="destructive" onClick={() => handleBulkAction('delete')}>
                      <Trash2 className="w-4 h-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Venues List */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <MapPin className="w-5 h-5" />
                Venues ({venues.length})
              </CardTitle>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline" size="sm">
                  <Upload className="w-4 h-4 mr-2" />
                  Import
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading venues...</p>
              </div>
            ) : venues.length === 0 ? (
              <div className="text-center py-8">
                <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No venues found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Table Header */}
                <div className="flex items-center p-4 bg-gray-50 rounded-lg font-medium text-sm text-gray-700">
                  <div className="w-8">
                    <Checkbox
                      checked={selectedVenues.length === venues.length}
                      onCheckedChange={handleSelectAll}
                    />
                  </div>
                  <div className="flex-1 min-w-0">Venue Details</div>
                  <div className="w-24 text-center">Status</div>
                  <div className="w-24 text-center">Featured</div>
                  <div className="w-32 text-center">Actions</div>
                </div>

                {/* Venue Rows */}
                {venues.map((venue) => (
                  <div
                    key={venue.id}
                    className="flex items-center p-4 border rounded-lg hover:bg-gray-50"
                  >
                    <div className="w-8">
                      <Checkbox
                        checked={selectedVenues.includes(venue.id)}
                        onCheckedChange={() => handleVenueSelect(venue.id)}
                      />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3">
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900 truncate">
                            {venue.name}
                          </h3>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <MapPin className="w-3 h-3" />
                            <span className="truncate">{venue.city}, {venue.state}</span>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Building className="w-3 h-3" />
                            <span>{venue.category}</span>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-gray-500 mt-1">
                            <Calendar className="w-3 h-3" />
                            <span>Added: {new Date(venue.createdAt).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="w-24 text-center">
                      <Badge variant={venue.status === 'active' ? "default" : "secondary"}>
                        {venue.status || 'pending'}
                      </Badge>
                    </div>

                    <div className="w-24 text-center">
                      <Badge variant={venue.featured ? "default" : "outline"}>
                        {venue.featured ? <Star className="w-3 h-3" /> : <Star className="w-3 h-3 text-gray-400" />}
                      </Badge>
                    </div>

                    <div className="w-32 text-center">
                      <div className="flex items-center justify-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedVenue(venue);
                            setShowVenueDetails(true);
                          }}
                        >
                          <Eye className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteVenue(venue.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Load More Button */}
                {hasMore && (
                  <div className="text-center pt-4">
                    <Button
                      variant="outline"
                      onClick={() => loadVenues(true)}
                      disabled={loading}
                    >
                      {loading ? 'Loading...' : 'Load More Venues'}
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Venue Details Modal */}
        <Dialog open={showVenueDetails} onOpenChange={setShowVenueDetails}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Venue Details</DialogTitle>
            </DialogHeader>
            {selectedVenue && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Venue Name</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedVenue.name}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Category</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedVenue.category}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Contact Email</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedVenue.contactEmail || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Contact Phone</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedVenue.contactPhone || 'N/A'}</p>
                  </div>
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-700">Address</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedVenue.fullAddress || selectedVenue.address}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">City</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedVenue.city}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">State</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedVenue.state}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Capacity</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedVenue.capacity || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Status</label>
                    <div className="mt-1 flex gap-2">
                      {getStatusBadge(selectedVenue)}
                      {getFeaturedBadge(selectedVenue)}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Created</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(selectedVenue.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                {selectedVenue.description && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedVenue.description}</p>
                  </div>
                )}

                {selectedVenue.amenities && selectedVenue.amenities.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Amenities</label>
                    <div className="flex flex-wrap gap-2">
                      {selectedVenue.amenities.map((amenity: string, index: number) => (
                        <Badge key={index} variant="outline">{amenity}</Badge>
                      ))}
                    </div>
                  </div>
                )}

                {selectedVenue.spaces && selectedVenue.spaces.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Spaces</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {selectedVenue.spaces.map((space: any, index: number) => (
                        <div key={index} className="p-3 bg-gray-50 rounded-lg">
                          <h4 className="font-medium text-gray-900">{space.name}</h4>
                          <p className="text-sm text-gray-600">Capacity: {space.capacity}</p>
                          <p className="text-sm text-gray-600">Price: ₹{space.price}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {selectedVenue.packages && selectedVenue.packages.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Packages</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {selectedVenue.packages.map((pkg: any, index: number) => (
                        <div key={index} className="p-3 bg-gray-50 rounded-lg">
                          <h4 className="font-medium text-gray-900">{pkg.name}</h4>
                          <p className="text-sm text-gray-600">Price: ₹{pkg.price}</p>
                          <p className="text-sm text-gray-600">Duration: {pkg.duration}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {selectedVenue.images && selectedVenue.images.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Images</label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      {selectedVenue.images.map((image: string, index: number) => (
                        <div key={index} className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                          <img
                            src={image}
                            alt={`Venue ${index + 1}`}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder-image.jpg';
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex gap-2">
                  <Button onClick={() => setShowVenueDetails(false)}>
                    Close
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </AdminOnlyRoute>
  );
}
