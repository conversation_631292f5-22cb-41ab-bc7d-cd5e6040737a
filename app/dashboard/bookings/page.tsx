"use client"

import { useAuth } from '@/contexts/AuthContext'
import BookingsList from '@/components/BookingsList'
import { Card, CardContent } from '@/components/ui/card'
import { Calendar, Loader2 } from 'lucide-react'
import { useSearchParams } from 'next/navigation'

export default function BookingsPage() {
  const { user, isLoading, isVendor: authIsVendor } = useAuth()
  const searchParams = useSearchParams()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <Card>
        <CardContent className="py-12 text-center">
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Authentication Required
          </h3>
          <p className="text-gray-600">
            Please log in to view your bookings.
          </p>
        </CardContent>
      </Card>
    )
  }

  // Use the isVendor flag from auth context, with URL parameter override for testing
  const forceView = searchParams.get('view')
  const isVendor = forceView === 'vendor' ? true :
                   forceView === 'customer' ? false :
                   authIsVendor  // Use the properly calculated value from auth context

  // Debug logging to see which component is being rendered
  console.log('🔍 Debug - Dashboard user detection:', {
    userId: user?.userId,
    authIsVendor: authIsVendor,
    forceView: forceView,
    finalIsVendor: isVendor,
    componentToRender: isVendor ? 'VendorBookings' : 'CustomerBookings'
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">
          {isVendor ? 'Booking Requests' : 'My Bookings'}
        </h1>
      </div>

      {/* Unified Bookings List */}
      <BookingsList isVendor={isVendor} />
    </div>
  )
}