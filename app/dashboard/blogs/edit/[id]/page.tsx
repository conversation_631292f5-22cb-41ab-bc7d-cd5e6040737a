'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { 
  ArrowLeft, 
  Save, 
  Eye, 
  Upload, 
  X,
  Plus,
  FileText,
  Image as ImageIcon,
  Loader2
} from 'lucide-react'
import { BlogService, BLOG_CATEGORIES, BlogStatus, BlogCategory, AuthorType, UpdateBlogInput } from '@/lib/services/blogService'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter, useParams } from 'next/navigation'
import Link from 'next/link'

interface Blog {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  category: BlogCategory;
  authorId: string;
  authorName: string;
  authorType: AuthorType;
  featuredImage?: string;
  tags?: string[];
  status: BlogStatus;
  views?: number;
  likes?: number;
  comments?: number;
  isPinned?: boolean;
  isFeatured?: boolean;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export default function EditBlog() {
  const { isAuthenticated } = useAuth()
  const router = useRouter()
  const params = useParams()
  const blogId = params.id as string

  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [blog, setBlog] = useState<Blog | null>(null)
  const [formData, setFormData] = useState<UpdateBlogInput>({
    id: blogId,
    title: '',
    content: '',
    excerpt: '',
    category: BlogCategory.WEDDING_PLANNING,
    featuredImage: '',
    tags: [],
    status: BlogStatus.DRAFT
  })
  const [newTag, setNewTag] = useState('')
  const [previewMode, setPreviewMode] = useState(false)

  useEffect(() => {
    if (isAuthenticated && blogId) {
      loadBlog()
    }
  }, [isAuthenticated, blogId])

  const loadBlog = async () => {
    try {
      setLoading(true)
      const blogData = await BlogService.getBlog(blogId)
      setBlog(blogData)
      setFormData({
        id: blogData.id,
        title: blogData.title,
        content: blogData.content,
        excerpt: blogData.excerpt || '',
        category: blogData.category,
        featuredImage: blogData.featuredImage || '',
        tags: blogData.tags || [],
        status: blogData.status,
        isPinned: blogData.isPinned,
        isFeatured: blogData.isFeatured
      })
    } catch (error) {
      console.error('Error loading blog:', error)
      alert('Failed to load blog post')
      router.push('/dashboard/blogs')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: keyof UpdateBlogInput, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags?.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()]
      }))
      setNewTag('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags?.filter(tag => tag !== tagToRemove) || []
    }))
  }

  const handleSubmit = async (status?: BlogStatus) => {
    if (!formData.title?.trim() || !formData.content?.trim()) {
      alert('Please fill in the title and content')
      return
    }

    try {
      setSaving(true)
      const updateData = {
        ...formData,
        ...(status && { status }),
        excerpt: formData.excerpt || generateExcerpt(formData.content)
      }

      await BlogService.updateBlog(updateData)
      router.push('/dashboard/blogs')
    } catch (error) {
      console.error('Error updating blog:', error)
      alert('Failed to update blog post')
    } finally {
      setSaving(false)
    }
  }

  const generateExcerpt = (content: string, maxLength: number = 150): string => {
    const plainText = content.replace(/<[^>]*>/g, '')
    if (plainText.length <= maxLength) {
      return plainText
    }
    return plainText.substring(0, maxLength).trim() + '...'
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // In a real implementation, you would upload to S3 or another storage service
      const imageUrl = URL.createObjectURL(file)
      setFormData(prev => ({
        ...prev,
        featuredImage: imageUrl
      }))
    }
  }

  if (!isAuthenticated) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Authentication Required</h3>
            <p className="text-gray-600">Please log in to edit blog posts.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <Loader2 className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
          <p className="text-gray-600">Loading blog post...</p>
        </div>
      </div>
    )
  }

  if (!blog) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-6 text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Blog Post Not Found</h3>
            <p className="text-gray-600">The blog post you're looking for doesn't exist.</p>
            <Link href="/dashboard/blogs" className="mt-4 inline-block">
              <Button>Back to Blogs</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Link href="/dashboard/blogs">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Blogs
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Edit Blog Post</h1>
            <p className="text-gray-600">Update your blog post</p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setPreviewMode(!previewMode)}
          >
            <Eye className="h-4 w-4 mr-2" />
            {previewMode ? 'Edit' : 'Preview'}
          </Button>
          
          <Button
            onClick={() => handleSubmit()}
            disabled={saving}
            variant="outline"
          >
            {saving ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </Button>
          
          {formData.status !== BlogStatus.PUBLISHED && (
            <Button
              onClick={() => handleSubmit(BlogStatus.PUBLISHED)}
              disabled={saving}
              className="bg-primary hover:bg-primary/90 text-white"
            >
              {saving ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <FileText className="h-4 w-4 mr-2" />
              )}
              Publish
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          {previewMode ? (
            /* Preview Mode */
            <Card>
              <CardHeader>
                <CardTitle>Preview</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                {formData.featuredImage && (
                  <img
                    src={formData.featuredImage}
                    alt="Featured"
                    className="w-full h-64 object-cover rounded-lg mb-6"
                  />
                )}
                
                <div className="flex items-center gap-2 mb-4">
                  <Badge className={BLOG_CATEGORIES[formData.category!]?.color}>
                    {BLOG_CATEGORIES[formData.category!]?.title}
                  </Badge>
                  <span className="text-sm text-gray-500">by {blog.authorName}</span>
                </div>
                
                <h1 className="text-3xl font-bold text-gray-900 mb-4">{formData.title}</h1>
                
                {formData.excerpt && (
                  <p className="text-lg text-gray-600 mb-6">{formData.excerpt}</p>
                )}
                
                <div className="prose max-w-none">
                  <div dangerouslySetInnerHTML={{ __html: formData.content || '' }} />
                </div>
                
                {formData.tags && formData.tags.length > 0 && (
                  <div className="mt-6 pt-6 border-t">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Tags:</h4>
                    <div className="flex flex-wrap gap-2">
                      {formData.tags.map((tag, index) => (
                        <Badge key={index} variant="outline">#{tag}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            /* Edit Mode */
            <div className="space-y-6">
              {/* Title */}
              <Card>
                <CardContent className="p-6">
                  <Label htmlFor="title" className="text-base font-medium">Title *</Label>
                  <Input
                    id="title"
                    placeholder="Enter your blog post title..."
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    className="mt-2 text-lg"
                  />
                </CardContent>
              </Card>

              {/* Excerpt */}
              <Card>
                <CardContent className="p-6">
                  <Label htmlFor="excerpt" className="text-base font-medium">Excerpt</Label>
                  <Textarea
                    id="excerpt"
                    placeholder="Brief description of your post (optional - will be auto-generated if left empty)"
                    value={formData.excerpt}
                    onChange={(e) => handleInputChange('excerpt', e.target.value)}
                    className="mt-2"
                    rows={3}
                  />
                </CardContent>
              </Card>

              {/* Content */}
              <Card>
                <CardContent className="p-6">
                  <Label htmlFor="content" className="text-base font-medium">Content *</Label>
                  <Textarea
                    id="content"
                    placeholder="Write your blog post content here..."
                    value={formData.content}
                    onChange={(e) => handleInputChange('content', e.target.value)}
                    className="mt-2"
                    rows={15}
                  />
                  <p className="text-sm text-gray-500 mt-2">
                    You can use HTML tags for formatting
                  </p>
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Blog Status */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Current Status:</span>
                  <Badge className={
                    blog.status === BlogStatus.PUBLISHED ? 'bg-green-100 text-green-800' :
                    blog.status === BlogStatus.DRAFT ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }>
                    {blog.status}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Pinned:</span>
                  <input
                    type="checkbox"
                    checked={formData.isPinned || false}
                    onChange={(e) => handleInputChange('isPinned', e.target.checked)}
                    className="rounded"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Featured:</span>
                  <input
                    type="checkbox"
                    checked={formData.isFeatured || false}
                    onChange={(e) => handleInputChange('isFeatured', e.target.checked)}
                    className="rounded"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Category */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Category</CardTitle>
            </CardHeader>
            <CardContent>
              <select
                value={formData.category}
                onChange={(e) => handleInputChange('category', e.target.value as BlogCategory)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              >
                {Object.entries(BLOG_CATEGORIES).map(([key, category]) => (
                  <option key={key} value={key}>
                    {category.icon} {category.title}
                  </option>
                ))}
              </select>
            </CardContent>
          </Card>

          {/* Featured Image */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Featured Image</CardTitle>
            </CardHeader>
            <CardContent>
              {formData.featuredImage ? (
                <div className="relative">
                  <img
                    src={formData.featuredImage}
                    alt="Featured"
                    className="w-full h-32 object-cover rounded-lg"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleInputChange('featuredImage', '')}
                    className="absolute top-2 right-2"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <div>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    id="image-upload"
                  />
                  <label
                    htmlFor="image-upload"
                    className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50"
                  >
                    <ImageIcon className="h-8 w-8 text-gray-400 mb-2" />
                    <span className="text-sm text-gray-500">Click to upload image</span>
                  </label>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Tags */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Tags</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="Add a tag"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                />
                <Button onClick={handleAddTag} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              {formData.tags && formData.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map((tag, index) => (
                    <Badge key={index} variant="outline" className="flex items-center gap-1">
                      #{tag}
                      <button
                        onClick={() => handleRemoveTag(tag)}
                        className="ml-1 hover:text-red-600"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Blog Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Views:</span>
                <span className="font-medium">{blog.views || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Likes:</span>
                <span className="font-medium">{blog.likes || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Comments:</span>
                <span className="font-medium">{blog.comments || 0}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Created:</span>
                <span className="font-medium">
                  {new Date(blog.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                  })}
                </span>
              </div>
              {blog.publishedAt && (
                <div className="flex justify-between text-sm">
                  <span>Published:</span>
                  <span className="font-medium">
                    {new Date(blog.publishedAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
