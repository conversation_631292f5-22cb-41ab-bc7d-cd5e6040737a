"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  ShoppingBag, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Filter, 
  Download, 
  Upload,
  Eye,
  CheckCircle,
  XCircle,
  Star,
  Package,
  DollarSign,
  Calendar,
  Users,
  TrendingUp
} from 'lucide-react';
import { AdminOnlyRoute } from '@/components/RouteProtection';
import AdminContentService, { ContentFilters } from '@/lib/services/adminContentService';
import toast from 'react-hot-toast';

export default function AdminShopsPage() {
  const [shops, setShops] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedShops, setSelectedShops] = useState<string[]>([]);
  const [filters, setFilters] = useState<ContentFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [showShopDetails, setShowShopDetails] = useState(false);
  const [selectedShop, setSelectedShop] = useState<any>(null);
  const [shopStats, setShopStats] = useState<any>(null);
  const [nextToken, setNextToken] = useState<string | undefined>();
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    loadShops();
    loadShopStats();
  }, []);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      loadShops();
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, filters]);

  const loadShops = async (loadMore: boolean = false) => {
    try {
      setLoading(true);
      
      const searchFilters: ContentFilters = {
        ...filters,
        searchTerm: searchTerm || undefined
      };

      const result = await AdminContentService.getAllShops(
        searchFilters, 
        50, 
        loadMore ? nextToken : undefined
      );

      if (result.success && result.data) {
        if (loadMore) {
          setShops(prev => [...prev, ...result.data!.shops]);
        } else {
          setShops(result.data.shops);
        }
        setNextToken(result.data.nextToken);
        setHasMore(!!result.data.nextToken);
      } else {
        toast.error(result.error || 'Failed to load shops');
      }
    } catch (error) {
      console.error('Error loading shops:', error);
      toast.error('Failed to load shops');
    } finally {
      setLoading(false);
    }
  };

  const loadShopStats = async () => {
    try {
      const result = await AdminContentService.getContentStatistics();
      if (result.success) {
        setShopStats(result.data);
      }
    } catch (error) {
      console.error('Error loading shop stats:', error);
    }
  };

  const handleDeleteShop = async (shopId: string) => {
    if (!confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
      return;
    }

    try {
      const result = await AdminContentService.deleteShop(shopId);
      if (result.success) {
        toast.success('Product deleted successfully');
        loadShops();
        loadShopStats();
      } else {
        toast.error(result.error || 'Failed to delete product');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      toast.error('Failed to delete product');
    }
  };

  const handleBulkAction = async (action: 'delete' | 'activate' | 'deactivate' | 'feature' | 'unfeature') => {
    if (selectedShops.length === 0) {
      toast.error('Please select products first');
      return;
    }

    if (action === 'delete' && !confirm(`Are you sure you want to delete ${selectedShops.length} products? This action cannot be undone.`)) {
      return;
    }

    try {
      if (action === 'delete') {
        const result = await AdminContentService.bulkDelete('shop', selectedShops);
        if (result.success) {
          toast.success(`${selectedShops.length} products deleted successfully`);
        } else {
          toast.error(result.error || 'Failed to delete products');
        }
      } else {
        const updateData: any = {};
        if (action === 'activate') updateData.status = 'active';
        if (action === 'deactivate') updateData.status = 'inactive';
        if (action === 'feature') updateData.featured = true;
        if (action === 'unfeature') updateData.featured = false;

        const updatePromises = selectedShops.map(shopId => 
          AdminContentService.updateShop(shopId, updateData)
        );
        await Promise.all(updatePromises);
        toast.success(`${selectedShops.length} products updated successfully`);
      }
      
      setSelectedShops([]);
      loadShops();
      loadShopStats();
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error);
      toast.error(`Failed to ${action} products`);
    }
  };

  const handleSelectAll = () => {
    if (selectedShops.length === shops.length) {
      setSelectedShops([]);
    } else {
      setSelectedShops(shops.map(shop => shop.id));
    }
  };

  const handleShopSelect = (shopId: string) => {
    setSelectedShops(prev => 
      prev.includes(shopId) 
        ? prev.filter(id => id !== shopId)
        : [...prev, shopId]
    );
  };

  const applyFilters = (newFilters: ContentFilters) => {
    setFilters(newFilters);
    setNextToken(undefined);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setNextToken(undefined);
  };

  const openShopDetails = (shop: any) => {
    setSelectedShop(shop);
    setShowShopDetails(true);
  };

  const getStatusBadge = (shop: any) => {
    if (shop.status === 'active') {
      return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Active</Badge>;
    }
    return <Badge variant="secondary"><XCircle className="w-3 h-3 mr-1" />Inactive</Badge>;
  };

  const getFeaturedBadge = (shop: any) => {
    if (shop.featured) {
      return <Badge className="bg-blue-100 text-blue-800"><Star className="w-3 h-3 mr-1" />Featured</Badge>;
    }
    return null;
  };

  const getStockBadge = (shop: any) => {
    if (shop.inStock && shop.stock > 0) {
      return <Badge className="bg-blue-100 text-blue-800"><Package className="w-3 h-3 mr-1" />In Stock ({shop.stock})</Badge>;
    }
    return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Out of Stock</Badge>;
  };

  return (
    <AdminOnlyRoute>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Shop Management</h1>
            <p className="text-gray-600">Manage shop products and inventory</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Product
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        {shopStats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <ShoppingBag className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Products</p>
                    <p className="text-2xl font-bold text-gray-900">{shopStats.totalShops}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Package className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">In Stock</p>
                    <p className="text-2xl font-bold text-gray-900">{shopStats.inStockProducts}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Star className="h-8 w-8 text-yellow-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Featured</p>
                    <p className="text-2xl font-bold text-gray-900">{shops.filter(s => s.featured).length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Calendar className="h-8 w-8 text-indigo-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Recent</p>
                    <p className="text-2xl font-bold text-gray-900">{shopStats.recentContent}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Search and Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              {/* Search Bar */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search products by name, brand, or category..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Advanced Filters */}
              {showFilters && (
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <Select value={filters.category || ''} onValueChange={(value) => applyFilters({...filters, category: value || undefined})}>
                      <SelectTrigger>
                        <SelectValue placeholder="All Categories" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Categories</SelectItem>
                        <SelectItem value="Clothing">Clothing</SelectItem>
                        <SelectItem value="Jewelry">Jewelry</SelectItem>
                        <SelectItem value="Accessories">Accessories</SelectItem>
                        <SelectItem value="Footwear">Footwear</SelectItem>
                        <SelectItem value="Decor">Decor</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <Select value={filters.status || ''} onValueChange={(value) => applyFilters({...filters, status: value || undefined})}>
                      <SelectTrigger>
                        <SelectValue placeholder="All Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Status</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                        <SelectItem value="pending">Pending</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Stock Status</label>
                    <Select value={filters.inStock?.toString() || ''} onValueChange={(value) => applyFilters({...filters, inStock: value ? value === 'true' : undefined})}>
                      <SelectTrigger>
                        <SelectValue placeholder="All Products" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Products</SelectItem>
                        <SelectItem value="true">In Stock</SelectItem>
                        <SelectItem value="false">Out of Stock</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Featured</label>
                    <Select value={filters.featured?.toString() || ''} onValueChange={(value) => applyFilters({...filters, featured: value ? value === 'true' : undefined})}>
                      <SelectTrigger>
                        <SelectValue placeholder="All Products" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Products</SelectItem>
                        <SelectItem value="true">Featured Only</SelectItem>
                        <SelectItem value="false">Non-Featured</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button variant="outline" onClick={clearFilters} className="w-full">
                      Clear Filters
                    </Button>
                  </div>
                </div>
              )}

              {/* Bulk Actions */}
              {selectedShops.length > 0 && (
                <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                  <span className="text-sm font-medium text-blue-900">
                    {selectedShops.length} product(s) selected
                  </span>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => handleBulkAction('activate')}>
                      <CheckCircle className="w-4 h-4 mr-1" />
                      Activate
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => handleBulkAction('feature')}>
                      <Star className="w-4 h-4 mr-1" />
                      Feature
                    </Button>
                    <Button size="sm" variant="destructive" onClick={() => handleBulkAction('delete')}>
                      <Trash2 className="w-4 h-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Shop Products List */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <ShoppingBag className="w-5 h-5" />
                Shop Products ({shops.length})
              </CardTitle>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline" size="sm">
                  <Upload className="w-4 h-4 mr-2" />
                  Import
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading products...</p>
              </div>
            ) : shops.length === 0 ? (
              <div className="text-center py-8">
                <ShoppingBag className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No products found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Table Header */}
                <div className="flex items-center p-4 bg-gray-50 rounded-lg font-medium text-sm text-gray-700">
                  <div className="w-8">
                    <Checkbox
                      checked={selectedShops.length === shops.length}
                      onCheckedChange={handleSelectAll}
                    />
                  </div>
                  <div className="flex-1 min-w-0">Product Details</div>
                  <div className="w-24 text-center">Price</div>
                  <div className="w-24 text-center">Status</div>
                  <div className="w-24 text-center">Featured</div>
                  <div className="w-32 text-center">Actions</div>
                </div>

                {/* Shop Product Rows */}
                {shops.map((shop) => (
                  <div
                    key={shop.id}
                    className="flex items-center p-4 border rounded-lg hover:bg-gray-50"
                  >
                    <div className="w-8">
                      <Checkbox
                        checked={selectedShops.includes(shop.id)}
                        onCheckedChange={() => handleShopSelect(shop.id)}
                      />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-3">
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900 truncate">
                            {shop.name}
                          </h3>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Package className="w-3 h-3" />
                            <span className="truncate">{shop.category}</span>
                          </div>
                          {shop.brand && (
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <span className="truncate">Brand: {shop.brand}</span>
                            </div>
                          )}
                          <div className="flex items-center gap-2 text-xs text-gray-500 mt-1">
                            <Calendar className="w-3 h-3" />
                            <span>Added: {new Date(shop.createdAt).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="w-24 text-center">
                      <span className="font-medium text-gray-900">
                        ₹{shop.price || 'N/A'}
                      </span>
                    </div>

                    <div className="w-24 text-center">
                      <Badge variant={shop.status === 'active' ? "default" : "secondary"}>
                        {shop.status || 'pending'}
                      </Badge>
                    </div>

                    <div className="w-24 text-center">
                      <Badge variant={shop.featured ? "default" : "outline"}>
                        {shop.featured ? <Star className="w-3 h-3" /> : <Star className="w-3 h-3 text-gray-400" />}
                      </Badge>
                    </div>

                    <div className="w-32 text-center">
                      <div className="flex items-center justify-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedShop(shop);
                            setShowShopDetails(true);
                          }}
                        >
                          <Eye className="w-3 h-3" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteShop(shop.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Load More Button */}
                {hasMore && (
                  <div className="text-center pt-4">
                    <Button
                      variant="outline"
                      onClick={() => loadShops(true)}
                      disabled={loading}
                    >
                      {loading ? 'Loading...' : 'Load More Products'}
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Shop Product Details Modal */}
        <Dialog open={showShopDetails} onOpenChange={setShowShopDetails}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Product Details</DialogTitle>
            </DialogHeader>
            {selectedShop && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Product Name</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedShop.name}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Category</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedShop.category}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Brand</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedShop.brand || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Price</label>
                    <p className="mt-1 text-sm text-gray-900">₹{selectedShop.price || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">SKU</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedShop.sku || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Stock</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedShop.stock || 'N/A'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Status</label>
                    <div className="mt-1 flex gap-2">
                      {getStatusBadge(selectedShop)}
                      {getFeaturedBadge(selectedShop)}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Created</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(selectedShop.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>

                {selectedShop.description && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedShop.description}</p>
                  </div>
                )}

                {selectedShop.specifications && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Specifications</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {Object.entries(selectedShop.specifications).map(([key, value]) => (
                        <div key={key} className="p-3 bg-gray-50 rounded-lg">
                          <h4 className="font-medium text-gray-900 capitalize">{key}</h4>
                          <p className="text-sm text-gray-600">{value as string}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {selectedShop.images && selectedShop.images.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Images</label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                      {selectedShop.images.map((image: string, index: number) => (
                        <div key={index} className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                          <img
                            src={image}
                            alt={`Product ${index + 1}`}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              (e.target as HTMLImageElement).src = '/placeholder-image.jpg';
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {selectedShop.variants && selectedShop.variants.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Variants</label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {selectedShop.variants.map((variant: any, index: number) => (
                        <div key={index} className="p-3 bg-gray-50 rounded-lg">
                          <h4 className="font-medium text-gray-900">{variant.name}</h4>
                          <p className="text-sm text-gray-600">Price: ₹{variant.price}</p>
                          <p className="text-sm text-gray-600">Stock: {variant.stock}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="flex gap-2">
                  <Button onClick={() => setShowShopDetails(false)}>
                    Close
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </AdminOnlyRoute>
  );
}
