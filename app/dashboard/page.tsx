'use client'
import { BarChart2, Users, Image as ImageIcon, Star, User, ShoppingBag, MapPin, Plus, ArrowRight } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Link from 'next/link'

export default function DashboardHome() {
  // Mock analytics data
  const stats = [
    { label: 'Total Bookings', value: 24, icon: <BarChart2 className="h-6 w-6 text-primary" /> },
    { label: 'Orders', value: 5, icon: <ShoppingBag className="h-6 w-6 text-pink-600" /> },
    { label: 'Profile Completeness', value: '85%', icon: <User className="h-6 w-6 text-blue-600" /> },
  ]
  // Mock bookings trend
  const bookingsTrend = [4, 6, 3, 8, 2, 1, 0, 5, 7, 4, 6, 8]
  const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <h1 className="text-3xl md:text-4xl font-bold mb-8 text-primary">Welcome to Your Dashboard</h1>
      {/* Analytics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
        {stats.map((stat, i) => (
          <div key={i} className="bg-white rounded-2xl shadow-lg p-8 flex flex-col items-center justify-center gap-4 transition hover:shadow-xl">
            <div className="p-4 rounded-full bg-primary/10 flex items-center justify-center mb-2">
              {stat.icon}
            </div>
            <div className="text-3xl font-extrabold text-gray-900">{stat.value}</div>
            <div className="text-gray-500 text-base font-medium">{stat.label}</div>
          </div>
        ))}
      </div>
      {/* Bookings Trend Chart (Mock) */}
      <div className="bg-white shadow-lg p-8 mb-12">
        <div className="flex items-center justify-between mb-6">
          <div className="font-semibold text-xl text-primary">Bookings Trend (2024)</div>
          <div className="text-sm text-gray-400">(Demo Data)</div>
        </div>
        <div className="flex items-end gap-3 h-36">
          {bookingsTrend.map((val, i) => (
            <div key={i} className="flex flex-col items-center w-7">
              <div
                className="bg-primary shadow"
                style={{ height: `${val * 12}px`, minHeight: '8px', width: '100%' }}
                title={`${months[i]}: ${val} bookings`}
              />
              <div className="text-xs text-gray-400 mt-2">{months[i].slice(0, 3)}</div>
            </div>
          ))}
        </div>
      </div>
      {/* Quick Links */}
      <div className="flex flex-wrap gap-4 justify-center">
        <a href="/dashboard/bookings" className="px-6 py-3 rounded-full bg-primary text-white font-semibold shadow hover:bg-primary/90 transition text-base">View Bookings</a>
        <a href="/dashboard/profile" className="px-6 py-3 rounded-full bg-blue-600 text-white font-semibold shadow hover:bg-blue-700 transition text-base">Edit Profile</a>
      </div>
    </div>
  )
} 