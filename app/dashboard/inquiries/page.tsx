'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  MessageCircle,
  Clock,
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  Users,
  DollarSign,
  Filter,
  Search,
  RefreshCw,
  Send,
  CheckCircle,
  AlertCircle,
  Star
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useToast } from '@/hooks/use-toast'
import { Toaster } from '@/components/ui/toaster'
import { generateClient } from 'aws-amplify/api'
import { listInquiries, getInquiry } from '@/src/graphql/queries'
import { updateInquiry } from '@/src/graphql/mutations'
import { submitTestInquiry, createSampleTestData } from '@/lib/test/submitTestInquiry'

const client = generateClient()

interface Inquiry {
  id: string
  vendorUserId: string
  vendorId: string
  vendorName: string
  customerUserId: string
  customerName: string
  customerEmail: string
  customerPhone?: string
  eventDate?: string
  message: string
  inquiryType: string
  status: string
  priority?: string
  budget?: string
  guestCount?: string
  venue?: string
  additionalServices?: string[]
  preferredContactTime?: string
  responseMessage?: string
  respondedAt?: string
  createdAt: string
  updatedAt: string
}

interface InquiryStats {
  total: number
  new: number
  contacted: number
  quoted: number
  confirmed: number
}

export default function VendorInquiriesPage() {
  const { user } = useAuth()
  const { toast } = useToast()

  const [inquiries, setInquiries] = useState<Inquiry[]>([])
  const [stats, setStats] = useState<InquiryStats>({ total: 0, new: 0, contacted: 0, quoted: 0, confirmed: 0 })
  const [loading, setLoading] = useState(true)
  const [selectedInquiry, setSelectedInquiry] = useState<Inquiry | null>(null)
  const [responseMessage, setResponseMessage] = useState('')
  const [newStatus, setNewStatus] = useState('')
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [priorityFilter, setPriorityFilter] = useState('ALL')
  const [testingDatabase, setTestingDatabase] = useState(false)

  // Calculate inquiry statistics
  const calculateStats = (inquiriesList: Inquiry[]) => {
    const stats = {
      total: inquiriesList.length,
      new: inquiriesList.filter(i => i.status === 'NEW').length,
      contacted: inquiriesList.filter(i => i.status === 'CONTACTED').length,
      quoted: inquiriesList.filter(i => i.status === 'QUOTED').length,
      confirmed: inquiriesList.filter(i => i.status === 'CONFIRMED').length
    }
    setStats(stats)
  }

  // Mock data for demonstration
  const getMockInquiries = (): Inquiry[] => [
    {
      id: 'mock-1',
      vendorUserId: user?.userId || 'current-user',
      vendorId: 'vendor-1',
      vendorName: 'Your Business',
      customerUserId: 'customer-1',
      customerName: 'Priya Sharma',
      customerEmail: '<EMAIL>',
      customerPhone: '+91 8148376909',
      eventDate: '2024-12-25',
      message: 'Hi, I am interested in your photography services for my wedding on December 25th. Could you please share your packages and pricing?',
      inquiryType: 'VENDOR_INQUIRY',
      status: 'NEW',
      priority: 'HIGH',
      budget: '₹50,000 - ₹75,000',
      guestCount: '150',
      venue: 'Grand Palace Hotel',
      additionalServices: ['Pre-wedding shoot', 'Album design'],
      preferredContactTime: 'Evening',
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
    },
    {
      id: 'mock-2',
      vendorUserId: user?.userId || 'current-user',
      vendorId: 'vendor-2',
      vendorName: 'Your Business',
      customerUserId: 'customer-2',
      customerName: 'Rahul Kumar',
      customerEmail: '<EMAIL>',
      customerPhone: '+91 8148376909',
      eventDate: '2024-11-15',
      message: 'We are looking for decoration services for our wedding reception. Can you check availability and share pricing?',
      inquiryType: 'SERVICE_QUOTE',
      status: 'CONTACTED',
      priority: 'MEDIUM',
      budget: '₹1,00,000 - ₹1,50,000',
      guestCount: '200',
      venue: 'Sunset Garden',
      responseMessage: 'Thank you for your inquiry. I have sent you a detailed quote via email.',
      respondedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: 'mock-3',
      vendorUserId: user?.userId || 'current-user',
      vendorId: 'vendor-3',
      vendorName: 'Your Business',
      customerUserId: 'customer-3',
      customerName: 'Anita Patel',
      customerEmail: '<EMAIL>',
      customerPhone: '+91 8148376909',
      eventDate: '2025-01-20',
      message: 'Looking for catering services for our engagement ceremony. Need vegetarian menu for 100 guests.',
      inquiryType: 'BOOKING_REQUEST',
      status: 'QUOTED',
      priority: 'MEDIUM',
      budget: '₹75,000 - ₹1,00,000',
      guestCount: '100',
      venue: 'Home Function',
      responseMessage: 'We have prepared a customized vegetarian menu for your engagement. Please find the detailed quote attached.',
      respondedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date(Date.now() - 72 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
    }
  ]

  // Load vendor inquiries
  const loadInquiries = async () => {
    if (!user?.userId) return

    try {
      setLoading(true)

      // Try to fetch from database using API key for public read access
      console.log('Loading inquiries for vendor:', user.userId)

      try {
        // Only use Cognito User Pools authentication for signed-in users
        const result = await client.graphql({
          query: listInquiries,
          variables: {
            filter: {
              vendorUserId: { eq: user.userId }
            },
            limit: 50
          }
        })

        // Properly narrow the result type to access data
        if ('data' in result && result.data && result.data.listInquiries && result.data.listInquiries.items) {
          const fetchedInquiries = result.data.listInquiries.items as Inquiry[]
          console.log('Fetched inquiries from database:', fetchedInquiries.length)

          // Filter for current vendor (additional safety check)
          const vendorInquiries = fetchedInquiries.filter((inquiry: Inquiry) =>
            inquiry.vendorUserId === user!.userId
          )

          if (vendorInquiries.length === 0) {
            setInquiries([])
            calculateStats([])
            toast({
              title: "Database Connected",
              description: "No inquiries found for your vendor account.",
            })
          } else {
            setInquiries(vendorInquiries)
            calculateStats(vendorInquiries)
            toast({
              title: "Database Connected",
              description: `Loaded ${vendorInquiries.length} inquiries from database.`,
            })
          }
        } else {
          throw new Error('Invalid GraphQL result: missing data')
        }
      } catch (dbError) {
        console.error('Database error:', dbError)
        // Fallback to mock data
        const mockInquiries = getMockInquiries()
        setInquiries(mockInquiries)
        calculateStats(mockInquiries)
        toast({
          title: "Demo Mode",
          description: "Database connection failed. Using demo data for testing.",
        })
      }

    } catch (error) {
      console.error('Error loading inquiries:', error)
      toast({
        title: "Error",
        description: "Failed to load inquiries",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  // Test database connection
  const testDatabaseConnection = async () => {
    if (!user?.userId) return

    try {
      setTestingDatabase(true)

      const testData = createSampleTestData(user.userId, 'photographer')
      const result = await submitTestInquiry(testData)

      if (result.success) {
        toast({
          title: "Database Test Successful",
          description: "Test inquiry submitted to database successfully!",
        })

        // Reload inquiries to show the new test inquiry
        await loadInquiries()
      } else {
        toast({
          title: "Database Test Failed",
          description: result.message || "Failed to submit test inquiry",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Database test error:', error)
      toast({
        title: "Database Test Error",
        description: "An error occurred while testing database connection",
        variant: "destructive",
      })
    } finally {
      setTestingDatabase(false)
    }
  }

  // Helper functions for local storage
  const getStoredInquiries = (): Inquiry[] => {
    if (typeof window === 'undefined') return []
    try {
      const stored = localStorage.getItem('thirumanam_vendor_inquiries')
      return stored ? JSON.parse(stored) : []
    } catch {
      return []
    }
  }

  const storeInquiry = (inquiry: Inquiry): void => {
    if (typeof window === 'undefined') return
    try {
      const existing = getStoredInquiries()
      const updated = [inquiry, ...existing.filter(i => i.id !== inquiry.id)]
      localStorage.setItem('thirumanam_vendor_inquiries', JSON.stringify(updated))
    } catch (error) {
      console.warn('Failed to store inquiry locally:', error)
    }
  }

  // Update inquiry status and response
  const handleUpdateInquiry = async () => {
    if (!selectedInquiry || !newStatus) return

    try {
      const updateData = {
        id: selectedInquiry.id,
        status: newStatus,
        responseMessage: responseMessage || undefined,
        respondedAt: responseMessage ? new Date().toISOString() : undefined
      }

      // Try to update in database using API key
      await client.graphql({
        query: updateInquiry,
        variables: { input: updateData },
        authMode: 'apiKey'
      })

      // Update local state
      setInquiries(prev => prev.map(inquiry =>
        inquiry.id === selectedInquiry.id
          ? { ...inquiry, ...updateData }
          : inquiry
      ))

      toast({
        title: "Success",
        description: "Inquiry updated successfully",
      })
      setSelectedInquiry(null)
      setResponseMessage('')
      setNewStatus('')

    } catch (error) {
      console.error('Error updating inquiry:', error)

      // For mock data, still update locally
      if (selectedInquiry.id.startsWith('mock-')) {
        const updateData = {
          status: newStatus,
          responseMessage: responseMessage || undefined,
          respondedAt: responseMessage ? new Date().toISOString() : undefined
        }

        setInquiries(prev => prev.map(inquiry =>
          inquiry.id === selectedInquiry.id
            ? { ...inquiry, ...updateData }
            : inquiry
        ))

        toast({
          title: "Success",
          description: "Inquiry updated (demo mode)",
        })
        setSelectedInquiry(null)
        setResponseMessage('')
        setNewStatus('')
      } else {
        toast({
          title: "Error",
          description: "Failed to update inquiry",
          variant: "destructive",
        })
      }
    }
  }

  // Filter inquiries
  const filteredInquiries = inquiries.filter(inquiry => {
    const matchesSearch = inquiry.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         inquiry.customerEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         inquiry.message.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'ALL' || inquiry.status === statusFilter
    const matchesPriority = priorityFilter === 'ALL' || inquiry.priority === priorityFilter

    return matchesSearch && matchesStatus && matchesPriority
  })

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'NEW': return 'bg-blue-100 text-blue-800'
      case 'CONTACTED': return 'bg-yellow-100 text-yellow-800'
      case 'QUOTED': return 'bg-purple-100 text-purple-800'
      case 'CONFIRMED': return 'bg-green-100 text-green-800'
      case 'CANCELLED': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return 'bg-red-100 text-red-800'
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800'
      case 'LOW': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  useEffect(() => {
    if (user?.userId) {
      loadInquiries()
    }
  }, [user?.userId])

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Authentication Required</h3>
          <p className="text-gray-600">Please log in to view your inquiries.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-10">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-semibold text-gray-900 tracking-tight">Vendor Inquiries</h1>
          <p className="text-gray-500 mt-1 text-sm">Manage customer inquiries and responses</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={loadInquiries} disabled={loading} variant="ghost" size="icon" className="rounded-full">
            <RefreshCw className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </Button>
          <Button
            onClick={testDatabaseConnection}
            disabled={testingDatabase || !user?.userId}
            variant="ghost"
            size="icon"
            className="rounded-full"
          >
            <AlertCircle className={`h-5 w-5 ${testingDatabase ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </div>

      {/* Info Banner */}
      <div className="bg-gray-50 border border-gray-200 rounded-md px-4 py-2 mb-6 flex items-center gap-2 text-sm text-gray-700">
        <AlertCircle className="w-4 h-4 text-blue-500" />
        <span>
          This page shows inquiries submitted to your vendor profile.
          {inquiries.some(i => i.id.startsWith('mock-')) ? (
            <span className="ml-1 text-gray-500">Currently using demo data. Deploy the GraphQL schema to enable full database functionality.</span>
          ) : (
            <span className="ml-1 text-gray-500">Connected to live database.</span>
          )}
        </span>
      </div>

      {/* Stats Row */}
      <div className="grid grid-cols-2 sm:grid-cols-5 gap-4 mb-8">
        <div className="bg-white rounded-lg shadow-sm flex flex-col items-center py-4">
          <MessageCircle className="h-6 w-6 text-blue-500 mb-1" />
          <span className="text-lg font-semibold text-gray-900">{stats.total}</span>
          <span className="text-xs text-gray-500 mt-0.5">Total</span>
        </div>
        <div className="bg-white rounded-lg shadow-sm flex flex-col items-center py-4">
          <AlertCircle className="h-6 w-6 text-blue-500 mb-1" />
          <span className="text-lg font-semibold text-blue-600">{stats.new}</span>
          <span className="text-xs text-gray-500 mt-0.5">New</span>
        </div>
        <div className="bg-white rounded-lg shadow-sm flex flex-col items-center py-4">
          <Clock className="h-6 w-6 text-yellow-500 mb-1" />
          <span className="text-lg font-semibold text-yellow-600">{stats.contacted}</span>
          <span className="text-xs text-gray-500 mt-0.5">Contacted</span>
        </div>
        <div className="bg-white rounded-lg shadow-sm flex flex-col items-center py-4">
          <DollarSign className="h-6 w-6 text-purple-500 mb-1" />
          <span className="text-lg font-semibold text-purple-600">{stats.quoted}</span>
          <span className="text-xs text-gray-500 mt-0.5">Quoted</span>
        </div>
        <div className="bg-white rounded-lg shadow-sm flex flex-col items-center py-4">
          <CheckCircle className="h-6 w-6 text-green-500 mb-1" />
          <span className="text-lg font-semibold text-green-600">{stats.confirmed}</span>
          <span className="text-xs text-gray-500 mt-0.5">Confirmed</span>
        </div>
      </div>

      {/* Filters Bar */}
      <div className="flex flex-col sm:flex-row gap-3 mb-6 bg-gray-50 rounded-lg px-4 py-3 items-center shadow-sm">
        <div className="relative w-full sm:w-1/3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search inquiries..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-white rounded-md"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-40 bg-white rounded-md">
            <SelectValue placeholder="Filter by Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">All Status</SelectItem>
            <SelectItem value="NEW">New</SelectItem>
            <SelectItem value="CONTACTED">Contacted</SelectItem>
            <SelectItem value="QUOTED">Quoted</SelectItem>
            <SelectItem value="CONFIRMED">Confirmed</SelectItem>
            <SelectItem value="CANCELLED">Cancelled</SelectItem>
          </SelectContent>
        </Select>
        <Select value={priorityFilter} onValueChange={setPriorityFilter}>
          <SelectTrigger className="w-full sm:w-40 bg-white rounded-md">
            <SelectValue placeholder="Filter by Priority" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">All Priority</SelectItem>
            <SelectItem value="HIGH">High</SelectItem>
            <SelectItem value="MEDIUM">Medium</SelectItem>
            <SelectItem value="LOW">Low</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Inquiries List */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-12">
            <RefreshCw className="h-8 w-8 text-gray-300 mx-auto mb-4 animate-spin" />
            <p className="text-gray-500">Loading inquiries...</p>
          </div>
        ) : filteredInquiries.length === 0 ? (
          <div className="text-center py-12">
            <MessageCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No inquiries found</h3>
            <p className="text-gray-500">No inquiries match your current filters.</p>
          </div>
        ) : (
          filteredInquiries.map((inquiry) => (
            <div key={inquiry.id} className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow p-6 flex flex-col gap-3">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-2">
                <div className="flex items-center gap-2 flex-wrap">
                  <span className="font-semibold text-base text-gray-900">{inquiry.customerName}</span>
                  <Badge className={getStatusColor(inquiry.status)}>{inquiry.status}</Badge>
                  {inquiry.priority && (
                    <Badge className={getPriorityColor(inquiry.priority)}>{inquiry.priority}</Badge>
                  )}
                </div>
                <div className="text-xs text-gray-400">{formatDate(inquiry.createdAt)}</div>
              </div>
              <div className="flex flex-wrap gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1"><Mail className="h-4 w-4 text-gray-400" />{inquiry.customerEmail}</div>
                {inquiry.customerPhone && <div className="flex items-center gap-1"><Phone className="h-4 w-4 text-gray-400" />{inquiry.customerPhone}</div>}
                {inquiry.eventDate && <div className="flex items-center gap-1"><Calendar className="h-4 w-4 text-gray-400" />{new Date(inquiry.eventDate).toLocaleDateString()}</div>}
                {inquiry.guestCount && <div className="flex items-center gap-1"><Users className="h-4 w-4 text-gray-400" />{inquiry.guestCount} guests</div>}
              </div>
              <div className="bg-gray-50 rounded-md p-3 text-sm text-gray-700">{inquiry.message}</div>
              {inquiry.responseMessage && (
                <div className="bg-blue-50 rounded-md p-3 text-sm text-blue-700">
                  <span className="font-medium">Your Response:</span> {inquiry.responseMessage}
                  <div className="text-xs text-blue-500 mt-1">Responded on {inquiry.respondedAt ? formatDate(inquiry.respondedAt) : 'Unknown'}</div>
                </div>
              )}
              <div className="flex justify-between items-center mt-2">
                <div className="flex gap-2 flex-wrap">
                  {inquiry.budget && <Badge variant="outline">Budget: {inquiry.budget}</Badge>}
                  {inquiry.venue && <Badge variant="outline">Venue: {inquiry.venue}</Badge>}
                </div>
                <Button
                  onClick={() => setSelectedInquiry(inquiry)}
                  variant="outline"
                  size="sm"
                  className="rounded-full px-4"
                >
                  Respond
                </Button>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Response Modal */}
      {selectedInquiry && (
        <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-xl w-full max-w-lg p-6 relative">
            <button
              className="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
              onClick={() => {
                setSelectedInquiry(null)
                setResponseMessage('')
                setNewStatus('')
              }}
              aria-label="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <h2 className="text-xl font-semibold mb-2">Respond to {selectedInquiry.customerName}</h2>
            <div className="bg-gray-50 rounded-md p-3 mb-3">
              <div className="font-medium text-gray-900 mb-1">Customer Message:</div>
              <div className="text-sm text-gray-700">{selectedInquiry.message}</div>
              <div className="mt-2 text-xs text-gray-500 flex flex-wrap gap-2">
                <span>Event Date: {selectedInquiry.eventDate ? new Date(selectedInquiry.eventDate).toLocaleDateString() : 'Not specified'}</span>
                {selectedInquiry.budget && <span>Budget: {selectedInquiry.budget}</span>}
                {selectedInquiry.guestCount && <span>Guests: {selectedInquiry.guestCount}</span>}
              </div>
            </div>
            <div className="mb-3">
              <label className="block text-sm font-medium mb-1">Update Status</label>
              <Select value={newStatus} onValueChange={setNewStatus}>
                <SelectTrigger className="bg-white rounded-md">
                  <SelectValue placeholder="Select new status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CONTACTED">Contacted</SelectItem>
                  <SelectItem value="QUOTED">Quoted</SelectItem>
                  <SelectItem value="CONFIRMED">Confirmed</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">Response Message</label>
              <Textarea
                placeholder="Type your response to the customer..."
                value={responseMessage}
                onChange={(e) => setResponseMessage(e.target.value)}
                rows={4}
                className="bg-white rounded-md"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedInquiry(null)
                  setResponseMessage('')
                  setNewStatus('')
                }}
                className="rounded-full"
              >
                Cancel
              </Button>
              <Button onClick={handleUpdateInquiry} disabled={!newStatus} className="rounded-full">
                <Send className="h-4 w-4 mr-2" />
                Update Inquiry
              </Button>
            </div>
          </div>
        </div>
      )}
      <Toaster />
    </div>
  )
}