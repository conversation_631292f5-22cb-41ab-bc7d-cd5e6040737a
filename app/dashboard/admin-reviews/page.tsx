'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Shield,
  Star,
  Users,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  BarChart3,
  Settings,
  Check,
  X,
  Trash2,
  <PERSON>fresh<PERSON><PERSON>,
  Fi<PERSON>,
  Eye,
  Edit,
  MessageSquare,
  Building,
  Store
} from "lucide-react"
import EntityReviewService from '@/lib/services/entityReviewService'
import { useAuth } from '@/contexts/AuthContext'
import { showToast, toastMessages } from '@/lib/toast'
import { AdminOnlyRoute } from '@/components/RouteProtection'

interface AdminReview {
  id: string
  userId: string
  name: string
  email: string
  location: string
  weddingDate: string
  entityType: string
  entityId: string
  rating: number
  serviceRating?: number
  valueRating?: number
  communicationRating?: number
  professionalismRating?: number
  title: string
  review: string
  wouldRecommend: boolean
  status: string
  verified: boolean
  helpfulCount: number
  createdAt: string
  updatedAt: string
  category?: string
  reviewTarget?: string
  adminNotes?: string
  moderatedBy?: string
  moderatedAt?: string
}

export default function DashboardAdminReviewsPage() {
  const { isAuthenticated, user } = useAuth()
  const [reviews, setReviews] = useState<AdminReview[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedReviews, setSelectedReviews] = useState<string[]>([])
  const [entityTypeFilter, setEntityTypeFilter] = useState<string>('ALL')
  const [updating, setUpdating] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState<string>('all')
  const [editingReview, setEditingReview] = useState<AdminReview | null>(null)
  const [editDialogOpen, setEditDialogOpen] = useState(false)
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    platform: 0,
    vendor: 0
  })

  useEffect(() => {
    if (isAuthenticated && user) {
      loadReviews()
    }
  }, [isAuthenticated, user, entityTypeFilter, activeTab])

  const loadReviews = async () => {
    try {
      setLoading(true)

      // Build filter options based on active tab
      const filterOptions = {
        limit: 100,
        // Remove statusFilter from query - let API return all statuses
      }

      // Add tab-specific filters
      switch (activeTab) {
        case 'platform':
          filterOptions.reviewTarget = 'ADMIN'
          break
        case 'vendor':
          filterOptions.reviewTarget = 'VENDOR'
          break
        case 'all':
        default:
          // No specific filter for all reviews
          break
      }

      // Add entity type filter if specified
      if (entityTypeFilter !== 'ALL') {
        filterOptions.entityType = entityTypeFilter
      }

      console.log('Loading reviews with filters:', filterOptions)
      const result = await EntityReviewService.getAdminReviews(filterOptions)

      if (result.success) {
        let filteredReviews = result.data

        // Clean up reviews data and provide defaults for missing fields
        filteredReviews = filteredReviews.map(review => ({
          ...review,
          entityType: review.entityType || 'PLATFORM',
          entityId: review.entityId || 'unknown',
          userEntityComposite: review.userEntityComposite || `${review.userId}#${review.entityType || 'PLATFORM'}#${review.entityId || 'unknown'}`,
          reviewHelpfulUsers: review.reviewHelpfulUsers || [],
          helpfulCount: review.helpfulCount || 0,
          location: review.location || '',
          email: review.email || 'Not provided',
          weddingDate: review.weddingDate || new Date().toISOString().split('T')[0],
          category: review.category || (review.reviewTarget === 'ADMIN' ? 'PLATFORM' : 'VENDOR'),
          reviewTarget: review.reviewTarget || (review.entityType === 'PLATFORM' ? 'ADMIN' : 'VENDOR')
        }))

        setReviews(filteredReviews)
        calculateStats(filteredReviews)
        console.log(`Loaded ${filteredReviews.length} reviews for tab: ${activeTab}`)
      } else {
        console.error('Error loading reviews:', result.error)
      }
    } catch (error) {
      console.error('Error loading reviews:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (reviewsData: AdminReview[]) => {
    const total = reviewsData.length
    const pending = reviewsData.filter(r => r.status === 'PENDING').length
    const approved = reviewsData.filter(r => r.status === 'APPROVED').length
    const rejected = reviewsData.filter(r => r.status === 'REJECTED').length
    const platform = reviewsData.filter(r => r.category === 'PLATFORM' || r.reviewTarget === 'ADMIN').length
    const vendor = reviewsData.filter(r => r.category !== 'PLATFORM' && r.reviewTarget === 'VENDOR').length

    setStats({ total, pending, approved, rejected, platform, vendor })
  }

  const handleStatusUpdate = async (reviewId: string, newStatus: string) => {
    try {
      setUpdating(prev => [...prev, reviewId])

      const result = await EntityReviewService.updateReviewStatus(reviewId, newStatus, user)

      if (result.success) {
        // Update local state
        setReviews(prev => prev.map(review =>
          review.id === reviewId
            ? { ...review, status: newStatus, updatedAt: new Date().toISOString() }
            : review
        ))

        // Recalculate stats
        const updatedReviews = reviews.map(review =>
          review.id === reviewId ? { ...review, status: newStatus } : review
        )
        calculateStats(updatedReviews)

        showToast.success(`Review status updated to ${newStatus}`)
      } else {
        showToast.error(`Error: ${result.error}`)
      }
    } catch (error) {
      console.error('Error updating status:', error)
      showToast.error('Failed to update review status')
    } finally {
      setUpdating(prev => prev.filter(id => id !== reviewId))
    }
  }

  const handleBulkStatusUpdate = async (newStatus: string) => {
    if (selectedReviews.length === 0) {
      showToast.warning('Please select reviews to update')
      return
    }

    if (!confirm(`Update ${selectedReviews.length} reviews to ${newStatus}?`)) {
      return
    }

    try {
      setUpdating(prev => [...prev, ...selectedReviews])

      const updates = selectedReviews.map(reviewId => ({
        reviewId,
        newStatus
      }))

      const result = await EntityReviewService.bulkUpdateReviewStatus(updates, user)

      if (result.success) {
        // Update local state
        setReviews(prev => prev.map(review =>
          selectedReviews.includes(review.id)
            ? { ...review, status: newStatus, updatedAt: new Date().toISOString() }
            : review
        ))

        setSelectedReviews([])
        loadReviews() // Reload to get fresh stats
        showToast.success(result.message)
      } else {
        showToast.error(`Error: ${result.error}`)
      }
    } catch (error) {
      console.error('Error in bulk update:', error)
      showToast.error('Failed to bulk update reviews')
    } finally {
      setUpdating([])
    }
  }

  const handleEditReview = (review: AdminReview) => {
    setEditingReview(review)
    setEditDialogOpen(true)
  }

  const handleSaveEdit = async (updatedReview: AdminReview) => {
    try {
      setUpdating(prev => [...prev, updatedReview.id])

      const result = await EntityReviewService.updateReview(updatedReview.id, {
        title: updatedReview.title,
        review: updatedReview.review,
        rating: updatedReview.rating,
        serviceRating: updatedReview.serviceRating,
        valueRating: updatedReview.valueRating,
        communicationRating: updatedReview.communicationRating,
        professionalismRating: updatedReview.professionalismRating,
        wouldRecommend: updatedReview.wouldRecommend,
        adminNotes: updatedReview.adminNotes
      })

      setReviews(prev => prev.map(review =>
        review.id === updatedReview.id ? { ...review, ...updatedReview, updatedAt: new Date().toISOString() } : review
      ))
      setEditDialogOpen(false)
      setEditingReview(null)
      showToast.success('Review updated successfully')
    } catch (error) {
      console.error('Error updating review:', error)
      showToast.error('Failed to update review')
    } finally {
      setUpdating(prev => prev.filter(id => id !== updatedReview.id))
    }
  }

  const handleDeleteReview = async (reviewId: string) => {
    if (!confirm('Are you sure you want to delete this review? This action cannot be undone.')) {
      return
    }

    try {
      setUpdating(prev => [...prev, reviewId])

      const result = await EntityReviewService.deleteReview(reviewId, user)

      if (result.success) {
        setReviews(prev => prev.filter(review => review.id !== reviewId))
        loadReviews() // Reload to get fresh stats
        showToast.success('Review deleted successfully')
      } else {
        showToast.error(`Error: ${result.error}`)
      }
    } catch (error) {
      console.error('Error deleting review:', error)
      showToast.error('Failed to delete review')
    } finally {
      setUpdating(prev => prev.filter(id => id !== reviewId))
    }
  }

  const toggleReviewSelection = (reviewId: string) => {
    setSelectedReviews(prev =>
      prev.includes(reviewId)
        ? prev.filter(id => id !== reviewId)
        : [...prev, reviewId]
    )
  }

  const selectAllReviews = () => {
    const filteredReviews = getFilteredReviews()
    if (selectedReviews.length === filteredReviews.length) {
      setSelectedReviews([])
    } else {
      setSelectedReviews(filteredReviews.map(r => r.id))
    }
  }

  const getFilteredReviews = () => {
    // Since we're now filtering at the API level based on tabs,
    // we can return all reviews as they're already filtered
    return reviews
  }

  const renderReviewsList = (reviewsList: AdminReview[], title: string) => {
    return (
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">{title} ({reviewsList.length})</h3>
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={selectedReviews.length === reviewsList.length && reviewsList.length > 0}
              onCheckedChange={() => {
                if (selectedReviews.length === reviewsList.length) {
                  setSelectedReviews([])
                } else {
                  setSelectedReviews(reviewsList.map(r => r.id))
                }
              }}
            />
            <label className="text-sm text-gray-600">
              Select All ({reviewsList.length})
            </label>
          </div>
        </div>

        {loading ? (
          <div className="text-center py-8">
            <RefreshCw className="h-8 w-8 animate-spin text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Loading reviews...</p>
          </div>
        ) : reviewsList.length === 0 ? (
          <div className="text-center py-8">
            <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-600 mb-2">No reviews found</h3>
            <p className="text-gray-500">No reviews match the current filters.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {reviewsList.map((review) => (
              <div key={review.id} className="border rounded-lg p-6 hover:bg-gray-50">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start space-x-3">
                    <Checkbox
                      checked={selectedReviews.includes(review.id)}
                      onCheckedChange={() => toggleReviewSelection(review.id)}
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-lg text-gray-900">{review.name}</h3>
                        <Badge className={getStatusColor(review.status)}>
                          {getStatusIcon(review.status)}
                          <span className="ml-1">{review.status}</span>
                        </Badge>
                        {review.entityType && (
                          <Badge variant="outline">
                            {review.entityType}
                          </Badge>
                        )}
                        {review.verified && (
                          <Badge className="bg-blue-100 text-blue-800">
                            Verified
                          </Badge>
                        )}
                      </div>
                      <p className="text-gray-600 text-sm mb-2">
                        {review.email} • {review.location || 'Location not provided'} • Wedding: {review.weddingDate ? formatDate(review.weddingDate) : 'Date not provided'}
                      </p>
                      <div className="flex items-center gap-4 mb-2">
                        {renderStars(review.rating)}
                        {review.wouldRecommend && (
                          <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
                            Recommends
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right text-sm text-gray-500">
                    <p>Created: {formatDate(review.createdAt)}</p>
                    <p>Updated: {formatDate(review.updatedAt)}</p>
                    <p>ID: {review.id.slice(-8)}</p>
                  </div>
                </div>

                <div className="mb-4">
                  <h4 className="font-semibold text-gray-900 mb-2">{review.title}</h4>
                  <p className="text-gray-700 mb-3">{review.review}</p>
                </div>

                {/* Detailed Ratings */}
                {(review.serviceRating || review.valueRating || review.communicationRating || review.professionalismRating) && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-4 p-3 bg-gray-50 rounded">
                    {review.serviceRating && (
                      <div className="text-center">
                        <div className="text-sm font-medium">{review.serviceRating}/5</div>
                        <p className="text-xs text-gray-600">Service</p>
                      </div>
                    )}
                    {review.valueRating && (
                      <div className="text-center">
                        <div className="text-sm font-medium">{review.valueRating}/5</div>
                        <p className="text-xs text-gray-600">Value</p>
                      </div>
                    )}
                    {review.communicationRating && (
                      <div className="text-center">
                        <div className="text-sm font-medium">{review.communicationRating}/5</div>
                        <p className="text-xs text-gray-600">Communication</p>
                      </div>
                    )}
                    {review.professionalismRating && (
                      <div className="text-center">
                        <div className="text-sm font-medium">{review.professionalismRating}/5</div>
                        <p className="text-xs text-gray-600">Professional</p>
                      </div>
                    )}
                  </div>
                )}

                {/* Entity Information */}
                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center space-x-4">
                    <span>Entity: {review.entityType} #{review.entityId}</span>
                    <span>User: {review.userId}</span>
                    <span>{review.helpfulCount || 0} helpful votes</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex items-center space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEditReview(review)}
                      disabled={updating.includes(review.id)}
                      className="text-blue-600 border-blue-600 hover:bg-blue-50"
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    {review.status !== 'APPROVED' && (
                      <Button
                        size="sm"
                        onClick={() => handleStatusUpdate(review.id, 'APPROVED')}
                        disabled={updating.includes(review.id)}
                        className="bg-green-600 hover:bg-green-700 text-white"
                      >
                        {updating.includes(review.id) ? (
                          <RefreshCw className="h-4 w-4 animate-spin" />
                        ) : (
                          <Check className="h-4 w-4" />
                        )}
                        <span className="ml-1">Approve</span>
                      </Button>
                    )}
                    {review.status !== 'REJECTED' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleStatusUpdate(review.id, 'REJECTED')}
                        disabled={updating.includes(review.id)}
                        className="text-red-600 border-red-600 hover:bg-red-50"
                      >
                        {updating.includes(review.id) ? (
                          <RefreshCw className="h-4 w-4 animate-spin" />
                        ) : (
                          <X className="h-4 w-4" />
                        )}
                        <span className="ml-1">Reject</span>
                      </Button>
                    )}
                    {review.status !== 'PENDING' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleStatusUpdate(review.id, 'PENDING')}
                        disabled={updating.includes(review.id)}
                        className="text-yellow-600 border-yellow-600 hover:bg-yellow-50"
                      >
                        {updating.includes(review.id) ? (
                          <RefreshCw className="h-4 w-4 animate-spin" />
                        ) : (
                          <Clock className="h-4 w-4" />
                        )}
                        <span className="ml-1">Pending</span>
                      </Button>
                    )}
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDeleteReview(review.id)}
                    disabled={updating.includes(review.id)}
                    className="text-red-600 border-red-600 hover:bg-red-50"
                  >
                    {updating.includes(review.id) ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                    <span className="ml-1">Delete</span>
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <Card className="max-w-md mx-auto">
        <CardContent className="p-8 text-center">
          <Shield className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Admin Access Required</h2>
          <p className="text-gray-600 mb-6">
            Please log in with administrator credentials to access the review management dashboard.
          </p>
          <Button className="w-full">
            Login as Admin
          </Button>
        </CardContent>
      </Card>
    )
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'REJECTED':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-600" />
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'REJECTED':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`h-4 w-4 ${
              i < Math.floor(rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600">{rating}/5</span>
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <AdminOnlyRoute>
      <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          🛡️ Admin Review Management
        </h1>
        <p className="text-gray-600">
          Manage and moderate customer reviews across shops, venues, and vendors.
        </p>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <p className="text-sm text-gray-600">Total Reviews</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
            <p className="text-sm text-gray-600">Pending</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{stats.approved}</div>
            <p className="text-sm text-gray-600">Approved</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
            <p className="text-sm text-gray-600">Rejected</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{stats.platform}</div>
            <p className="text-sm text-gray-600">Platform</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{stats.vendor}</div>
            <p className="text-sm text-gray-600">Vendor</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Review Management</span>
            <Button onClick={loadReviews} disabled={loading} size="sm">
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-4 mb-6">
            {/* Entity Type Filter */}
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <Select value={entityTypeFilter} onValueChange={setEntityTypeFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Types</SelectItem>
                  <SelectItem value="SHOP">Shop</SelectItem>
                  <SelectItem value="VENUE">Venue</SelectItem>
                  <SelectItem value="VENDOR">Vendor</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Bulk Actions */}
            {selectedReviews.length > 0 && (
              <div className="flex items-center space-x-2 ml-auto">
                <span className="text-sm text-gray-600">
                  {selectedReviews.length} selected
                </span>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkStatusUpdate('APPROVED')}
                  className="text-green-600 border-green-600 hover:bg-green-50"
                >
                  <Check className="h-4 w-4 mr-1" />
                  Approve
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkStatusUpdate('REJECTED')}
                  className="text-red-600 border-red-600 hover:bg-red-50"
                >
                  <X className="h-4 w-4 mr-1" />
                  Reject
                </Button>
              </div>
            )}
          </div>

          {/* Select All */}
          <div className="flex items-center space-x-2 mb-4">
            <Checkbox
              checked={selectedReviews.length === getFilteredReviews().length && getFilteredReviews().length > 0}
              onCheckedChange={selectAllReviews}
            />
            <label className="text-sm text-gray-600">
              Select All ({getFilteredReviews().length} reviews)
            </label>
          </div>
        </CardContent>
      </Card>

      {/* Reviews List with Tabs */}
      <Card>
        <CardHeader>
          <CardTitle>Review Management</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all" className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                All Reviews ({stats.total})
              </TabsTrigger>
              <TabsTrigger value="platform" className="flex items-center gap-2">
                <MessageSquare className="h-4 w-4" />
                Platform Reviews ({stats.platform})
              </TabsTrigger>
              <TabsTrigger value="vendor" className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                Vendor Reviews ({stats.vendor})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-6">
              {renderReviewsList(getFilteredReviews(), "All Reviews")}
            </TabsContent>

            <TabsContent value="platform" className="mt-6">
              {renderReviewsList(getFilteredReviews(), "Platform Reviews")}
            </TabsContent>

            <TabsContent value="vendor" className="mt-6">
              {renderReviewsList(getFilteredReviews(), "Vendor Reviews")}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Edit Review Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Review</DialogTitle>
          </DialogHeader>
          {editingReview && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit-title">Review Title</Label>
                  <Input
                    id="edit-title"
                    value={editingReview.title}
                    onChange={(e) => setEditingReview({...editingReview, title: e.target.value})}
                  />
                </div>
                <div>
                  <Label htmlFor="edit-rating">Overall Rating</Label>
                  <Select
                    value={editingReview.rating.toString()}
                    onValueChange={(value) => setEditingReview({...editingReview, rating: parseInt(value)})}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 Star</SelectItem>
                      <SelectItem value="2">2 Stars</SelectItem>
                      <SelectItem value="3">3 Stars</SelectItem>
                      <SelectItem value="4">4 Stars</SelectItem>
                      <SelectItem value="5">5 Stars</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="edit-review">Review Content</Label>
                <Textarea
                  id="edit-review"
                  value={editingReview.review}
                  onChange={(e) => setEditingReview({...editingReview, review: e.target.value})}
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="edit-service">Service Rating</Label>
                  <Select
                    value={editingReview.serviceRating?.toString() || ""}
                    onValueChange={(value) => setEditingReview({...editingReview, serviceRating: value ? parseInt(value) : undefined})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1</SelectItem>
                      <SelectItem value="2">2</SelectItem>
                      <SelectItem value="3">3</SelectItem>
                      <SelectItem value="4">4</SelectItem>
                      <SelectItem value="5">5</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-value">Value Rating</Label>
                  <Select
                    value={editingReview.valueRating?.toString() || ""}
                    onValueChange={(value) => setEditingReview({...editingReview, valueRating: value ? parseInt(value) : undefined})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1</SelectItem>
                      <SelectItem value="2">2</SelectItem>
                      <SelectItem value="3">3</SelectItem>
                      <SelectItem value="4">4</SelectItem>
                      <SelectItem value="5">5</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-communication">Communication</Label>
                  <Select
                    value={editingReview.communicationRating?.toString() || ""}
                    onValueChange={(value) => setEditingReview({...editingReview, communicationRating: value ? parseInt(value) : undefined})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1</SelectItem>
                      <SelectItem value="2">2</SelectItem>
                      <SelectItem value="3">3</SelectItem>
                      <SelectItem value="4">4</SelectItem>
                      <SelectItem value="5">5</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-professionalism">Professionalism</Label>
                  <Select
                    value={editingReview.professionalismRating?.toString() || ""}
                    onValueChange={(value) => setEditingReview({...editingReview, professionalismRating: value ? parseInt(value) : undefined})}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1</SelectItem>
                      <SelectItem value="2">2</SelectItem>
                      <SelectItem value="3">3</SelectItem>
                      <SelectItem value="4">4</SelectItem>
                      <SelectItem value="5">5</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="edit-admin-notes">Admin Notes</Label>
                <Textarea
                  id="edit-admin-notes"
                  value={editingReview.adminNotes || ""}
                  onChange={(e) => setEditingReview({...editingReview, adminNotes: e.target.value})}
                  rows={2}
                  placeholder="Internal admin notes..."
                />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={editingReview.wouldRecommend}
                  onCheckedChange={(checked) => setEditingReview({...editingReview, wouldRecommend: !!checked})}
                />
                <Label>Would recommend</Label>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => handleSaveEdit(editingReview)}>
                  Save Changes
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
      </div>
    </AdminOnlyRoute>
  )
}
