"use client"

import { DashboardPageWrapper } from '@/components/dashboard/DashboardPageWrapper'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { BarChart2, Users, ShoppingBag, MapPin } from 'lucide-react'

export default function PerformanceTestPage() {
  // Simulate fast loading data
  const stats = [
    { label: 'Total Users', value: '1,234', icon: Users, color: 'text-blue-600' },
    { label: 'Active Vendors', value: '567', icon: ShoppingBag, color: 'text-green-600' },
    { label: 'Venues Listed', value: '890', icon: MapPin, color: 'text-purple-600' },
    { label: 'Monthly Revenue', value: '₹12.5L', icon: BarChart2, color: 'text-orange-600' },
  ]

  return (
    <DashboardPageWrapper 
      title="Performance Test Page" 
      description="This page loads quickly with optimized components"
    >
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">
                {stat.label}
              </CardTitle>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button className="w-full justify-start">
              <Users className="mr-2 h-4 w-4" />
              Manage Users
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <ShoppingBag className="mr-2 h-4 w-4" />
              View Vendors
            </Button>
            <Button variant="outline" className="w-full justify-start">
              <MapPin className="mr-2 h-4 w-4" />
              Browse Venues
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                'New vendor registration: Royal Photography',
                'Venue updated: Grand Palace Hall',
                'User inquiry: Wedding planning services',
                'Review submitted: 5-star rating',
              ].map((activity, index) => (
                <div key={index} className="text-sm text-gray-600 p-2 bg-gray-50 rounded">
                  {activity}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardPageWrapper>
  )
}
