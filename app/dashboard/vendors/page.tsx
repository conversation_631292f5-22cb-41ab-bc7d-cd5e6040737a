"use client"
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ImageUpload } from "@/components/ui/image-upload";
import { vendorService, VendorData, VendorResponse } from "@/lib/services/vendorService";
import { useAuth } from "@/contexts/AuthContext";
import { Loader2 } from "lucide-react";
import Link from "next/link";
import { Progress } from "@/components/ui/progress"
import { MapPin, Star } from "lucide-react";

const initialVendors = [
  {
    id: 1,
    name: "Royal Photography Studio",
    category: "Wedding Photography",
    description: "Professional wedding photography with 10+ years of experience. Specializing in candid moments and traditional ceremonies.",
    contact: "+91 8148376909",
    email: "<EMAIL>",
    address: "123 Photography Street, Anna Nagar",
    city: "Chennai",
    state: "Tamil Nadu",
    pincode: "600040",
    website: "www.royalphotography.com",
    socialMedia: {
      facebook: "royalphotography",
      instagram: "royalphotography",
      youtube: "royalphotography"
    },
    profilePhoto: "/api/placeholder/150/150",
    gallery: ["/api/placeholder/300/200", "/api/placeholder/300/200", "/api/placeholder/300/200"],
    services: [
      { name: "Wedding Photography", price: "₹50,000", description: "Full day wedding coverage" },
      { name: "Pre-Wedding Shoot", price: "₹15,000", description: "2-hour outdoor shoot" },
      { name: "Reception Photography", price: "₹25,000", description: "Evening reception coverage" }
    ],
    experience: "10+ years",
    events: "500+",
    responseTime: "2 hours",
    rating: 4.8,
    reviewCount: 124,
    verified: true,
    featured: true,
    availability: "Available",
    priceRange: "₹15,000 - ₹75,000",
    specializations: ["Candid Photography", "Traditional Ceremonies", "Pre-Wedding Shoots"],
    awards: ["Best Wedding Photographer 2023", "Excellence in Photography"],
    languages: ["Tamil", "English", "Hindi"],
    coverage: ["Chennai", "Bangalore", "Coimbatore"],
    equipment: ["DSLR Cameras", "Drone Photography", "Professional Lighting"],
    status: "active"
  },
  {
    id: 2,
    name: "Melody Nadaswaram Artists",
    category: "Traditional Music",
    description: "Traditional Nadaswaram artists for authentic Tamil wedding ceremonies. Preserving cultural heritage through music.",
    contact: "+91 8148376909",
    email: "<EMAIL>",
    address: "456 Music Lane, T. Nagar",
    city: "Chennai",
    state: "Tamil Nadu",
    pincode: "600017",
    website: "www.melodynadaswaram.com",
    socialMedia: {
      facebook: "melodynadaswaram",
      instagram: "melodynadaswaram",
      youtube: "melodynadaswaram"
    },
    profilePhoto: "/api/placeholder/150/150",
    gallery: ["/api/placeholder/300/200", "/api/placeholder/300/200"],
    services: [
      { name: "Wedding Ceremony Music", price: "₹8,000", description: "Traditional ceremony music" },
      { name: "Reception Performance", price: "₹12,000", description: "Cultural performance" }
    ],
    experience: "15+ years",
    events: "800+",
    responseTime: "4 hours",
    rating: 4.9,
    reviewCount: 89,
    verified: true,
    featured: false,
    availability: "Available",
    priceRange: "₹8,000 - ₹20,000",
    specializations: ["Traditional Nadaswaram", "Cultural Music", "Religious Ceremonies"],
    awards: ["Cultural Heritage Award 2022"],
    languages: ["Tamil", "Telugu"],
    coverage: ["Chennai", "Madurai", "Trichy"],
    equipment: ["Traditional Nadaswaram", "Thavil", "Sound System"],
    status: "active"
  }
];

const categories = [
  "Wedding Photography",
  "Traditional Music",
  "Catering Services",
  "Decoration & Design",
  "Bridal Makeup",
  "Mehendi Artists",
  "DJ & Entertainment",
  "Transportation",
  "Floral Design",
  "Wedding Planning",
  "Videography",
  "Others"
];

const states = [
  "Tamil Nadu", "Karnataka", "Kerala", "Andhra Pradesh", 
  "Telangana", "Maharashtra", "Gujarat", "Rajasthan"
];

export default function DashboardVendorsPage() {
  const { user, isAuthenticated } = useAuth();
  const [vendors, setVendors] = useState<VendorResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [editingVendor, setEditingVendor] = useState<VendorResponse | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterCategory, setFilterCategory] = useState("");
  const [filterStatus, setFilterStatus] = useState("");
  const [filterCity, setFilterCity] = useState("");
  const [sortBy, setSortBy] = useState("name");
  const [selectedVendors, setSelectedVendors] = useState<string[]>([]);
  const [form, setForm] = useState<VendorData>({
    name: "",
    category: "",
    description: "",
    contact: "",
    email: "",
    address: "",
    city: "",
    state: "",
    pincode: "",
    website: "",
    socialMedia: {
      facebook: "",
      instagram: "",
      youtube: ""
    },
    profilePhoto: "",
    gallery: [],
    services: [{ name: "", price: "", description: "" }],
    experience: "",
    events: "",
    responseTime: "",
    rating: 0,
    reviewCount: 0,
    verified: false,
    featured: false,
    availability: "Available",
    priceRange: "",
    specializations: [],
    awards: [],
    languages: [],
    coverage: [],
    equipment: [],
    status: "active"
  });
  const [modalStep, setModalStep] = useState(1); // 1 to 4
  const totalSteps = 4;

  // Load vendors on component mount
  useEffect(() => {
    if (isAuthenticated) {
      loadVendors();
    }
  }, [isAuthenticated]);

  const loadVendors = async () => {
    try {
      setLoading(true);
      setError(null);
      const userVendors = await vendorService.getUserVendors();
      setVendors(userVendors);
    } catch (err: any) {
      setError(err.message || 'Failed to load vendors');
      console.error('Error loading vendors:', err);
    } finally {
      setLoading(false);
    }
  };

  // Filter and sort vendors
  const filteredAndSortedVendors = vendors.filter(vendor => {
    const matchesSearch = vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vendor.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vendor.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !filterCategory || vendor.category === filterCategory;
    const matchesStatus = !filterStatus || vendor.status === filterStatus;
    const matchesCity = !filterCity || vendor.city.toLowerCase().includes(filterCity.toLowerCase());

    return matchesSearch && matchesCategory && matchesStatus && matchesCity;
  }).sort((a, b) => {
    switch (sortBy) {
      case "name":
        return a.name.localeCompare(b.name);
      case "category":
        return a.category.localeCompare(b.category);
      case "rating":
        return (b.rating || 0) - (a.rating || 0);
      case "experience":
        const expA = parseInt(a.experience?.replace(/[^\d]/g, '') || '0');
        const expB = parseInt(b.experience?.replace(/[^\d]/g, '') || '0');
        return expB - expA;
      default:
        return a.name.localeCompare(b.name);
    }
  });

  // Get unique values for filters
  const getUniqueCategories = () => {
    const categories = vendors.map(v => v.category).filter(Boolean);
    return Array.from(new Set(categories));
  };

  const getUniqueCities = () => {
    const cities = vendors.map(v => v.city).filter(Boolean);
    return Array.from(new Set(cities));
  };

  // Selection handlers
  const handleSelectVendor = (vendorId: string) => {
    setSelectedVendors(prev =>
      prev.includes(vendorId)
        ? prev.filter(id => id !== vendorId)
        : [...prev, vendorId]
    );
  };

  const handleSelectAll = () => {
    if (selectedVendors.length === filteredAndSortedVendors.length) {
      setSelectedVendors([]);
    } else {
      setSelectedVendors(filteredAndSortedVendors.map(vendor => vendor.id!).filter(Boolean));
    }
  };

  // Helper functions for array fields
  const addToArray = (field: string, value: string) => {
    if (value.trim()) {
      setForm(f => ({
        ...f,
        [field]: [...f[field as keyof typeof f] as string[], value.trim()]
      }));
    }
  };

  const removeFromArray = (field: string, index: number) => {
    setForm(f => ({
      ...f,
      [field]: (f[field as keyof typeof f] as string[]).filter((_, i) => i !== index)
    }));
  };

  const updateSocialMedia = (platform: string, value: string) => {
    setForm(f => ({
      ...f,
      socialMedia: {
        ...f.socialMedia,
        [platform]: value
      }
    }));
  };

  // Service management functions
  const addService = () => {
    setForm(f => ({
      ...f,
      services: [...f.services, { name: "", price: "", description: "" }]
    }));
  };

  const removeService = (index: number) => {
    setForm(f => ({
      ...f,
      services: f.services.filter((_, i) => i !== index)
    }));
  };

  const updateService = (index: number, field: string, value: string) => {
    setForm(f => ({
      ...f,
      services: f.services.map((service, i) =>
        i === index ? { ...service, [field]: value } : service
      )
    }));
  };

  const openAdd = () => {
    setForm({
      name: "",
      category: "",
      description: "",
      contact: "",
      email: "",
      address: "",
      city: "",
      state: "",
      pincode: "",
      website: "",
      socialMedia: {
        facebook: "",
        instagram: "",
        youtube: ""
      },
      profilePhoto: "",
      gallery: [],
      services: [{ name: "", price: "", description: "" }],
      experience: "",
      events: "",
      responseTime: "",
      rating: 0,
      reviewCount: 0,
      verified: false,
      featured: false,
      availability: "Available",
      priceRange: "",
      specializations: [],
      awards: [],
      languages: [],
      coverage: [],
      equipment: [],
      status: "active"
    });
    setEditingVendor(null);
    setShowModal(true);
  };

  const openEdit = (vendor: VendorResponse) => {
    setForm({
      name: vendor.name,
      category: vendor.category,
      description: vendor.description || "",
      contact: vendor.contact,
      email: vendor.email || "",
      address: vendor.address || "",
      city: vendor.city,
      state: vendor.state,
      pincode: vendor.pincode || "",
      website: vendor.website || "",
      socialMedia: {
        facebook: vendor.socialMedia?.facebook || "",
        instagram: vendor.socialMedia?.instagram || "",
        youtube: vendor.socialMedia?.youtube || ""
      },
      profilePhoto: vendor.profilePhoto || "",
      gallery: vendor.gallery || [],
      services: (vendor.services && vendor.services.length > 0)
        ? vendor.services.map(s => ({
            name: s.name || "",
            price: s.price || "",
            description: s.description || ""
          }))
        : [{ name: "", price: "", description: "" }],
      experience: vendor.experience || "",
      events: vendor.events || "",
      responseTime: vendor.responseTime || "",
      rating: vendor.rating || 0,
      reviewCount: vendor.reviewCount || 0,
      verified: vendor.verified || false,
      featured: vendor.featured || false,
      availability: vendor.availability || "Available",
      priceRange: vendor.priceRange || "",
      specializations: vendor.specializations || [],
      awards: vendor.awards || [],
      languages: vendor.languages || [],
      coverage: vendor.coverage || [],
      equipment: vendor.equipment || [],
      status: vendor.status || "active"
    });
    setEditingVendor(vendor);
    setShowModal(true);
  };

  const handleChange = (e: any) => {
    const { name, value, type, checked } = e.target;
    if (type === "checkbox") {
      setForm((f) => ({ ...f, [name]: checked }));
    } else if (name === "rating" || name === "reviewCount") {
      setForm((f) => ({ ...f, [name]: Number(value) }));
    } else {
      setForm((f) => ({ ...f, [name]: value }));
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      if (editingVendor) {
        // Update existing vendor
        const updatedVendor = await vendorService.updateVendor(editingVendor.id, form);
        setVendors(vendors.map(v => v.id === editingVendor.id ? updatedVendor : v));
      } else {
        // Create new vendor
        const newVendor = await vendorService.createVendor(form);
        setVendors([...vendors, newVendor]);
      }

      setShowModal(false);
      setEditingVendor(null);
    } catch (err: any) {
      setError(err.message || 'Failed to save vendor');
      console.error('Error saving vendor:', err);
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (vendor: VendorResponse) => {
    if (window.confirm("Are you sure you want to delete this vendor? This action cannot be undone.")) {
      try {
        setError(null);
        await vendorService.deleteVendor(vendor.id);
        setVendors(vendors.filter(v => v.id !== vendor.id));
      } catch (err: any) {
        setError(err.message || 'Failed to delete vendor');
        console.error('Error deleting vendor:', err);
      }
    }
  };

  // Validation for each step
  const validateStep = (step: number) => {
    if (step === 1) {
      return form.name.trim() && form.category.trim() && form.contact.trim();
    }
    if (step === 2) {
      return form.city.trim() && form.state.trim();
    }
    // Steps 3 and 4 can be less strict, or add more as needed
    return true;
  };

  // Reset modal step when opening/closing modal
  useEffect(() => {
    if (showModal) setModalStep(1);
  }, [showModal]);

  // Show loading state if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Please log in to manage vendors</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="space-y-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-1">Manage Vendors</h1>
            <p className="text-gray-500 text-base">{filteredAndSortedVendors.length} of {vendors.length} vendors{selectedVendors.length > 0 && ` • ${selectedVendors.length} selected`}</p>
          </div>
          <div className="flex gap-2">
            {selectedVendors.length > 0 && (
              <Button variant="outline" onClick={() => { if (window.confirm(`Delete ${selectedVendors.length} selected vendors?`)) { selectedVendors.forEach(id => { const vendor = vendors.find(v => v.id === id); if (vendor) handleDelete(vendor); }); setSelectedVendors([]); } }} className="text-red-600 hover:text-red-700 hover:bg-red-50">Delete Selected ({selectedVendors.length})</Button>
            )}
            <Button className="bg-primary hover:bg-primary/90" onClick={openAdd}>+ Add Vendor</Button>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <input type="text" placeholder="Search vendors..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="w-full px-3 py-2 pl-10 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary bg-gray-50" />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</div>
            </div>
            <select value={filterCategory} onChange={e => setFilterCategory(e.target.value)} className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary bg-gray-50"><option value="">All Categories</option>{getUniqueCategories().map(category => (<option key={category} value={category}>{category}</option>))}</select>
            <select value={filterCity} onChange={e => setFilterCity(e.target.value)} className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary bg-gray-50"><option value="">All Cities</option>{getUniqueCities().map(city => (<option key={city} value={city}>{city}</option>))}</select>
            <select value={filterStatus} onChange={e => setFilterStatus(e.target.value)} className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary bg-gray-50"><option value="">All Status</option><option value="active">Active</option><option value="inactive">Inactive</option><option value="pending">Pending</option></select>
            <select value={sortBy} onChange={e => setSortBy(e.target.value)} className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary bg-gray-50"><option value="name">Sort by Name</option><option value="category">Sort by Category</option><option value="rating">Sort by Rating</option><option value="experience">Sort by Experience</option></select>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredAndSortedVendors.map((vendor) => (
            <Card key={vendor.id} className="overflow-hidden rounded-2xl shadow border border-gray-100 bg-white hover:shadow-md transition-shadow">
              <CardContent className="p-6 flex flex-col h-full">
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1">
                    <h2 className="font-bold text-xl text-gray-900 mb-1 line-clamp-1">{vendor.name}</h2>
                    <p className="text-primary font-medium text-xs uppercase tracking-wide mb-1">{vendor.category}</p>
                  </div>
                  <div className="flex gap-2">
                    {vendor.verified && <Badge className="bg-green-600">Verified</Badge>}
                    {vendor.featured && <Badge className="bg-primary">Featured</Badge>}
                  </div>
                </div>
                <div className="text-gray-500 text-sm mb-2 flex items-center gap-2"><MapPin className="h-4 w-4 text-primary" />{vendor.city}, {vendor.state}</div>
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{vendor.description}</p>
                <div className="flex items-center justify-between text-sm mb-4">
                  <span className="font-bold text-lg text-primary">{vendor.priceRange || 'Contact for pricing'}</span>
                  <span className="flex items-center gap-1 bg-yellow-50 px-2 py-1 rounded-full ml-2"><Star className="h-4 w-4 fill-yellow-400 text-yellow-400" /><span className="text-sm font-semibold text-gray-900">{vendor.rating || "N/A"}</span></span>
                </div>
                <div className="flex gap-2 mt-auto">
                  <Button variant="outline" size="sm" onClick={() => openEdit(vendor)} className="flex-1">Edit</Button>
                  <Button variant="outline" size="sm" onClick={() => handleDelete(vendor)} className="flex-1">Delete</Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Add/Edit Vendor Modal */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 p-4">
          <div className="bg-white rounded-2xl shadow-2xl p-12 w-full max-w-4xl relative max-h-[95vh] overflow-y-auto">
            <button className="absolute top-3 right-3 text-gray-500 hover:text-primary text-2xl" onClick={() => setShowModal(false)}>&times;</button>
            <h2 className="text-3xl font-bold mb-8">{editingVendor === null ? "Add New Vendor" : "Edit Vendor"}</h2>
            {/* Progress Bar */}
            <div className="mb-8">
              <Progress 
                value={(modalStep / totalSteps) * 100}
                className="bg-gray-200"
                indicatorClassName="bg-[#800000]"
              />
              <div className="flex justify-between text-sm text-gray-500 mt-2">
                <span>Step {modalStep} of {totalSteps}</span>
                <span>{["Basic Info", "Location", "Professional", "Images & Skills"][modalStep-1]}</span>
              </div>
            </div>
            <form className="space-y-10" onSubmit={e => { e.preventDefault(); handleSave(); }}>
              {/* Step 1: Basic Information */}
              {modalStep === 1 && (
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Vendor Name *</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="name" value={form.name} onChange={handleChange} placeholder="Enter vendor/business name" required />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                      <select className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="category" value={form.category} onChange={handleChange} required>
                        <option value="">Select Category</option>
                        {categories.map(cat => (<option key={cat} value={cat}>{cat}</option>))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Contact Number *</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="contact" value={form.contact} onChange={handleChange} placeholder="+91 8148376909" required />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="email" type="email" value={form.email} onChange={handleChange} placeholder="<EMAIL>" />
                    </div>
                  </div>
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea className="border rounded-lg px-3 py-2 w-full h-24 focus:ring-2 focus:ring-primary focus:border-transparent" name="description" value={form.description} onChange={handleChange} placeholder="Describe your services and expertise" />
                  </div>
                </div>
              )}
              {/* Step 2: Location Information */}
              {modalStep === 2 && (
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Location Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="address" value={form.address} onChange={handleChange} placeholder="Street address" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">City *</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="city" value={form.city} onChange={handleChange} placeholder="City" required />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">State *</label>
                      <select className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="state" value={form.state} onChange={handleChange} required>
                        <option value="">Select State</option>
                        {states.map(state => (<option key={state} value={state}>{state}</option>))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Pincode</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="pincode" value={form.pincode} onChange={handleChange} placeholder="600001" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="website" value={form.website} onChange={handleChange} placeholder="www.vendor.com" />
                    </div>
                  </div>
                  <div className="mt-4">
                    <h4 className="text-md font-semibold mb-2">Social Media</h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Facebook</label>
                        <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={form.socialMedia.facebook} onChange={e => updateSocialMedia('facebook', e.target.value)} placeholder="facebook.com/yourpage" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Instagram</label>
                        <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={form.socialMedia.instagram} onChange={e => updateSocialMedia('instagram', e.target.value)} placeholder="instagram.com/yourpage" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">YouTube</label>
                        <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={form.socialMedia.youtube} onChange={e => updateSocialMedia('youtube', e.target.value)} placeholder="youtube.com/yourchannel" />
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {/* Step 3: Professional & Services */}
              {modalStep === 3 && (
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Professional & Services</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Experience</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="experience" value={form.experience} onChange={handleChange} placeholder="e.g., 10+ years" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Events Completed</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="events" value={form.events} onChange={handleChange} placeholder="e.g., 500+" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Response Time</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="responseTime" value={form.responseTime} onChange={handleChange} placeholder="e.g., 2 hours" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="priceRange" value={form.priceRange} onChange={handleChange} placeholder="e.g., ₹15,000 - ₹75,000" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Availability</label>
                      <select className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="availability" value={form.availability} onChange={handleChange}>
                        <option value="Available">Available</option>
                        <option value="Busy">Busy</option>
                        <option value="Unavailable">Unavailable</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                      <select className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="status" value={form.status} onChange={handleChange}>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="pending">Pending</option>
                      </select>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Rating (0-5)</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="rating" type="number" value={form.rating} onChange={handleChange} placeholder="4.8" min="0" max="5" step="0.1" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Review Count</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="reviewCount" type="number" value={form.reviewCount} onChange={handleChange} placeholder="124" min="0" />
                    </div>
                    <div className="flex items-center gap-4 pt-6">
                      <label className="flex items-center gap-2 text-sm">
                        <input type="checkbox" name="verified" checked={form.verified} onChange={handleChange} className="rounded border-gray-300 text-primary focus:ring-primary" />
                        <span className="font-medium">Verified Vendor</span>
                      </label>
                      <label className="flex items-center gap-2 text-sm">
                        <input type="checkbox" name="featured" checked={form.featured} onChange={handleChange} className="rounded border-gray-300 text-primary focus:ring-primary" />
                        <span className="font-medium">Featured</span>
                      </label>
                    </div>
                  </div>
                  {/* Services Management */}
                  <div className="mt-6">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="text-md font-semibold">Services & Pricing</h4>
                      <Button type="button" onClick={addService} className="bg-green-600 hover:bg-green-700">+ Add Service</Button>
                    </div>
                    {form.services.length === 0 ? (
                      <p className="text-gray-500 text-center py-4">No services added yet. Click "Add Service" to get started.</p>
                    ) : (
                      <div className="space-y-4">
                        {form.services.map((service, index) => (
                          <div key={index} className="bg-white p-4 rounded-lg border">
                            <div className="flex justify-between items-center mb-3">
                              <h5 className="font-medium text-gray-800">Service {index + 1}</h5>
                              <Button type="button" onClick={() => removeService(index)} variant="outline" size="sm" className="text-red-600 hover:text-red-700">Remove</Button>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Service Name</label>
                                <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={service.name} onChange={e => updateService(index, 'name', e.target.value)} placeholder="e.g., Wedding Photography" />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Price</label>
                                <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={service.price} onChange={e => updateService(index, 'price', e.target.value)} placeholder="e.g., ₹50,000" />
                              </div>
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                                <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={service.description} onChange={e => updateService(index, 'description', e.target.value)} placeholder="Brief service description" />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
              {/* Step 4: Images & Specializations */}
              {modalStep === 4 && (
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Images & Specializations</h3>
                  <div className="mb-6">
                    <ImageUpload images={form.gallery} onImagesChange={images => setForm(f => ({ ...f, gallery: images, profilePhoto: images[0] || "" }))} maxImages={12} label="Upload Vendor Images" description="Upload portfolio images (JPG, PNG, WebP) - Max 5MB each" />
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Or Add Image URLs</label>
                      <div className="flex gap-2">
                        <input className="border rounded-lg px-3 py-2 flex-1 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="https://example.com/portfolio-image.jpg" onKeyPress={e => { if (e.key === 'Enter') { const input = e.target as HTMLInputElement; if (input.value.trim()) { const newImages = [...form.gallery, input.value.trim()]; setForm(f => ({ ...f, gallery: newImages, profilePhoto: newImages[0] || "" })); input.value = ''; } } }} />
                        <Button type="button" variant="outline" onClick={e => { const input = (e.target as HTMLElement).previousElementSibling as HTMLInputElement; if (input.value.trim()) { const newImages = [...form.gallery, input.value.trim()]; setForm(f => ({ ...f, gallery: newImages, profilePhoto: newImages[0] || "" })); input.value = ''; } }}>Add URL</Button>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">You can also add images by URL. First image will be the profile photo.</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Specializations</label>
                      <div className="flex flex-wrap gap-2 mb-2">
                        {form.specializations.map((spec, index) => (
                          <span key={index} className="bg-primary/10 text-primary px-2 py-1 rounded text-sm flex items-center gap-1">{spec}<button type="button" onClick={() => removeFromArray('specializations', index)} className="text-red-500 hover:text-red-700">×</button></span>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <input className="border rounded-lg px-3 py-2 flex-1 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Add specialization" onKeyPress={e => { if (e.key === 'Enter') { const input = e.target as HTMLInputElement; if (input.value.trim()) { addToArray('specializations', input.value); input.value = ''; } } }} />
                        <Button type="button" variant="outline" onClick={e => { const input = (e.target as HTMLElement).previousElementSibling as HTMLInputElement; if (input.value.trim()) { addToArray('specializations', input.value); input.value = ''; } }}>Add</Button>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Languages</label>
                      <div className="flex flex-wrap gap-2 mb-2">
                        {form.languages.map((lang, index) => (
                          <span key={index} className="bg-primary/10 text-primary px-2 py-1 rounded text-sm flex items-center gap-1">{lang}<button type="button" onClick={() => removeFromArray('languages', index)} className="text-red-500 hover:text-red-700">×</button></span>
                        ))}
                      </div>
                      <div className="flex gap-2">
                        <input className="border rounded-lg px-3 py-2 flex-1 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Add language" onKeyPress={e => { if (e.key === 'Enter') { const input = e.target as HTMLInputElement; if (input.value.trim()) { addToArray('languages', input.value); input.value = ''; } } }} />
                        <Button type="button" variant="outline" onClick={e => { const input = (e.target as HTMLElement).previousElementSibling as HTMLInputElement; if (input.value.trim()) { addToArray('languages', input.value); input.value = ''; } }}>Add</Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {/* Navigation Buttons */}
              <div className="flex gap-3 justify-between pt-4 border-t mt-6">
                <Button type="button" variant="outline" onClick={() => setShowModal(false)} disabled={saving}>Cancel</Button>
                <div className="flex gap-2 ml-auto">
                  {modalStep > 1 && (
                    <Button type="button" variant="outline" onClick={() => setModalStep(modalStep - 1)} disabled={saving}>Back</Button>
                  )}
                  {modalStep < totalSteps && (
                    <Button type="button" className="bg-primary hover:bg-primary/90" onClick={() => validateStep(modalStep) && setModalStep(modalStep + 1)} disabled={!validateStep(modalStep) || saving}>Next</Button>
                  )}
                  {modalStep === totalSteps && (
                    <Button type="submit" className="bg-primary hover:bg-primary/90" disabled={saving}>
                      {saving ? (<><Loader2 className="h-4 w-4 animate-spin mr-2" />Saving...</>) : (editingVendor ? "Save Changes" : "Add Vendor")}
                    </Button>
                  )}
                </div>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
