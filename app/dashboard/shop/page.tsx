"use client"
import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ImageUpload } from "@/components/ui/image-upload";
import { Plus, Edit, Trash2, Eye, Package, Star, ShoppingBag, TrendingUp, Users, Loader2 } from "lucide-react";
import { shopService, ShopResponse, CreateShopInput, UpdateShopInput } from "@/lib/services/shopService";
import { useToast } from "@/hooks/use-toast";
import { getCurrentUser } from 'aws-amplify/auth';
import { Amplify } from 'aws-amplify';
import awsExports from '@/src/aws-exports';
import Link from "next/link";
import { Progress } from "@/components/ui/progress"

// Configure Amplify
Amplify.configure(awsExports);

const initialProducts = [
  {
    id: 1,
    name: "Designer Bridal Lehenga",
    category: "Bridal Wear",
    price: "₹85,000",
    originalPrice: "₹1,20,000",
    discount: 29,
    stock: 5,
    sku: "BL-2024-001",
    brand: "Royal Couture",
    featured: true,
    description: "Beautiful traditional wedding dress with intricate embroidery",
    features: ["Premium silk fabric", "Hand embroidered with gold thread", "Traditional Indian motifs", "Custom fitting available"],
    sizes: ["XS", "S", "M", "L", "XL", "XXL", "Custom"],
    colors: ["Red", "Maroon", "Pink", "Gold", "Royal Blue"],
    images: ["/api/placeholder/300/200"],
    specifications: {
      fabric: "Pure Silk",
      work: "Hand Embroidery",
      occasion: "Wedding, Reception",
      care: "Dry Clean Only",
      delivery: "7-10 business days",
      returnPolicy: "15 days return"
    },
    rating: 4.8,
    reviewCount: 124,
    inStock: true,
    status: "active"
  },
  {
    id: 2,
    name: "Gold Plated Jewelry Set",
    category: "Jewelry",
    price: "₹15,000",
    originalPrice: "",
    discount: 0,
    stock: 10,
    sku: "JS-2024-002",
    brand: "Golden Heritage",
    featured: false,
    description: "Exquisite gold jewelry set perfect for weddings",
    features: ["Gold plated finish", "Traditional design", "Matching earrings included", "Adjustable necklace"],
    sizes: ["Free Size"],
    colors: ["Gold", "Antique Gold"],
    images: ["/api/placeholder/300/200"],
    specifications: {
      fabric: "Metal Alloy",
      work: "Gold Plating",
      occasion: "Wedding, Festival",
      care: "Keep dry, avoid perfume",
      delivery: "3-5 business days",
      returnPolicy: "7 days return"
    },
    rating: 4.9,
    reviewCount: 89,
    inStock: true,
    status: "active"
  },
  {
    id: 3,
    name: "Wedding Decoration Package",
    category: "Decorations",
    price: "₹25,000",
    originalPrice: "₹35,000",
    discount: 28,
    stock: 10,
    sku: "WD-2024-003",
    brand: "Dream Decor",
    featured: true,
    description: "Complete decoration package for wedding ceremonies",
    features: ["Fresh flower arrangements", "LED lighting setup", "Backdrop decoration", "Table centerpieces"],
    sizes: ["Standard", "Premium", "Luxury"],
    colors: ["Red & Gold", "Pink & White", "Purple & Silver"],
    images: ["/api/placeholder/300/200"],
    specifications: {
      fabric: "Mixed Materials",
      work: "Professional Setup",
      occasion: "Wedding Ceremony",
      care: "Professional handling",
      delivery: "Same day setup",
      returnPolicy: "No returns on services"
    },
    rating: 4.7,
    reviewCount: 156,
    inStock: true,
    status: "active"
  },
  {
    id: 4,
    name: "Bridal Makeup Kit",
    category: "Beauty",
    price: "₹8,500",
    originalPrice: "",
    discount: 0,
    stock: 15,
    sku: "MK-2024-004",
    brand: "Glamour Pro",
    featured: false,
    description: "Professional bridal makeup kit with premium products",
    features: ["Long-lasting formula", "Waterproof products", "Complete kit", "Professional brushes included"],
    sizes: ["Full Kit", "Travel Size"],
    colors: ["Natural", "Bold", "Classic"],
    images: ["/api/placeholder/300/200"],
    specifications: {
      fabric: "Cosmetic Grade",
      work: "Professional Quality",
      occasion: "Bridal, Party",
      care: "Store in cool place",
      delivery: "2-3 business days",
      returnPolicy: "No returns on cosmetics"
    },
    rating: 4.6,
    reviewCount: 78,
    inStock: true,
    status: "active"
  }
];

export default function DashboardShopPage() {
  const [products, setProducts] = useState<ShopResponse[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [editIdx, setEditIdx] = useState<number | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterCategory, setFilterCategory] = useState("");
  const [filterStatus, setFilterStatus] = useState("");
  const [filterStock, setFilterStock] = useState("");
  const [sortBy, setSortBy] = useState("name");
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const { toast } = useToast();

  const [form, setForm] = useState({
    name: "",
    category: "",
    price: "",
    originalPrice: "",
    discount: 0,
    stock: 0,
    sku: "",
    brand: "",
    featured: false,
    description: "",
    features: [] as string[],
    sizes: [] as string[],
    colors: [] as string[],
    images: [] as string[],
    specifications: {
      fabric: "",
      work: "",
      occasion: "",
      care: "",
      delivery: "",
      returnPolicy: ""
    },
    rating: 0,
    reviewCount: 0,
    inStock: true,
    status: "active"
  });

  const [modalStep, setModalStep] = useState(1); // 1 to 4
  const totalSteps = 4;

  // Validation for each step
  const validateStep = (step: number) => {
    if (step === 1) {
      return form.name.trim() && form.category.trim();
    }
    if (step === 2) {
      return form.price.trim() && form.stock >= 0;
    }
    // Steps 3 and 4 can be less strict, or add more as needed
    return true;
  };

  // Reset modal step when opening/closing modal
  useEffect(() => {
    if (showModal) setModalStep(1);
  }, [showModal]);

  // Load user and products on component mount
  useEffect(() => {
    loadUserAndProducts();
  }, []);

  const loadUserAndProducts = async () => {
    try {
      setLoading(true);
      const user = await getCurrentUser();
      console.log('Current user:', user); // Debug log
      setCurrentUser(user);

      // Try different user ID fields - Cognito typically uses 'sub' as the user identifier
      const userId = user?.sub || user?.userId || user?.username;
      console.log('Using userId:', userId); // Debug log

      if (userId) {
        const userShops = await shopService.getShopsByUserId(userId);
        setProducts(userShops.items);
      } else {
        console.warn('No valid user ID found');
        toast({
          title: "Authentication Required",
          description: "Please log in to manage your products.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading user and products:', error);
      if (error.message?.includes('not authenticated')) {
        toast({
          title: "Authentication Required",
          description: "Please log in to access your products.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to load products. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // Filter and sort products
  const filteredAndSortedProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.brand?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = !filterCategory || product.category === filterCategory;
    const matchesStatus = !filterStatus || product.status === filterStatus;
    const matchesStock = !filterStock || (() => {
      switch (filterStock) {
        case "in-stock": return product.inStock;
        case "out-of-stock": return !product.inStock;
        case "low-stock": return product.stock && product.stock < 10;
        default: return true;
      }
    })();

    return matchesSearch && matchesCategory && matchesStatus && matchesStock;
  }).sort((a, b) => {
    switch (sortBy) {
      case "name":
        return a.name.localeCompare(b.name);
      case "category":
        return a.category.localeCompare(b.category);
      case "price":
        const priceA = parseFloat(a.price.replace(/[^\d.]/g, '')) || 0;
        const priceB = parseFloat(b.price.replace(/[^\d.]/g, '')) || 0;
        return priceA - priceB;
      case "stock":
        return (b.stock || 0) - (a.stock || 0);
      case "rating":
        return (b.rating || 0) - (a.rating || 0);
      default:
        return a.name.localeCompare(b.name);
    }
  });

  // Get unique values for filters
  const getUniqueCategories = () => {
    const categories = products.map(p => p.category).filter(Boolean);
    return [...new Set(categories)];
  };

  const getUniqueBrands = () => {
    const brands = products.map(p => p.brand).filter(Boolean);
    return [...new Set(brands)];
  };

  // Selection handlers
  const handleSelectProduct = (productId: string) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleSelectAll = () => {
    if (selectedProducts.length === filteredAndSortedProducts.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(filteredAndSortedProducts.map(product => product.id!).filter(Boolean));
    }
  };

  // Create new product
  const handleCreate = async () => {
    const userId = currentUser?.sub || currentUser?.userId || currentUser?.username;
    if (!userId) {
      toast({
        title: "Error",
        description: "User not authenticated",
        variant: "destructive",
      });
      return;
    }

    try {
      setSubmitting(true);
      const createInput: CreateShopInput = {
        userId: userId,
        name: form.name,
        category: form.category,
        price: form.price,
        originalPrice: form.originalPrice || undefined,
        discount: form.discount,
        stock: form.stock,
        sku: form.sku || undefined,
        brand: form.brand || undefined,
        featured: form.featured,
        description: form.description || undefined,
        features: form.features.length > 0 ? form.features : undefined,
        sizes: form.sizes.length > 0 ? form.sizes : undefined,
        colors: form.colors.length > 0 ? form.colors : undefined,
        images: form.images.length > 0 ? form.images : undefined,
        specifications: Object.values(form.specifications).some(val => val && val.trim()) ? form.specifications : undefined,
        rating: form.rating,
        reviewCount: form.reviewCount,
        inStock: form.inStock,
        status: form.status
      };

      const newProduct = await shopService.createShop(createInput);
      setProducts(prev => [...prev, newProduct]);

      toast({
        title: "Success",
        description: "Product created successfully!",
      });

      setShowModal(false);
      resetForm();
    } catch (error) {
      console.error('Error creating product:', error);
      toast({
        title: "Error",
        description: "Failed to create product. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Update existing product
  const handleUpdate = async () => {
    if (editIdx === null) return;

    try {
      setSubmitting(true);
      const productToUpdate = products[editIdx];

      console.log('Updating product:', productToUpdate.id);
      console.log('Form data:', form);

      const updateInput: UpdateShopInput = {
        id: productToUpdate.id,
        // Don't include userId in updates - it should remain the same
        name: form.name,
        category: form.category,
        price: form.price,
        originalPrice: form.originalPrice || undefined,
        discount: form.discount,
        stock: form.stock,
        sku: form.sku || undefined,
        brand: form.brand || undefined,
        featured: form.featured,
        description: form.description || undefined,
        features: form.features.length > 0 ? form.features : undefined,
        sizes: form.sizes.length > 0 ? form.sizes : undefined,
        colors: form.colors.length > 0 ? form.colors : undefined,
        images: form.images.length > 0 ? form.images : undefined,
        specifications: Object.values(form.specifications).some(val => val && val.trim()) ? form.specifications : undefined,
        rating: form.rating,
        reviewCount: form.reviewCount,
        inStock: form.inStock,
        status: form.status
      };

      console.log('Update input:', updateInput);

      const updatedProduct = await shopService.updateShop(updateInput);

      console.log('Updated product response:', updatedProduct);

      setProducts(prev => prev.map((product) =>
        product.id === updatedProduct.id ? updatedProduct : product
      ));

      toast({
        title: "Success",
        description: "Product updated successfully!",
      });

      setShowModal(false);
      resetForm();
    } catch (error) {
      console.error('Error updating product:', error);
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);

      toast({
        title: "Error",
        description: `Failed to update product: ${error.message || 'Unknown error'}`,
        variant: "destructive",
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Delete product
  const handleDelete = async (index: number) => {
    if (!confirm('Are you sure you want to delete this product?')) return;

    try {
      const productToDelete = products[index];
      await shopService.deleteShop(productToDelete.id);

      setProducts(prev => prev.filter((_, i) => i !== index));

      toast({
        title: "Success",
        description: "Product deleted successfully!",
      });
    } catch (error) {
      console.error('Error deleting product:', error);
      toast({
        title: "Error",
        description: "Failed to delete product. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Reset form to initial state
  // Validate form data
  const validateForm = () => {
    const errors = [];

    if (!form.name.trim()) errors.push("Product name is required");
    if (!form.category.trim()) errors.push("Category is required");
    if (!form.price.trim()) errors.push("Price is required");
    if (form.stock < 0) errors.push("Stock cannot be negative");

    return errors;
  };

  const resetForm = () => {
    setForm({
      name: "",
      category: "",
      price: "",
      originalPrice: "",
      discount: 0,
      stock: 0,
      sku: "",
      brand: "",
      featured: false,
      description: "",
      features: [],
      sizes: [],
      colors: [],
      images: [],
      specifications: {
        fabric: "",
        work: "",
        occasion: "",
        care: "",
        delivery: "",
        returnPolicy: ""
      },
      rating: 0,
      reviewCount: 0,
      inStock: true,
      status: "active"
    });
    setEditIdx(null);
  };

  const categories = [
    "Bridal Wear",
    "Groom Wear",
    "Jewelry",
    "Decorations",
    "Beauty",
    "Accessories",
    "Invitations",
    "Gifts"
  ];

  const availableSizes = ["XS", "S", "M", "L", "XL", "XXL", "Custom", "Free Size"];
  const availableColors = ["Red", "Maroon", "Pink", "Gold", "Royal Blue", "Green", "Purple", "Orange", "Yellow", "Black", "White", "Silver"];

  // Helper functions for array fields
  const addToArray = (field: string, value: string) => {
    if (value.trim()) {
      setForm(f => ({
        ...f,
        [field]: [...f[field as keyof typeof f] as string[], value.trim()]
      }));
    }
  };

  const removeFromArray = (field: string, index: number) => {
    setForm(f => ({
      ...f,
      [field]: (f[field as keyof typeof f] as string[]).filter((_, i) => i !== index)
    }));
  };

  const updateSpecification = (key: string, value: string) => {
    setForm(f => ({
      ...f,
      specifications: {
        ...f.specifications,
        [key]: value
      }
    }));
  };

  const openAdd = () => {
    resetForm();
    setShowModal(true);
  };

  const openEdit = (idx: number) => {
    const product = products[idx];
    setForm({
      name: product.name,
      category: product.category,
      price: product.price,
      originalPrice: product.originalPrice || "",
      discount: product.discount || 0,
      stock: product.stock,
      sku: product.sku || "",
      brand: product.brand || "",
      featured: product.featured || false,
      description: product.description || "",
      features: product.features || [],
      sizes: product.sizes || [],
      colors: product.colors || [],
      images: product.images || [],
      specifications: product.specifications || {
        fabric: "",
        work: "",
        occasion: "",
        care: "",
        delivery: "",
        returnPolicy: ""
      },
      rating: product.rating || 0,
      reviewCount: product.reviewCount || 0,
      inStock: product.inStock,
      status: product.status
    });
    setEditIdx(idx);
    setShowModal(true);
  };
  const handleChange = (e: any) => {
    const { name, value, type, checked } = e.target;
    if (type === "checkbox") {
      setForm((f) => ({ ...f, [name]: checked }));
    } else if (name === "stock" || name === "discount" || name === "rating" || name === "reviewCount") {
      setForm((f) => ({ ...f, [name]: Number(value) }));
    } else {
      setForm((f) => ({ ...f, [name]: value }));
    }
  };
  const handleSave = async () => {
    // Validate form before saving
    const validationErrors = validateForm();
    if (validationErrors.length > 0) {
      toast({
        title: "Validation Error",
        description: validationErrors.join(", "),
        variant: "destructive",
      });
      return;
    }

    if (editIdx === null) {
      await handleCreate();
    } else {
      await handleUpdate();
    }
  };
  const handleDeleteProduct = async (idx: number) => {
    await handleDelete(idx);
  };

  return (
    <div>
      <div className="space-y-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-1">Manage Shop Products</h1>
            <p className="text-gray-500 text-base">{filteredAndSortedProducts.length} of {products.length} products{selectedProducts.length > 0 && ` • ${selectedProducts.length} selected`}</p>
          </div>
          <div className="flex gap-2">
            {selectedProducts.length > 0 && (
              <Button variant="outline" onClick={() => { if (window.confirm(`Delete ${selectedProducts.length} selected products?`)) { selectedProducts.forEach(id => { const index = products.findIndex(p => p.id === id); if (index !== -1) handleDelete(index); }); setSelectedProducts([]); } }} className="text-red-600 hover:text-red-700 hover:bg-red-50">Delete Selected ({selectedProducts.length})</Button>
            )}
            <Button className="bg-primary hover:bg-primary/90" onClick={openAdd}>+ Add Product</Button>
          </div>
        </div>
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <input type="text" placeholder="Search products..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="w-full px-3 py-2 pl-10 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary bg-gray-50" />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</div>
            </div>
            <select value={filterCategory} onChange={e => setFilterCategory(e.target.value)} className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary bg-gray-50"><option value="">All Categories</option>{getUniqueCategories().map(category => (<option key={category} value={category}>{category}</option>))}</select>
            <select value={filterStock} onChange={e => setFilterStock(e.target.value)} className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary bg-gray-50"><option value="">All Stock</option><option value="in-stock">In Stock</option><option value="out-of-stock">Out of Stock</option><option value="low-stock">Low Stock (&lt;10)</option></select>
            <select value={filterStatus} onChange={e => setFilterStatus(e.target.value)} className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary bg-gray-50"><option value="">All Status</option><option value="active">Active</option><option value="inactive">Inactive</option><option value="draft">Draft</option></select>
            <select value={sortBy} onChange={e => setSortBy(e.target.value)} className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary bg-gray-50"><option value="name">Sort by Name</option><option value="category">Sort by Category</option><option value="price">Sort by Price</option><option value="stock">Sort by Stock</option><option value="rating">Sort by Rating</option></select>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredAndSortedProducts.map((product, idx) => {
            const originalIndex = products.findIndex(p => p.id === product.id);
            return (
              <Card key={product.id} className="overflow-hidden rounded-2xl shadow border border-gray-100 bg-white hover:shadow-md transition-shadow">
                <CardContent className="p-6 flex flex-col h-full">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h2 className="font-bold text-xl text-gray-900 mb-1 line-clamp-1">{product.name}</h2>
                      <p className="text-primary font-medium text-xs uppercase tracking-wide mb-1">{product.category}</p>
                    </div>
                    <div className="flex gap-2">
                      {product.featured && <Badge className="bg-primary">Featured</Badge>}
                      <Badge variant={product.status === 'active' ? 'default' : 'secondary'}>{product.status}</Badge>
                    </div>
                  </div>
                  <div className="text-gray-500 text-sm mb-2 flex items-center gap-2"><span className="font-semibold">Stock:</span> <span className={product.stock < 5 ? 'text-red-500 font-semibold' : ''}>{product.stock}</span></div>
                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">{product.description}</p>
                  <div className="flex items-center justify-between text-sm mb-4">
                    <span className="font-bold text-lg text-primary">{product.price}</span>
                    <span className="flex items-center gap-1 bg-yellow-50 px-2 py-1 rounded-full ml-2"><Star className="h-4 w-4 fill-yellow-400 text-yellow-400" /><span className="text-sm font-semibold text-gray-900">{product.rating || "N/A"}</span></span>
                  </div>
                  <div className="flex gap-2 mt-auto">
                    <Button variant="outline" size="sm" onClick={() => openEdit(originalIndex)} className="flex-1">Edit</Button>
                    <Button variant="outline" size="sm" onClick={() => handleDeleteProduct(originalIndex)} className="flex-1">Delete</Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
      {/* Add/Edit Product Modal */}
      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 p-4">
          <div className="bg-white rounded-2xl shadow-2xl p-12 w-full max-w-4xl relative max-h-[95vh] overflow-y-auto">
            <button className="absolute top-3 right-3 text-gray-500 hover:text-primary text-2xl" onClick={() => setShowModal(false)}>&times;</button>
            <h2 className="text-3xl font-bold mb-8">{editIdx === null ? "Add New Product" : "Edit Product"}</h2>
            {/* Progress Bar */}
            <div className="mb-8">
              <Progress 
                value={(modalStep / totalSteps) * 100}
                className="bg-gray-200"
                indicatorClassName="bg-[#800000]"
              />
              <div className="flex justify-between text-sm text-gray-500 mt-2">
                <span>Step {modalStep} of {totalSteps}</span>
                <span>{["Basic Info", "Pricing & Inventory", "Features & Images", "Specs & Status"][modalStep-1]}</span>
              </div>
            </div>
            <form className="space-y-10" onSubmit={e => { e.preventDefault(); handleSave(); }}>
              {/* Step 1: Basic Information */}
              {modalStep === 1 && (
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Product Name *</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="name" value={form.name} onChange={handleChange} placeholder="Enter product name" required />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                      <select className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="category" value={form.category} onChange={handleChange} required>
                        <option value="">Select Category</option>
                        {categories.map(cat => (<option key={cat} value={cat}>{cat}</option>))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Brand</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="brand" value={form.brand} onChange={handleChange} placeholder="Enter brand name" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">SKU</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="sku" value={form.sku} onChange={handleChange} placeholder="Product SKU" />
                    </div>
                  </div>
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea className="border rounded-lg px-3 py-2 w-full h-24 focus:ring-2 focus:ring-primary focus:border-transparent" name="description" value={form.description} onChange={handleChange} placeholder="Enter detailed product description" />
                  </div>
                </div>
              )}
              {/* Step 2: Pricing & Inventory */}
              {modalStep === 2 && (
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Pricing & Inventory</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Current Price *</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="price" value={form.price} onChange={handleChange} placeholder="₹0" required />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Original Price</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="originalPrice" value={form.originalPrice} onChange={handleChange} placeholder="₹0" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Discount %</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="discount" type="number" value={form.discount} onChange={handleChange} placeholder="0" min="0" max="100" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Stock Quantity *</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="stock" type="number" value={form.stock} onChange={handleChange} placeholder="0" min="0" required />
                    </div>
                    <div className="flex items-center pt-6">
                      <label className="flex items-center gap-2 text-sm">
                        <input type="checkbox" name="inStock" checked={form.inStock} onChange={handleChange} className="rounded border-gray-300 text-primary focus:ring-primary" />
                        <span className="font-medium">In Stock</span>
                      </label>
                    </div>
                    <div className="flex items-center pt-6">
                      <label className="flex items-center gap-2 text-sm">
                        <input type="checkbox" name="featured" checked={form.featured} onChange={handleChange} className="rounded border-gray-300 text-primary focus:ring-primary" />
                        <span className="font-medium">Featured Product</span>
                      </label>
                    </div>
                  </div>
                </div>
              )}
              {/* Step 3: Features & Images */}
              {modalStep === 3 && (
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Product Features & Images</h3>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Features (one per line)</label>
                    <textarea className="border rounded-lg px-3 py-2 w-full h-24 focus:ring-2 focus:ring-primary focus:border-transparent" value={form.features.join('\n')} onChange={e => setForm(f => ({ ...f, features: e.target.value.split('\n').filter(f => f.trim()) }))} placeholder="Premium silk fabric\nHand embroidered with gold thread\nTraditional Indian motifs\nCustom fitting available" />
                    <p className="text-xs text-gray-500 mt-1">Enter each feature on a new line</p>
                  </div>
                  <div className="mt-6">
                    <h4 className="text-md font-semibold mb-2">Product Images</h4>
                    <ImageUpload images={form.images} onImagesChange={images => setForm(f => ({ ...f, images }))} maxImages={8} label="Upload Product Images" description="Upload product images (JPG, PNG, WebP) - Max 5MB each" />
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Or Add Image URLs</label>
                      <div className="flex gap-2">
                        <input className="border rounded-lg px-3 py-2 flex-1 focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="https://example.com/image.jpg" onKeyPress={e => { if (e.key === 'Enter') { const input = e.target as HTMLInputElement; if (input.value.trim()) { setForm(f => ({ ...f, images: [...f.images, input.value.trim()] })); input.value = ''; } } }} />
                        <Button type="button" variant="outline" onClick={e => { const input = (e.target as HTMLElement).previousElementSibling as HTMLInputElement; if (input.value.trim()) { setForm(f => ({ ...f, images: [...f.images, input.value.trim()] })); input.value = ''; } }}>Add URL</Button>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">You can also add images by URL.</p>
                    </div>
                  </div>
                </div>
              )}
              {/* Step 4: Specifications & Status */}
              {modalStep === 4 && (
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h3 className="text-lg font-semibold mb-4">Specifications & Status</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Fabric</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={form.specifications.fabric} onChange={e => updateSpecification('fabric', e.target.value)} placeholder="e.g., Pure Silk" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Work</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={form.specifications.work} onChange={e => updateSpecification('work', e.target.value)} placeholder="e.g., Hand Embroidery" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Occasion</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={form.specifications.occasion} onChange={e => updateSpecification('occasion', e.target.value)} placeholder="e.g., Wedding, Reception" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Care Instructions</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={form.specifications.care} onChange={e => updateSpecification('care', e.target.value)} placeholder="e.g., Dry Clean Only" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Delivery Time</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={form.specifications.delivery} onChange={e => updateSpecification('delivery', e.target.value)} placeholder="e.g., 7-10 business days" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Return Policy</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" value={form.specifications.returnPolicy} onChange={e => updateSpecification('returnPolicy', e.target.value)} placeholder="e.g., 15 days return" />
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                      <select className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="status" value={form.status} onChange={handleChange}>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="draft">Draft</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Rating (0-5)</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="rating" type="number" value={form.rating} onChange={handleChange} placeholder="4.5" min="0" max="5" step="0.1" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Review Count</label>
                      <input className="border rounded-lg px-3 py-2 w-full focus:ring-2 focus:ring-primary focus:border-transparent" name="reviewCount" type="number" value={form.reviewCount} onChange={handleChange} placeholder="0" min="0" />
                    </div>
                  </div>
                </div>
              )}
              {/* Navigation Buttons */}
              <div className="flex gap-3 justify-between pt-4 border-t mt-6">
                <Button type="button" variant="outline" onClick={() => setShowModal(false)} disabled={submitting}>Cancel</Button>
                <div className="flex gap-2 ml-auto">
                  {modalStep > 1 && (
                    <Button type="button" variant="outline" onClick={() => setModalStep(modalStep - 1)} disabled={submitting}>Back</Button>
                  )}
                  {modalStep < totalSteps && (
                    <Button type="button" className="bg-primary hover:bg-primary/90" onClick={() => validateStep(modalStep) && setModalStep(modalStep + 1)} disabled={!validateStep(modalStep) || submitting}>Next</Button>
                  )}
                  {modalStep === totalSteps && (
                    <Button type="submit" className="bg-primary hover:bg-primary/90" disabled={submitting}>
                      {submitting ? (<><Loader2 className="h-4 w-4 animate-spin mr-2" />{editIdx === null ? "Adding..." : "Saving..."}</>) : (editIdx === null ? "Add Product" : "Save Changes")}
                    </Button>
                  )}
                </div>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}