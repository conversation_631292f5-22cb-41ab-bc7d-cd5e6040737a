"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Briefcase,
  Search,
  Plus,
  Trash2,
  Eye,
  CheckCircle,
  XCircle,
  Star,
  MapPin,
  Mail,
} from 'lucide-react';
import { AdminOnlyRoute } from '@/components/RouteProtection';
import AdminContentService, { ContentFilters } from '@/lib/services/adminContentService';
import toast from 'react-hot-toast';

export default function AdminVendorsPage() {
  const [vendors, setVendors] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedVendors, setSelectedVendors] = useState<string[]>([]);
  const [filters, setFilters] = useState<ContentFilters>({});
  const [showVendorDetails, setShowVendorDetails] = useState(false);
  const [selectedVendor, setSelectedVendor] = useState<any>(null);
  const [vendorStats, setVendorStats] = useState<any>(null);
  const [nextToken, setNextToken] = useState<string | undefined>();
  const [hasMore, setHasMore] = useState(true);
  const [sortBy, setSortBy] = useState('name');
  // Add state for edit form
  const [editForm, setEditForm] = useState<any>(null);
  const [saving, setSaving] = useState(false);
  // Add state for multi-step form
  const [editStep, setEditStep] = useState(1);
  const totalEditSteps = 4;

  useEffect(() => {
    loadVendors();
    loadVendorStats();
  }, []);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      loadVendors();
    }, 500);
    return () => clearTimeout(delayedSearch);
  }, [searchTerm, filters, sortBy]);

  const loadVendors = async (loadMore: boolean = false) => {
    try {
      setLoading(true);
      const searchFilters: ContentFilters = {
        ...filters,
        searchTerm: searchTerm || undefined,
        // sortBy removed to fix linter error
      };
      const result = await AdminContentService.getAllVendors(
        searchFilters,
        50,
        loadMore ? nextToken : undefined
      );
      if (result.success && result.data) {
        if (loadMore) {
          setVendors(prev => [...prev, ...result.data!.vendors]);
        } else {
          setVendors(result.data.vendors);
        }
        setNextToken(result.data.nextToken);
        setHasMore(!!result.data.nextToken);
      } else {
        toast.error(result.error || 'Failed to load vendors');
      }
    } catch (error) {
      console.error('Error loading vendors:', error);
      toast.error('Failed to load vendors');
    } finally {
      setLoading(false);
    }
  };

  const loadVendorStats = async () => {
    try {
      const result = await AdminContentService.getContentStatistics();
      if (result.success) {
        setVendorStats(result.data);
      }
    } catch (error) {
      console.error('Error loading vendor stats:', error);
    }
  };

  const handleDeleteVendor = async (vendorId: string) => {
    if (!confirm('Are you sure you want to delete this vendor? This action cannot be undone.')) {
      return;
    }
    try {
      const result = await AdminContentService.deleteVendor(vendorId);
      if (result.success) {
        toast.success('Vendor deleted successfully');
        loadVendors();
        loadVendorStats();
      } else {
        toast.error(result.error || 'Failed to delete vendor');
      }
    } catch (error) {
      console.error('Error deleting vendor:', error);
      toast.error('Failed to delete vendor');
    }
  };

  const handleBulkDelete = async () => {
    if (selectedVendors.length === 0) return;
    if (!confirm(`Delete ${selectedVendors.length} selected vendors?`)) return;
    try {
      const result = await AdminContentService.bulkDelete('vendor', selectedVendors);
      if (result.success) {
        toast.success(`${selectedVendors.length} vendors deleted successfully`);
        setSelectedVendors([]);
        loadVendors();
        loadVendorStats();
      } else {
        toast.error(result.error || 'Failed to delete vendors');
      }
    } catch (error) {
      console.error('Error deleting vendors:', error);
      toast.error('Failed to delete vendors');
    }
  };

  const handleSelectAll = () => {
    if (selectedVendors.length === vendors.length) {
      setSelectedVendors([]);
    } else {
      setSelectedVendors(vendors.map(vendor => vendor.id));
    }
  };

  const handleVendorSelect = (vendorId: string) => {
    setSelectedVendors(prev =>
      prev.includes(vendorId)
        ? prev.filter(id => id !== vendorId)
        : [...prev, vendorId]
    );
  };

  const applyFilters = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value || undefined }));
    setNextToken(undefined);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setSortBy('name');
    setNextToken(undefined);
  };

  // When opening modal, reset step to 1
  const openVendorDetails = (vendor: any) => {
    setSelectedVendor(vendor);
    setEditForm({ ...vendor, services: vendor.services ? [...vendor.services] : [], gallery: vendor.gallery ? [...vendor.gallery] : [], specializations: vendor.specializations ? [...vendor.specializations] : [], languages: vendor.languages ? [...vendor.languages] : [] });
    setEditStep(1);
    setShowVendorDetails(true);
  };

  // Handle form field changes
  const handleEditChange = (field: string, value: any) => {
    setEditForm((prev: any) => ({ ...prev, [field]: value }));
  };

  // Handle service changes
  const handleServiceChange = (idx: number, key: string, value: string) => {
    setEditForm((prev: any) => {
      const services = [...(prev.services || [])];
      services[idx] = { ...services[idx], [key]: value };
      return { ...prev, services };
    });
  };
  const addService = () => {
    setEditForm((prev: any) => ({ ...prev, services: [...(prev.services || []), { name: '', price: '' }] }));
  };
  const removeService = (idx: number) => {
    setEditForm((prev: any) => {
      const services = [...(prev.services || [])];
      services.splice(idx, 1);
      return { ...prev, services };
    });
  };

  // Save handler
  const handleSaveEdit = async () => {
    if (!editForm) return;
    setSaving(true);
    try {
      // Sanitize input: only send valid fields, remove undefined/null/empty, clean services
      const allowedFields = [
        'name', 'category', 'email', 'contact', 'address', 'city', 'state', 'rating', 'status', 'description', 'featured', 'verified', 'services',
        'pincode', 'website', 'profilePhoto', 'gallery', 'experience', 'events', 'responseTime', 'reviewCount', 'availability', 'priceRange', 'specializations', 'awards', 'languages', 'coverage', 'equipment'
      ];
      const cleaned: any = {};
      for (const key of allowedFields) {
        if (key in editForm && editForm[key] !== undefined && editForm[key] !== null && editForm[key] !== '') {
          if (key === 'services' && Array.isArray(editForm.services)) {
            cleaned.services = editForm.services
              .filter((s: any) => s && s.name && s.name.trim() && s.price && s.price.toString().trim())
              .map((s: any) => ({ name: s.name.trim(), price: s.price.toString().trim() }));
          } else {
            cleaned[key] = editForm[key];
          }
        }
      }
      cleaned.id = editForm.id;
      const result = await AdminContentService.updateVendor(editForm.id, cleaned);
      if (result.success) {
        toast.success('Vendor updated successfully');
        setShowVendorDetails(false);
        setEditForm(null);
        loadVendors();
      } else {
        toast.error(result.error || 'Failed to update vendor');
      }
    } catch (error) {
      toast.error('Failed to update vendor');
    } finally {
      setSaving(false);
    }
  };

  const getStatusBadge = (vendor: any) => {
    if (vendor.verified) {
      return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Verified</Badge>;
    }
    return <Badge variant="secondary"><XCircle className="w-3 h-3 mr-1" />Unverified</Badge>;
  };
  const getFeaturedBadge = (vendor: any) => {
    if (vendor.featured) {
      return <Badge className="bg-yellow-100 text-yellow-800"><Star className="w-3 h-3 mr-1" />Featured</Badge>;
    }
    return null;
  };

  // --- UI ---
  return (
    <AdminOnlyRoute>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-1">Manage Vendors</h1>
            <p className="text-gray-500 text-base">{vendors.length} vendor{vendors.length !== 1 ? 's' : ''}</p>
          </div>
          <div className="flex gap-2">
            {selectedVendors.length > 0 && (
              <Button variant="outline" onClick={handleBulkDelete} className="text-red-600 hover:text-red-700 hover:bg-red-50">Delete Selected ({selectedVendors.length})</Button>
            )}
            <Button className="bg-primary hover:bg-primary/90"><Plus className="w-4 h-4 mr-2" />Add Vendor</Button>
          </div>
        </div>
        {/* Filters */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            <div className="relative">
              <Input type="text" placeholder="Search vendors..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="w-full pl-10" />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            </div>
            <Select value={filters.category || 'all'} onValueChange={value => applyFilters('category', value === 'all' ? undefined : value)}>
              <SelectTrigger><SelectValue placeholder="All Categories" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="Photography">Photography</SelectItem>
                <SelectItem value="Catering">Catering</SelectItem>
                <SelectItem value="Decoration">Decoration</SelectItem>
                <SelectItem value="Music">Music</SelectItem>
                <SelectItem value="Makeup">Makeup</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filters.status || 'all'} onValueChange={value => applyFilters('status', value === 'all' ? undefined : value)}>
              <SelectTrigger><SelectValue placeholder="All Status" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filters.verified !== undefined ? String(filters.verified) : 'all'} onValueChange={value => applyFilters('verified', value === 'all' ? undefined : value === 'true')}>
              <SelectTrigger><SelectValue placeholder="All Vendors" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Vendors</SelectItem>
                <SelectItem value="true">Verified Only</SelectItem>
                <SelectItem value="false">Unverified Only</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filters.featured !== undefined ? String(filters.featured) : 'all'} onValueChange={value => applyFilters('featured', value === 'all' ? undefined : value === 'true')}>
              <SelectTrigger><SelectValue placeholder="All Vendors" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Vendors</SelectItem>
                <SelectItem value="true">Featured Only</SelectItem>
                <SelectItem value="false">Non-Featured</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={value => setSortBy(value)}>
              <SelectTrigger><SelectValue placeholder="Sort by Name" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Sort by Name</SelectItem>
                <SelectItem value="category">Sort by Category</SelectItem>
                <SelectItem value="rating">Sort by Rating</SelectItem>
                <SelectItem value="city">Sort by City</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        {/* Vendor Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {loading ? (
            <div className="col-span-full text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading vendors...</p>
            </div>
          ) : vendors.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <Briefcase className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No vendors found</p>
            </div>
          ) : (
            vendors.map((vendor) => (
              <Card key={vendor.id} className="overflow-hidden rounded-2xl shadow border border-gray-100 bg-white hover:shadow-md transition-shadow relative">
                <CardContent className="p-6 flex flex-col h-full">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h2 className="font-bold text-xl text-gray-900 mb-1 line-clamp-1">{vendor.name}</h2>
                      <p className="text-primary font-medium text-xs uppercase tracking-wide mb-1">{vendor.category}</p>
                    </div>
                    <div className="flex gap-2">
                      {getFeaturedBadge(vendor)}
                      <Badge variant={vendor.status === 'active' ? 'default' : 'secondary'}>{vendor.status || 'active'}</Badge>
                    </div>
                  </div>
                  <div className="text-gray-500 text-sm mb-2 flex items-center gap-2">
                    <Mail className="w-3 h-3" />
                    <span className="truncate">{vendor.email}</span>
                  </div>
                  <div className="text-gray-500 text-sm mb-2 flex items-center gap-2">
                    <MapPin className="w-3 h-3" />
                    <span>{vendor.city}, {vendor.state}</span>
                  </div>
                  <div className="flex items-center gap-2 mt-1 mb-2">
                    {getStatusBadge(vendor)}
                  </div>
                  <div className="flex items-center justify-between text-sm mb-4">
                    <span className="flex items-center gap-1 bg-yellow-50 px-2 py-1 rounded-full ml-2"><Star className="h-4 w-4 fill-yellow-400 text-yellow-400" /><span className="text-sm font-semibold text-gray-900">{vendor.rating || 'N/A'}</span></span>
                  </div>
                  <div className="flex gap-2 mt-auto">
                    <Button variant="outline" size="sm" onClick={() => openVendorDetails(vendor)} className="flex-1">Edit</Button>
                    <Button variant="outline" size="sm" onClick={() => handleDeleteVendor(vendor.id)} className="flex-1 text-red-600 hover:text-red-700">Delete</Button>
                    <Checkbox checked={selectedVendors.includes(vendor.id)} onCheckedChange={() => handleVendorSelect(vendor.id)} className="ml-2 mt-1" />
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
        {/* Load More Button */}
        {hasMore && !loading && vendors.length > 0 && (
          <div className="text-center pt-4">
            <Button variant="outline" onClick={() => loadVendors(true)} disabled={loading}>
              {loading ? 'Loading...' : 'Load More Vendors'}
            </Button>
          </div>
        )}
        {/* Vendor Details Modal */}
        <Dialog open={showVendorDetails} onOpenChange={setShowVendorDetails}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto rounded-2xl">
            <DialogHeader>
              <DialogTitle>Edit Vendor</DialogTitle>
            </DialogHeader>
            {editForm && (
              <form className="space-y-6" onSubmit={e => { e.preventDefault(); handleSaveEdit(); }}>
                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="w-full h-3 rounded-full bg-gray-200 mb-1">
                    <div className="h-3 rounded-full bg-[#800000] transition-all" style={{ width: `${(editStep / totalEditSteps) * 100}%` }} />
                  </div>
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>Step {editStep} of {totalEditSteps}</span>
                    <span>{['Basic Info', 'Location', 'Professional', 'Images & Skills'][editStep-1]}</span>
                  </div>
                </div>
                {/* Step 1: Basic Info */}
                {editStep === 1 && (
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Basic Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Vendor Name *</label>
                        <Input value={editForm.name || ''} onChange={e => handleEditChange('name', e.target.value)} required />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                        <Input value={editForm.category || ''} onChange={e => handleEditChange('category', e.target.value)} required />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Contact Number *</label>
                        <Input value={editForm.contact || ''} onChange={e => handleEditChange('contact', e.target.value)} required />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <Input value={editForm.email || ''} onChange={e => handleEditChange('email', e.target.value)} />
                      </div>
                      <div className="col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea className="border rounded-lg px-3 py-2 w-full h-24 focus:ring-2 focus:ring-primary focus:border-transparent" value={editForm.description || ''} onChange={e => handleEditChange('description', e.target.value)} />
                      </div>
                    </div>
                  </div>
                )}
                {/* Step 2: Location */}
                {editStep === 2 && (
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Location Information</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
                        <Input value={editForm.address || ''} onChange={e => handleEditChange('address', e.target.value)} />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">City *</label>
                        <Input value={editForm.city || ''} onChange={e => handleEditChange('city', e.target.value)} required />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">State *</label>
                        <Input value={editForm.state || ''} onChange={e => handleEditChange('state', e.target.value)} required />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Pincode</label>
                        <Input value={editForm.pincode || ''} onChange={e => handleEditChange('pincode', e.target.value)} />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
                        <Input value={editForm.website || ''} onChange={e => handleEditChange('website', e.target.value)} />
                      </div>
                    </div>
                    <div className="mt-4">
                      <h4 className="font-semibold mb-2">Social Media</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Input placeholder="facebook.com/yourpage" value={editForm.facebook || ''} onChange={e => handleEditChange('facebook', e.target.value)} />
                        <Input placeholder="instagram.com/yourpage" value={editForm.instagram || ''} onChange={e => handleEditChange('instagram', e.target.value)} />
                        <Input placeholder="youtube.com/yourchannel" value={editForm.youtube || ''} onChange={e => handleEditChange('youtube', e.target.value)} />
                      </div>
                    </div>
                  </div>
                )}
                {/* Step 3: Professional & Services */}
                {editStep === 3 && (
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Professional & Services</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Experience</label>
                        <Input value={editForm.experience || ''} onChange={e => handleEditChange('experience', e.target.value)} />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Events Completed</label>
                        <Input value={editForm.events || ''} onChange={e => handleEditChange('events', e.target.value)} />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Response Time</label>
                        <Input value={editForm.responseTime || ''} onChange={e => handleEditChange('responseTime', e.target.value)} />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                        <Input value={editForm.priceRange || ''} onChange={e => handleEditChange('priceRange', e.target.value)} />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Availability</label>
                        <Input value={editForm.availability || ''} onChange={e => handleEditChange('availability', e.target.value)} />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <Input value={editForm.status || ''} onChange={e => handleEditChange('status', e.target.value)} />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Rating (0-5)</label>
                        <Input type="number" min={0} max={5} step={0.1} value={editForm.rating || 0} onChange={e => handleEditChange('rating', parseFloat(e.target.value))} />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Review Count</label>
                        <Input type="number" min={0} value={editForm.reviewCount || 0} onChange={e => handleEditChange('reviewCount', parseInt(e.target.value))} />
                      </div>
                      <div className="flex items-center gap-4 mt-6">
                        <Checkbox checked={!!editForm.verified} onCheckedChange={v => handleEditChange('verified', v)} />
                        <span>Verified Vendor</span>
                        <Checkbox checked={!!editForm.featured} onCheckedChange={v => handleEditChange('featured', v)} />
                        <span>Featured</span>
                      </div>
                    </div>
                    <div className="mt-6">
                      <h4 className="font-semibold mb-2">Services & Pricing</h4>
                      {(editForm.services || []).map((service: any, idx: number) => (
                        <div key={idx} className="flex gap-2 items-center mb-2">
                          <Input className="flex-1" placeholder="Service Name" value={service.name} onChange={e => handleServiceChange(idx, 'name', e.target.value)} />
                          <Input className="w-32" placeholder="Price" value={service.price} onChange={e => handleServiceChange(idx, 'price', e.target.value)} />
                          <Input className="flex-1" placeholder="Description" value={service.description || ''} onChange={e => handleServiceChange(idx, 'description', e.target.value)} />
                          <Button type="button" variant="destructive" size="sm" onClick={() => removeService(idx)}>Remove</Button>
                        </div>
                      ))}
                      <Button type="button" variant="outline" size="sm" onClick={addService}>+ Add Service</Button>
                    </div>
                  </div>
                )}
                {/* Step 4: Images & Specializations */}
                {editStep === 4 && (
                  <div className="bg-gray-50 p-6 rounded-lg">
                    <h3 className="text-lg font-semibold mb-4">Images & Specializations</h3>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Upload Vendor Images</label>
                      {/* Placeholder for image upload UI */}
                      <div className="border-2 border-dashed rounded-lg p-6 text-center text-gray-400">Click to upload or drag and drop<br />0/12 images uploaded</div>
                    </div>
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Or Add Image URLs</label>
                      <div className="flex gap-2">
                        <Input className="flex-1" placeholder="https://example.com/portfolio-image.jpg" />
                        <Button type="button" variant="outline" size="sm">Add URL</Button>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Specializations</label>
                        <div className="flex gap-2 mb-2">
                          <Input className="flex-1" placeholder="Add specialization" />
                          <Button type="button" variant="outline" size="sm">Add</Button>
                        </div>
                        {/* List of specializations */}
                        <div className="flex flex-wrap gap-2">
                          {(editForm.specializations || []).map((spec: string, idx: number) => (
                            <span key={idx} className="bg-gray-200 rounded-full px-3 py-1 text-sm">{spec}</span>
                          ))}
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Languages</label>
                        <div className="flex gap-2 mb-2">
                          <Input className="flex-1" placeholder="Add language" />
                          <Button type="button" variant="outline" size="sm">Add</Button>
                        </div>
                        {/* List of languages */}
                        <div className="flex flex-wrap gap-2">
                          {(editForm.languages || []).map((lang: string, idx: number) => (
                            <span key={idx} className="bg-gray-200 rounded-full px-3 py-1 text-sm">{lang}</span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
                {/* Navigation Buttons */}
                <div className="flex gap-3 justify-between pt-4 border-t mt-6">
                  <Button type="button" variant="outline" onClick={() => setShowVendorDetails(false)} disabled={saving}>Cancel</Button>
                  <div className="flex gap-2 ml-auto">
                    {editStep > 1 && (
                      <Button type="button" variant="outline" onClick={() => setEditStep(editStep - 1)} disabled={saving}>Back</Button>
                    )}
                    {editStep < totalEditSteps && (
                      <Button type="button" className="bg-primary hover:bg-primary/90" onClick={() => setEditStep(editStep + 1)} disabled={saving}>Next</Button>
                    )}
                    {editStep === totalEditSteps && (
                      <Button type="submit" className="bg-primary hover:bg-primary/90" disabled={saving}>{saving ? 'Saving...' : 'Save Changes'}</Button>
                    )}
                  </div>
                </div>
              </form>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </AdminOnlyRoute>
  );
}