'use client'
import React from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState } from 'react'
import { AuthenticatedRoute } from '@/components/RouteProtection'
import { UserTypeWelcome } from '@/components/UserTypeIndicator'
import { DebugUserType } from '@/components/DebugUserType'

import AuthRoutingService from '@/lib/services/authRouting'
import {
  Home,
  User,
  Users,
  Briefcase,
  ShoppingBag,
  MapPin,
  Calendar,
  Star,
  Package,
  Image as ImageIcon,
  Settings,
  Menu,
  X,
  ArrowLeft,
  Bell,
  LogOut,
  Search,
  Shield,
  MessageSquare,
  Database,
  MessageCircle,
  FileText,
  BarChart,
  Mail
} from 'lucide-react'

// Dynamic menu based on user type
const getMenuForUserType = (userType: string | null) => {
  if (process.env.NODE_ENV === 'development') {
    console.log('getMenuForUserType called with userType:', userType);
  }

  // First two items are always Dashboard and Profile for all users
  const primaryItems = [
    { href: '/dashboard', label: 'Dashboard', icon: Home },
    { href: '/dashboard/profile', label: 'Profile', icon: User },
  ];

  const customerItems = [
    { href: '/dashboard/bookings', label: 'Bookings', icon: Calendar },
    { href: '/dashboard/orders', label: 'My Orders', icon: ShoppingBag },
    { href: '/dashboard/settings', label: 'Settings', icon: Settings },
  ];

  const vendorItems = [
    { href: '/dashboard/vendors', label: 'My Vendors', icon: Briefcase },
    { href: '/dashboard/shop', label: 'Shop Management', icon: ShoppingBag },
    { href: '/dashboard/venues', label: 'Venue Management', icon: MapPin },
    { href: '/dashboard/vendor-orders', label: 'Order Management', icon: Package },
    { href: '/dashboard/inquiries', label: 'Inquiry Management', icon: MessageCircle },
    { href: '/dashboard/blogs', label: 'Blog Management', icon: FileText },
    { href: '/dashboard/bookings', label: 'Bookings', icon: Calendar },
    { href: '/dashboard/vendor-reviews', label: 'My Reviews', icon: Star },
    { href: '/dashboard/admin-tools', label: 'Business Tools', icon: Database },
    { href: '/dashboard/settings', label: 'Settings', icon: Settings },
  ];

  const adminItems = [
    { href: '/dashboard/admin-users', label: 'User Management', icon: Users },
    { href: '/dashboard/admin-vendors', label: 'Vendor Management', icon: Briefcase },
    { href: '/dashboard/admin-venues', label: 'Venue Management', icon: MapPin },
    { href: '/dashboard/admin-shops', label: 'Shop Management', icon: ShoppingBag },
    { href: '/dashboard/admin-orders', label: 'Order Management', icon: Package },
    { href: '/dashboard/admin-reviews', label: 'Review Management', icon: Star },
    { href: '/dashboard/admin-newsletter', label: 'Newsletter Management', icon: Mail },
    { href: '/dashboard/contacts', label: 'Contact Management', icon: MessageSquare },
    { href: '/dashboard/admin-tools', label: 'Admin Tools', icon: Database },
  ];

  const superAdminItems = [
    { href: '/dashboard/super-admin', label: 'Super Admin Dashboard', icon: Bell },
    { href: '/dashboard/super-admin/users', label: 'User Management', icon: User },
    { href: '/dashboard/super-admin/system', label: 'System Management', icon: Database },
    { href: '/dashboard/super-admin/analytics', label: 'Platform Analytics', icon: BarChart },
  ];

  switch (userType) {
    case 'super_admin':
      if (process.env.NODE_ENV === 'development') console.log('Returning super admin menu');
      return [...primaryItems, ...superAdminItems, ...adminItems];
    case 'admin':
      if (process.env.NODE_ENV === 'development') console.log('Returning admin menu');
      return [...primaryItems, ...adminItems];
    case 'vendor':
      if (process.env.NODE_ENV === 'development') console.log('Returning vendor menu');
      return [...primaryItems, ...vendorItems];
    case 'customer':
      if (process.env.NODE_ENV === 'development') console.log('Returning customer menu');
      return [...primaryItems, ...customerItems];
    default:
      if (process.env.NODE_ENV === 'development') console.log('Returning default (customer) menu for userType:', userType);
      return [...primaryItems, ...customerItems];
  }
};

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const router = useRouter()
  const { isAuthenticated, isLoading, signOut, userType, userProfile } = useAuth()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const handleLogout = async () => {
    try {
      await signOut()
      router.push('/')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  // Force re-render when userProfile changes to ensure menu updates
  useEffect(() => {
    if (userProfile && process.env.NODE_ENV === 'development') {
      console.log('User profile updated, forcing menu refresh:', userProfile)
    }
  }, [userProfile])

  // Optimize menu generation by memoizing it with fallback logic
  const menu = React.useMemo(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Dashboard Layout - User Type:', userType, 'User Profile:', userProfile)
    }

    // Fallback: if userType is null but userProfile indicates vendor, use vendor menu
    let effectiveUserType = userType
    if (!effectiveUserType && userProfile) {
      if (userProfile.isSuperAdmin || userProfile.role === 'SUPER_ADMIN') {
        effectiveUserType = 'super_admin'
      } else if (userProfile.isAdmin || userProfile.role === 'ADMIN') {
        effectiveUserType = 'admin'
      } else if (userProfile.isVendor || userProfile.role === 'VENDOR' ||
                 (userProfile.businessInfo && userProfile.businessInfo.businessName)) {
        effectiveUserType = 'vendor'
      } else {
        effectiveUserType = 'customer'
      }
      if (process.env.NODE_ENV === 'development') {
        console.log('Fallback user type determined:', effectiveUserType)
      }
    }

    return getMenuForUserType(effectiveUserType)
  }, [userType, userProfile])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null // Will redirect to login
  }

  return (
    <AuthenticatedRoute>
      <div className="min-h-screen bg-gray-50">
      {/* Top Navbar */}
      <nav className="bg-white border-b shadow-sm sticky top-0 z-50">
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Left Section */}
            <div className="flex items-center gap-4">
              {/* Mobile Menu Button */}
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                {mobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>

              {/* Logo */}
              <Link href="/" className="flex items-center gap-2 group">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-gradient-to-br from-primary to-yellow-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">T</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-lg font-bold text-gray-900 leading-none">Thirumanam</span>
                    <span className="text-xs font-medium text-primary leading-none">360</span>
                  </div>
                </div>
              </Link>

              {/* Back to Home Button */}
              <Link
                href="/"
                className="hidden sm:flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 hover:text-primary hover:bg-gray-100 rounded-lg transition-all duration-200"
              >
                <ArrowLeft className="w-4 h-4" />
                Back to Home
              </Link>
            </div>

            {/* Center Section - Search */}
            <div className="hidden md:flex flex-1 max-w-md mx-8">
              <div className="relative w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search dashboard..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-colors"
                />
              </div>
            </div>

            {/* Right Section */}
            <div className="flex items-center gap-3">
              {/* Notifications */}
              <button className="relative p-2 text-gray-600 hover:text-primary hover:bg-gray-100 rounded-lg transition-colors">
                <Bell className="w-5 h-5" />
                <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  2
                </span>
              </button>

              {/* Logout Button */}
              <button
                onClick={handleLogout}
                className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200"
              >
                <LogOut className="w-4 h-4" />
                <span className="hidden sm:inline">Logout</span>
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="flex">
        {/* Desktop Sidebar */}
        <aside className="w-64 bg-white border-r flex-shrink-0 hidden md:flex flex-col shadow-sm min-h-[calc(100vh-4rem)]">
          <div className="p-4 border-b">
            <h2 className="font-semibold text-gray-900">Dashboard Menu</h2>
            {userType && (
              <div className="mt-2 p-2 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-2">
                  {userType === 'super_admin' && (
                    <>
                      <Bell className="w-4 h-4 text-purple-600" />
                      <span className="text-xs font-medium text-purple-600">Super Admin</span>
                    </>
                  )}
                  {userType === 'admin' && (
                    <>
                      <Shield className="w-4 h-4 text-orange-600" />
                      <span className="text-xs font-medium text-orange-600">Administrator</span>
                    </>
                  )}
                  {userType === 'vendor' && (
                    <>
                      <Briefcase className="w-4 h-4 text-primary" />
                      <span className="text-xs font-medium text-primary">Business Account</span>
                    </>
                  )}
                  {userType === 'customer' && (
                    <>
                      <User className="w-4 h-4 text-blue-600" />
                      <span className="text-xs font-medium text-blue-600">Customer Account</span>
                    </>
                  )}
                </div>
                {userProfile?.businessInfo?.businessName && (
                  <div className="text-xs text-gray-600 mt-1 truncate">
                    {userProfile.businessInfo.businessName}
                  </div>
                )}
              </div>
            )}
          </div>
          <nav className="flex-1 flex flex-col gap-1 px-3 py-4">
            {menu.map(item => {
              const IconComponent = item.icon;
              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`px-4 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-3 ${
                    pathname === item.href
                      ? 'bg-primary text-white shadow-md transform scale-[1.02]'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-primary hover:shadow-sm'
                  }`}
                >
                  <IconComponent className="w-5 h-5" />
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </nav>
        </aside>

        {/* Mobile Sidebar */}
        {mobileMenuOpen && (
          <div className="md:hidden fixed inset-0 z-40 flex">
            <div className="fixed inset-0 bg-black opacity-50" onClick={() => setMobileMenuOpen(false)} />
            <aside className="relative w-64 bg-white border-r flex-shrink-0 flex flex-col shadow-lg">
              <div className="p-4 border-b">
                <div className="flex items-center justify-between">
                  <h2 className="font-semibold text-gray-900">Dashboard Menu</h2>
                  <button
                    onClick={() => setMobileMenuOpen(false)}
                    className="p-1 hover:bg-gray-100 rounded"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
                {userType && (
                  <div className="mt-2 p-2 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      {userType === 'super_admin' && (
                        <>
                          <Bell className="w-4 h-4 text-purple-600" />
                          <span className="text-xs font-medium text-purple-600">Super Admin</span>
                        </>
                      )}
                      {userType === 'admin' && (
                        <>
                          <Shield className="w-4 h-4 text-orange-600" />
                          <span className="text-xs font-medium text-orange-600">Administrator</span>
                        </>
                      )}
                      {userType === 'vendor' && (
                        <>
                          <Briefcase className="w-4 h-4 text-primary" />
                          <span className="text-xs font-medium text-primary">Business Account</span>
                        </>
                      )}
                      {userType === 'customer' && (
                        <>
                          <User className="w-4 h-4 text-blue-600" />
                          <span className="text-xs font-medium text-blue-600">Customer Account</span>
                        </>
                      )}
                    </div>
                    {userProfile?.businessInfo?.businessName && (
                      <div className="text-xs text-gray-600 mt-1 truncate">
                        {userProfile.businessInfo.businessName}
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Mobile Back to Home */}
              <div className="p-3 border-b">
                <Link
                  href="/"
                  onClick={() => setMobileMenuOpen(false)}
                  className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 hover:text-primary hover:bg-gray-100 rounded-lg transition-all duration-200"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Back to Home
                </Link>
              </div>

              <nav className="flex-1 flex flex-col gap-1 px-3 py-4">
                {menu.map(item => {
                  const IconComponent = item.icon;
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      onClick={() => setMobileMenuOpen(false)}
                      className={`px-4 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-3 ${
                        pathname === item.href
                          ? 'bg-primary text-white shadow-md'
                          : 'text-gray-700 hover:bg-gray-100 hover:text-primary'
                      }`}
                    >
                      <IconComponent className="w-5 h-5" />
                      <span>{item.label}</span>
                    </Link>
                  );
                })}
              </nav>

              {/* Mobile Logout */}
              <div className="p-3 border-t">
                <button
                  onClick={() => {
                    handleLogout();
                    setMobileMenuOpen(false);
                  }}
                  className="w-full flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200"
                >
                  <LogOut className="w-4 h-4" />
                  Logout
                </button>
              </div>
            </aside>
          </div>
        )}

        {/* Main Content */}
        <main className="flex-1 min-w-0 p-4 md:p-8">
          {children}
        </main>
      </div>
    </div>
    </AuthenticatedRoute>
  )
} 