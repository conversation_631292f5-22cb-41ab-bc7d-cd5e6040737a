"use client"

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Users,
  Search,
  Plus,
  Edit,
  Shield,
  Crown,
  Briefcase,
  User,
  Trash2,
  Filter,
  Download,
  Upload,
  MoreHorizontal,
  Eye,
  UserCheck,
  UserX,
  Calendar,
  Mail,
  Phone
} from 'lucide-react';
import UserRoleManager, { QuickRoleButtons } from '@/components/admin/UserRoleManager';
import { AdminOnlyRoute } from '@/components/RouteProtection';
import { UserRole, RoleUpdateResult } from '@/lib/services/userRoleService';
import { profileService } from '@/lib/services/profileService';
import AdminUserService, { UserProfile, UserSearchFilters } from '@/lib/services/adminUserService';
import toast from 'react-hot-toast';

export default function UserManagementPage() {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<UserProfile | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [filters, setFilters] = useState<UserSearchFilters>({});
  const [showFilters, setShowFilters] = useState(false);
  const [showUserDetails, setShowUserDetails] = useState(false);
  const [showRoleManager, setShowRoleManager] = useState(false);
  const [userStats, setUserStats] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [nextToken, setNextToken] = useState<string | undefined>();
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    loadUsers();
    loadUserStats();
  }, []);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      loadUsers();
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchTerm, filters]);

  const loadUsers = async (loadMore: boolean = false) => {
    try {
      setLoading(true);

      const searchFilters: UserSearchFilters = {
        ...filters,
        searchTerm: searchTerm || undefined
      };

      const result = await AdminUserService.getAllUsers(
        searchFilters,
        50,
        loadMore ? nextToken : undefined
      );

      if (result.success && result.data) {
        if (loadMore) {
          setUsers(prev => [...prev, ...result.data!.users]);
        } else {
          setUsers(result.data.users);
        }
        setNextToken(result.data.nextToken);
        setHasMore(!!result.data.nextToken);
      } else {
        toast.error(result.error || 'Failed to load users');
      }
    } catch (error) {
      console.error('Error loading users:', error);
      toast.error('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const loadUserStats = async () => {
    try {
      const result = await AdminUserService.getUserStatistics();
      if (result.success) {
        setUserStats(result.data);
      }
    } catch (error) {
      console.error('Error loading user stats:', error);
    }
  };

  const determineUserRole = (user: UserProfile): UserRole => {
    if (user.isSuperAdmin || user.role === 'SUPER_ADMIN') return 'super_admin';
    if (user.isAdmin || user.role === 'ADMIN') return 'admin';
    if (user.isVendor || user.role === 'VENDOR') return 'vendor';
    return 'customer';
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      return;
    }

    try {
      const result = await AdminUserService.deleteUser(userId);
      if (result.success) {
        toast.success('User deleted successfully');
        loadUsers();
        loadUserStats();
      } else {
        toast.error(result.error || 'Failed to delete user');
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      toast.error('Failed to delete user');
    }
  };

  const handleBulkAction = async (action: 'delete' | 'activate' | 'deactivate') => {
    if (selectedUsers.length === 0) {
      toast.error('Please select users first');
      return;
    }

    if (action === 'delete' && !confirm(`Are you sure you want to delete ${selectedUsers.length} users? This action cannot be undone.`)) {
      return;
    }

    try {
      if (action === 'delete') {
        const deletePromises = selectedUsers.map(userId => AdminUserService.deleteUser(userId));
        await Promise.all(deletePromises);
        toast.success(`${selectedUsers.length} users deleted successfully`);
      } else {
        const updatePromises = selectedUsers.map(userId =>
          AdminUserService.updateUser(userId, {
            isVerified: action === 'activate'
          })
        );
        await Promise.all(updatePromises);
        toast.success(`${selectedUsers.length} users ${action}d successfully`);
      }

      setSelectedUsers([]);
      loadUsers();
      loadUserStats();
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error);
      toast.error(`Failed to ${action} users`);
    }
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === users.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(users.map(user => user.id));
    }
  };

  const handleUserSelect = (userId: string) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const applyFilters = (newFilters: UserSearchFilters) => {
    setFilters(newFilters);
    setCurrentPage(1);
    setNextToken(undefined);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setCurrentPage(1);
    setNextToken(undefined);
  };

  const getRoleBadge = (role: UserRole) => {
    const configs = {
      super_admin: { label: 'Super Admin', icon: Crown, className: 'bg-purple-100 text-purple-800' },
      admin: { label: 'Admin', icon: Shield, className: 'bg-orange-100 text-orange-800' },
      vendor: { label: 'Vendor', icon: Briefcase, className: 'bg-green-100 text-green-800' },
      customer: { label: 'Customer', icon: User, className: 'bg-blue-100 text-blue-800' }
    };

    const config = configs[role];
    const IconComponent = config.icon;

    return (
      <Badge className={config.className}>
        <IconComponent className="w-3 h-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const handleRoleUpdated = (result: RoleUpdateResult) => {
    // Refresh the user list
    loadUsers();
    loadUserStats();
    setShowRoleManager(false);
    setSelectedUser(null);
  };

  const openRoleManager = (user: UserProfile) => {
    setSelectedUser(user);
    setShowRoleManager(true);
  };

  const openUserDetails = (user: UserProfile) => {
    setSelectedUser(user);
    setShowUserDetails(true);
  };

  return (
    <AdminOnlyRoute>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">User Management</h1>
            <p className="text-gray-600">Manage user accounts and roles across the platform</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setShowFilters(!showFilters)}>
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
            <Button onClick={() => setShowRoleManager(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Update User Role
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        {userStats && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Users className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Users</p>
                    <p className="text-2xl font-bold text-gray-900">{userStats.totalUsers}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <User className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Customers</p>
                    <p className="text-2xl font-bold text-gray-900">{userStats.totalCustomers}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Briefcase className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Vendors</p>
                    <p className="text-2xl font-bold text-gray-900">{userStats.totalVendors}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Shield className="h-8 w-8 text-orange-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Admins</p>
                    <p className="text-2xl font-bold text-gray-900">{userStats.totalAdmins}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Calendar className="h-8 w-8 text-indigo-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Recent</p>
                    <p className="text-2xl font-bold text-gray-900">{userStats.recentUsers}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Search and Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              {/* Search Bar */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search users by name, email, or user ID..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Advanced Filters */}
              {showFilters && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                    <Select value={filters.role || ''} onValueChange={(value) => applyFilters({...filters, role: value || undefined})}>
                      <SelectTrigger>
                        <SelectValue placeholder="All Roles" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Roles</SelectItem>
                        <SelectItem value="CUSTOMER">Customer</SelectItem>
                        <SelectItem value="VENDOR">Vendor</SelectItem>
                        <SelectItem value="ADMIN">Admin</SelectItem>
                        <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Admin Status</label>
                    <Select value={filters.isAdmin?.toString() || ''} onValueChange={(value) => applyFilters({...filters, isAdmin: value ? value === 'true' : undefined})}>
                      <SelectTrigger>
                        <SelectValue placeholder="All Users" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Users</SelectItem>
                        <SelectItem value="true">Admins Only</SelectItem>
                        <SelectItem value="false">Non-Admins</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Vendor Status</label>
                    <Select value={filters.isVendor?.toString() || ''} onValueChange={(value) => applyFilters({...filters, isVendor: value ? value === 'true' : undefined})}>
                      <SelectTrigger>
                        <SelectValue placeholder="All Users" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Users</SelectItem>
                        <SelectItem value="true">Vendors Only</SelectItem>
                        <SelectItem value="false">Non-Vendors</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button variant="outline" onClick={clearFilters} className="w-full">
                      Clear Filters
                    </Button>
                  </div>
                </div>
              )}

              {/* Bulk Actions */}
              {selectedUsers.length > 0 && (
                <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                  <span className="text-sm font-medium text-blue-900">
                    {selectedUsers.length} user(s) selected
                  </span>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline" onClick={() => handleBulkAction('activate')}>
                      <UserCheck className="w-4 h-4 mr-1" />
                      Activate
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => handleBulkAction('deactivate')}>
                      <UserX className="w-4 h-4 mr-1" />
                      Deactivate
                    </Button>
                    <Button size="sm" variant="destructive" onClick={() => handleBulkAction('delete')}>
                      <Trash2 className="w-4 h-4 mr-1" />
                      Delete
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* User Details Modal */}
        <Dialog open={showUserDetails} onOpenChange={setShowUserDetails}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>User Details</DialogTitle>
            </DialogHeader>
            {selectedUser && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Name</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.firstName} {selectedUser.lastName}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Email</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.email}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">User ID</label>
                    <p className="mt-1 text-sm text-gray-900 font-mono">{selectedUser.userId}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Role</label>
                    <div className="mt-1">{getRoleBadge(determineUserRole(selectedUser))}</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Account Type</label>
                    <p className="mt-1 text-sm text-gray-900">{selectedUser.accountType || 'Standard'}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Verification Status</label>
                    <Badge variant={selectedUser.isVerified ? "default" : "secondary"} className="mt-1">
                      {selectedUser.isVerified ? "Verified" : "Pending"}
                    </Badge>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Created At</label>
                    <p className="mt-1 text-sm text-gray-900">{new Date(selectedUser.createdAt).toLocaleString()}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Last Login</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {selectedUser.lastLoginAt ? new Date(selectedUser.lastLoginAt).toLocaleString() : 'Never'}
                    </p>
                  </div>
                </div>

                {selectedUser.businessInfo && (
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-3">Business Information</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Business Name</label>
                        <p className="mt-1 text-sm text-gray-900">{selectedUser.businessInfo.businessName}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Business Type</label>
                        <p className="mt-1 text-sm text-gray-900">{selectedUser.businessInfo.businessType}</p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex gap-2">
                  <Button onClick={() => openRoleManager(selectedUser)}>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Role
                  </Button>
                  <Button variant="outline" onClick={() => setShowUserDetails(false)}>
                    Close
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Role Manager Modal */}
        <Dialog open={showRoleManager} onOpenChange={setShowRoleManager}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Update User Role</DialogTitle>
            </DialogHeader>
            <UserRoleManager
              targetUserId={selectedUser?.userId}
              targetUserEmail={selectedUser?.email}
              currentRole={selectedUser ? determineUserRole(selectedUser) : 'customer'}
              onRoleUpdated={handleRoleUpdated}
            />
          </DialogContent>
        </Dialog>

        {/* Users List */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Users ({users.length})
              </CardTitle>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline" size="sm">
                  <Upload className="w-4 h-4 mr-2" />
                  Import
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading users...</p>
              </div>
            ) : users.length === 0 ? (
              <div className="text-center py-8">
                <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No users found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Table Header */}
                <div className="flex items-center p-4 bg-gray-50 rounded-lg font-medium text-sm text-gray-700">
                  <div className="w-8">
                    <Checkbox
                      checked={selectedUsers.length === users.length}
                      onCheckedChange={handleSelectAll}
                    />
                  </div>
                  <div className="flex-1 min-w-0">User Details</div>
                  <div className="w-24 text-center">Role</div>
                  <div className="w-32 text-center">Status</div>
                  <div className="w-32 text-center">Actions</div>
                </div>

                {/* User Rows */}
                {users.map((user) => {
                  const userRole = determineUserRole(user);
                  return (
                    <div
                      key={user.id}
                      className="flex items-center p-4 border rounded-lg hover:bg-gray-50"
                    >
                      <div className="w-8">
                        <Checkbox
                          checked={selectedUsers.includes(user.id)}
                          onCheckedChange={() => handleUserSelect(user.id)}
                        />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3">
                          <div className="flex-1">
                            <h3 className="font-medium text-gray-900 truncate">
                              {user.firstName} {user.lastName}
                            </h3>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Mail className="w-3 h-3" />
                              <span className="truncate">{user.email}</span>
                            </div>
                            {user.phone && (
                              <div className="flex items-center gap-2 text-sm text-gray-600">
                                <Phone className="w-3 h-3" />
                                <span>{user.phone}</span>
                              </div>
                            )}
                            <div className="flex items-center gap-2 text-xs text-gray-500 mt-1">
                              <Calendar className="w-3 h-3" />
                              <span>Joined: {new Date(user.createdAt).toLocaleDateString()}</span>
                              {user.lastLoginAt && (
                                <span>• Last: {new Date(user.lastLoginAt).toLocaleDateString()}</span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="w-24 text-center">
                        {getRoleBadge(userRole)}
                      </div>

                      <div className="w-32 text-center">
                        <Badge variant={user.isVerified ? "default" : "secondary"}>
                          {user.isVerified ? "Verified" : "Pending"}
                        </Badge>
                      </div>

                      <div className="w-32 text-center">
                        <div className="flex items-center justify-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openUserDetails(user)}
                          >
                            <Eye className="w-3 h-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openRoleManager(user)}
                          >
                            <Edit className="w-3 h-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteUser(user.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  );
                })}

                {/* Load More Button */}
                {hasMore && (
                  <div className="text-center pt-4">
                    <Button
                      variant="outline"
                      onClick={() => loadUsers(true)}
                      disabled={loading}
                    >
                      {loading ? 'Loading...' : 'Load More Users'}
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How to Update User Roles</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Quick Role Updates</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Use the colored buttons for quick role changes</li>
                  <li>• Current role is highlighted</li>
                  <li>• Changes take effect immediately</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Detailed Role Management</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Click "Edit" for detailed role management</li>
                  <li>• Add reasons for role changes</li>
                  <li>• Confirmation required for admin roles</li>
                </ul>
              </div>
            </div>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">Important Notes</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• Only Super Admins can create other Super Admins</li>
                <li>• All role changes are logged for security auditing</li>
                <li>• Users will see their new permissions immediately</li>
                <li>• Be careful when assigning administrative roles</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminOnlyRoute>
  );
}
