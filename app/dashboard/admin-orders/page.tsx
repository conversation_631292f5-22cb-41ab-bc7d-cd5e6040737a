"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog"
import { Package, Search, Calendar, CreditCard, Truck, Eye, Edit, Trash2, RefreshCw, Filter, MapPin, Phone, Mail, AlertCircle, DollarSign, Users, TrendingUp } from "lucide-react"
import { OrderService, OrderData } from "@/lib/services/orderService"
import { useAuth } from "@/contexts/AuthContext"
import { showToast } from "@/lib/toast"

export default function AdminOrdersPage() {
  const { isAuthenticated, user, isAdmin, userType } = useAuth()
  const router = useRouter()
  const [orders, setOrders] = useState<OrderData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [paymentFilter, setPaymentFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')
  const [filteredOrders, setFilteredOrders] = useState<OrderData[]>([])
  const [selectedOrder, setSelectedOrder] = useState<OrderData | null>(null)
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [updateData, setUpdateData] = useState({
    status: '',
    paymentStatus: '',
    trackingNumber: '',
    courierPartner: '',
    notes: ''
  })

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/login?redirect=/dashboard/admin-orders')
      return
    }

    // Check if user is admin
    if (!isAdmin) {
      router.push('/dashboard')
      showToast.error('Access denied. Admin privileges required.')
      return
    }

    loadAllOrders()
  }, [isAuthenticated, user])

  useEffect(() => {
    filterOrders()
  }, [searchTerm, statusFilter, paymentFilter, dateFilter, orders])

  const loadAllOrders = async () => {
    try {
      setLoading(true)
      // This would be a new method to get all orders for admin
      const result = await OrderService.getAllOrders(500) // Admin can see more orders

      if (result.success) {
        setOrders(result.orders || [])
      } else {
        showToast.error('Failed to load orders')
        setOrders([]) // Set empty array as fallback
      }
    } catch (error) {
      console.error('Error loading admin orders:', error)
      showToast.error('Failed to load orders')
      setOrders([]) // Set empty array as fallback
    } finally {
      setLoading(false)
    }
  }

  const filterOrders = () => {
    let filtered = orders

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(order =>
        order.orderNumber?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        order.customerEmail?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (order.items && order.items.some(item => item.productName?.toLowerCase().includes(searchTerm.toLowerCase())))
      )
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter)
    }

    // Payment filter
    if (paymentFilter !== 'all') {
      filtered = filtered.filter(order => order.paymentStatus === paymentFilter)
    }

    // Date filter
    if (dateFilter !== 'all') {
      const now = new Date()
      const filterDate = new Date()
      
      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0)
          break
        case 'week':
          filterDate.setDate(now.getDate() - 7)
          break
        case 'month':
          filterDate.setMonth(now.getMonth() - 1)
          break
        case 'quarter':
          filterDate.setMonth(now.getMonth() - 3)
          break
      }
      
      filtered = filtered.filter(order => new Date(order.orderDate) >= filterDate)
    }

    setFilteredOrders(filtered)
  }

  const handleUpdateOrder = async () => {
    if (!selectedOrder) return

    try {
      let result
      try {
        // Try the main update method first
        result = await OrderService.updateOrderStatus(
          selectedOrder.id,
          updateData.status as any,
          {
            paymentStatus: updateData.paymentStatus as any,
            trackingNumber: updateData.trackingNumber,
            courierPartner: updateData.courierPartner,
            adminNotes: updateData.notes
          }
        )
      } catch (updateError) {
        console.log('Main update failed, using simple update:', updateError.message)
        // Fallback to simple update method
        result = await OrderService.updateOrderStatusSimple(
          selectedOrder.id,
          updateData.status as any,
          {
            paymentStatus: updateData.paymentStatus as any,
            trackingNumber: updateData.trackingNumber,
            courierPartner: updateData.courierPartner,
            adminNotes: updateData.notes
          }
        )
      }

      if (result.success) {
        showToast.success(result.message || 'Order updated successfully')
        setIsUpdateDialogOpen(false)
        loadAllOrders()
      } else {
        showToast.error(result.message || 'Failed to update order')
      }
    } catch (error) {
      console.error('Error updating order:', error)
      showToast.error('Failed to update order')
    }
  }

  const handleDeleteOrder = async () => {
    if (!selectedOrder) return

    try {
      const result = await OrderService.deleteOrder(selectedOrder.id)

      if (result.success) {
        showToast.success('Order deleted successfully')
        setIsDeleteDialogOpen(false)
        loadAllOrders()
      } else {
        showToast.error('Failed to delete order')
      }
    } catch (error) {
      console.error('Error deleting order:', error)
      showToast.error('Failed to delete order')
    }
  }

  const openUpdateDialog = (order: OrderData) => {
    setSelectedOrder(order)
    setUpdateData({
      status: order.status,
      paymentStatus: order.paymentStatus,
      trackingNumber: order.trackingNumber || '',
      courierPartner: order.courierPartner || '',
      notes: ''
    })
    setIsUpdateDialogOpen(true)
  }

  const openDeleteDialog = (order: OrderData) => {
    setSelectedOrder(order)
    setIsDeleteDialogOpen(true)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'CONFIRMED': return 'bg-green-100 text-green-800'
      case 'PROCESSING': return 'bg-blue-100 text-blue-800'
      case 'SHIPPED': return 'bg-purple-100 text-purple-800'
      case 'OUT_FOR_DELIVERY': return 'bg-indigo-100 text-indigo-800'
      case 'DELIVERED': return 'bg-green-100 text-green-800'
      case 'CANCELLED': return 'bg-red-100 text-red-800'
      case 'RETURNED': return 'bg-orange-100 text-orange-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'PAID': return 'bg-green-100 text-green-800'
      case 'COD_PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'COD_COLLECTED': return 'bg-green-100 text-green-800'
      case 'FAILED': return 'bg-red-100 text-red-800'
      case 'PENDING': return 'bg-yellow-100 text-yellow-800'
      case 'REFUNDED': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Calculate stats
  const totalRevenue = filteredOrders.reduce((sum, order) => sum + order.total, 0)
  const totalOrders = filteredOrders.length
  const pendingOrders = filteredOrders.filter(o => o.status === 'PENDING').length
  const completedOrders = filteredOrders.filter(o => o.status === 'DELIVERED').length

  const getActiveOrders = () => filteredOrders.filter(order => 
    ['PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'OUT_FOR_DELIVERY'].includes(order.status)
  )

  const getCompletedOrders = () => filteredOrders.filter(order => 
    ['DELIVERED', 'CANCELLED', 'RETURNED'].includes(order.status)
  )

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F6C244]"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Admin Orders</h1>
          <p className="text-gray-600 mt-1">Manage all platform orders</p>
        </div>
        <Button onClick={loadAllOrders} variant="outline" className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">₹{totalRevenue.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Orders</p>
                <p className="text-2xl font-bold text-gray-900">{totalOrders}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <AlertCircle className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Orders</p>
                <p className="text-2xl font-bold text-gray-900">{pendingOrders}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold text-gray-900">{completedOrders}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search orders..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="CONFIRMED">Confirmed</SelectItem>
                <SelectItem value="PROCESSING">Processing</SelectItem>
                <SelectItem value="SHIPPED">Shipped</SelectItem>
                <SelectItem value="OUT_FOR_DELIVERY">Out for Delivery</SelectItem>
                <SelectItem value="DELIVERED">Delivered</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
                <SelectItem value="RETURNED">Returned</SelectItem>
              </SelectContent>
            </Select>

            <Select value={paymentFilter} onValueChange={setPaymentFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Payment status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Payments</SelectItem>
                <SelectItem value="PAID">Paid</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="COD_PENDING">COD Pending</SelectItem>
                <SelectItem value="COD_COLLECTED">COD Collected</SelectItem>
                <SelectItem value="FAILED">Failed</SelectItem>
                <SelectItem value="REFUNDED">Refunded</SelectItem>
              </SelectContent>
            </Select>

            <Select value={dateFilter} onValueChange={setDateFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by date" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="week">Last Week</SelectItem>
                <SelectItem value="month">Last Month</SelectItem>
                <SelectItem value="quarter">Last 3 Months</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Filter className="h-4 w-4" />
              <span>{filteredOrders.length} orders found</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Orders Tabs */}
      <Tabs defaultValue="active" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="active" className="flex items-center gap-2">
            <Package className="h-4 w-4" />
            Active Orders ({getActiveOrders().length})
          </TabsTrigger>
          <TabsTrigger value="completed" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Completed Orders ({getCompletedOrders().length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4">
          {getActiveOrders().length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No active orders</h3>
                <p className="text-gray-600">No active orders found matching your filters.</p>
              </CardContent>
            </Card>
          ) : (
            getActiveOrders().map((order) => (
              <AdminOrderCard
                key={order.id}
                order={order}
                getStatusColor={getStatusColor}
                getPaymentStatusColor={getPaymentStatusColor}
                onUpdate={openUpdateDialog}
                onDelete={openDeleteDialog}
              />
            ))
          )}
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          {getCompletedOrders().length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No completed orders</h3>
                <p className="text-gray-600">No completed orders found matching your filters.</p>
              </CardContent>
            </Card>
          ) : (
            getCompletedOrders().map((order) => (
              <AdminOrderCard
                key={order.id}
                order={order}
                getStatusColor={getStatusColor}
                getPaymentStatusColor={getPaymentStatusColor}
                onUpdate={openUpdateDialog}
                onDelete={openDeleteDialog}
              />
            ))
          )}
        </TabsContent>
      </Tabs>

      {/* Update Order Dialog */}
      <Dialog open={isUpdateDialogOpen} onOpenChange={setIsUpdateDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Update Order</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="status">Order Status</Label>
              <Select value={updateData.status} onValueChange={(value) => setUpdateData(prev => ({ ...prev, status: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="CONFIRMED">Confirmed</SelectItem>
                  <SelectItem value="PROCESSING">Processing</SelectItem>
                  <SelectItem value="SHIPPED">Shipped</SelectItem>
                  <SelectItem value="OUT_FOR_DELIVERY">Out for Delivery</SelectItem>
                  <SelectItem value="DELIVERED">Delivered</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  <SelectItem value="RETURNED">Returned</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="paymentStatus">Payment Status</Label>
              <Select value={updateData.paymentStatus} onValueChange={(value) => setUpdateData(prev => ({ ...prev, paymentStatus: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select payment status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="PAID">Paid</SelectItem>
                  <SelectItem value="COD_PENDING">COD Pending</SelectItem>
                  <SelectItem value="COD_COLLECTED">COD Collected</SelectItem>
                  <SelectItem value="FAILED">Failed</SelectItem>
                  <SelectItem value="REFUNDED">Refunded</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="trackingNumber">Tracking Number</Label>
              <Input
                id="trackingNumber"
                value={updateData.trackingNumber}
                onChange={(e) => setUpdateData(prev => ({ ...prev, trackingNumber: e.target.value }))}
                placeholder="Enter tracking number"
              />
            </div>

            <div>
              <Label htmlFor="courierPartner">Courier Partner</Label>
              <Input
                id="courierPartner"
                value={updateData.courierPartner}
                onChange={(e) => setUpdateData(prev => ({ ...prev, courierPartner: e.target.value }))}
                placeholder="e.g., BlueDart, FedEx"
              />
            </div>

            <div>
              <Label htmlFor="notes">Admin Notes</Label>
              <Textarea
                id="notes"
                value={updateData.notes}
                onChange={(e) => setUpdateData(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Add admin notes"
                rows={3}
              />
            </div>

            <div className="flex gap-3">
              <Button onClick={handleUpdateOrder} className="flex-1">
                Update Order
              </Button>
              <Button variant="outline" onClick={() => setIsUpdateDialogOpen(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Order Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Order</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete order #{selectedOrder?.orderNumber}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteOrder} className="bg-red-600 hover:bg-red-700">
              Delete Order
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

// Admin Order Card Component
function AdminOrderCard({ order, getStatusColor, getPaymentStatusColor, onUpdate, onDelete }: {
  order: OrderData,
  getStatusColor: (status: string) => string,
  getPaymentStatusColor: (status: string) => string,
  onUpdate: (order: OrderData) => void,
  onDelete: (order: OrderData) => void
}) {
  const router = useRouter()

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">Order #{order.orderNumber}</CardTitle>
            <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
              <span className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                {new Date(order.orderDate).toLocaleDateString()}
              </span>
              <span className="flex items-center gap-1">
                <CreditCard className="h-4 w-4" />
                {order.paymentMethod}
              </span>
              <span className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                {order.customerName || 'N/A'}
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold">₹{order.total.toLocaleString()}</div>
            <div className="flex gap-2 mt-1">
              <Badge className={getStatusColor(order.status)}>{order.status}</Badge>
              <Badge className={getPaymentStatusColor(order.paymentStatus)}>
                {order.paymentStatus.replace('_', ' ')}
              </Badge>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Customer Information */}
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <h4 className="font-semibold text-sm mb-2">Customer Information</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-gray-400" />
              <span>{order.customerEmail || 'N/A'}</span>
            </div>
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-gray-400" />
              <span>{order.customerPhone || 'N/A'}</span>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-gray-400" />
              <span>
                {order.shippingAddress?.city || 'N/A'}, {order.shippingAddress?.state || 'N/A'}
              </span>
            </div>
          </div>
        </div>

        {/* Order Items Summary */}
        <div className="space-y-2 mb-4">
          <h4 className="font-semibold text-sm">Order Items ({order.items?.length || 0})</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {order.items && order.items.length > 0 ? (
              <>
                {order.items.slice(0, 4).map((item, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <span className="font-medium">{item.quantity}x</span>
                    <span className="truncate">{item.productName}</span>
                    <span className="text-gray-500">₹{item.subtotal?.toLocaleString() || '0'}</span>
                  </div>
                ))}
                {order.items.length > 4 && (
                  <div className="text-sm text-gray-500">+{order.items.length - 4} more items</div>
                )}
              </>
            ) : (
              <div className="text-sm text-gray-500">No items found</div>
            )}
          </div>
        </div>

        {/* Order Summary */}
        <div className="bg-blue-50 rounded-lg p-4 mb-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Subtotal:</span>
              <div className="font-medium">₹{order.subtotal?.toLocaleString() || '0'}</div>
            </div>
            <div>
              <span className="text-gray-600">Shipping:</span>
              <div className="font-medium">₹{order.shippingCost?.toLocaleString() || '0'}</div>
            </div>
            <div>
              <span className="text-gray-600">Tax:</span>
              <div className="font-medium">₹{order.tax?.toLocaleString() || '0'}</div>
            </div>
            <div>
              <span className="text-gray-600">Total:</span>
              <div className="font-bold text-lg">₹{order.total?.toLocaleString() || '0'}</div>
            </div>
          </div>
        </div>

        {/* Tracking Information */}
        {(order.trackingNumber || order.courierPartner) && (
          <div className="bg-purple-50 rounded-lg p-4 mb-4">
            <h4 className="font-semibold text-sm mb-2">Tracking Information</h4>
            <div className="text-sm">
              {order.trackingNumber && (
                <p><span className="font-medium">Tracking Number:</span> {order.trackingNumber}</p>
              )}
              {order.courierPartner && (
                <p><span className="font-medium">Courier Partner:</span> {order.courierPartner}</p>
              )}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(`/order-confirmation?orderId=${order.id}`)}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            View Details
          </Button>

          <Button
            size="sm"
            onClick={() => onUpdate(order)}
            className="flex items-center gap-2 bg-[#F6C244] hover:bg-[#F6C244]/90 text-black"
          >
            <Edit className="h-4 w-4" />
            Edit Order
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onDelete(order)}
            className="flex items-center gap-2 text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
          >
            <Trash2 className="h-4 w-4" />
            Delete
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
