"use client"
import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Eye, EyeOff, Mail, Lock, User, AlertCircle, Phone, MessageSquare, RefreshCw, CheckCircle, Clock, Building2 } from "lucide-react"
import Link from "next/link"
import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { useRouter } from "next/navigation"
import AuthRoutingService from "@/lib/services/authRouting"
import HybridMobileAuthService from "@/lib/services/hybridMobileAuth"
import MobileAuthService from "@/lib/services/mobileAuthService"
import { RedirectUtils } from "@/lib/utils/redirectUtils"
import { profileService } from '@/lib/services/profileService'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"


export default function LoginPage() {
  const [isSignUp, setIsSignUp] = useState(false)
  const [isConfirmation, setIsConfirmation] = useState(false)
  const [isForgotPassword, setIsForgotPassword] = useState(false)
  const [isResetCode, setIsResetCode] = useState(false)
  const [isPhone, setIsPhone] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    emailOrPhone: '',
    password: '',
    confirmPassword: '',
    confirmationCode: '',
    resetCode: '',
    newPassword: '',
    otp: '',
    agreeToTerms: false
  })

  // Authentication states
  const [authMode, setAuthMode] = useState<'login' | 'customer-signup'>('login')
  const [authMethod, setAuthMethod] = useState<'otp' | 'password'>('password')
  const [loginMethod, setLoginMethod] = useState<'phone' | 'email'>('email')
  const [currentStep, setCurrentStep] = useState<'input' | 'otp'>('input')
  const [successMessage, setSuccessMessage] = useState('')

  // Mobile OTP states
  const [signupMethod, setSignupMethod] = useState<'email' | 'mobile'>('email')
  const [isOTPStep, setIsOTPStep] = useState(false)
  const [otpSession, setOtpSession] = useState('')
  const [countryCode, setCountryCode] = useState('+91')
  const [resendTimer, setResendTimer] = useState(0)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)

  const { signInWithPassword, signUp, confirmSignUp, resendConfirmationCode, requestPasswordReset, confirmPasswordReset, sendOTP, verifyOTP, refreshUserProfile, userType, userProfile } = useAuth()
  const router = useRouter()

  // Store redirect URL from query parameters on component mount
  useEffect(() => {
    RedirectUtils.storeRedirectFromQuery();
  }, []);

  // Helper function to handle post-login redirect
  const handlePostLoginRedirect = () => {
    const redirectUrl = RedirectUtils.getAndClearRedirectUrl();
    if (redirectUrl) {
      router.push(redirectUrl);
    } else {
      const dashboardRoute = AuthRoutingService.getDashboardRoute(userType, userProfile);
      router.push(dashboardRoute);
    }
  };

  // Country codes for mobile OTP
  const countryCodes = HybridMobileAuthService.getSupportedCountryCodes()

  // Start resend timer
  const startResendTimer = () => {
    setResendTimer(30)
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer)
          return 0
        }
        return prev - 1
      })
    }, 1000)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }



  const validateInput = (input: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

    if (emailRegex.test(input)) {
      setIsPhone(false)
      return true
    } else if (MobileAuthService.validatePhoneNumber(input)) {
      setIsPhone(true)
      return true
    }
    return false
  }

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!validateInput(formData.emailOrPhone)) {
      setError('Please enter a valid email address or phone number')
      setLoading(false)
      return
    }

    try {
      await signInWithPassword(formData.emailOrPhone, formData.password)

      // Wait a moment for user profile to be fetched
      setTimeout(() => {
        handlePostLoginRedirect()
      }, 1000)
    } catch (error: any) {
      setError(error.message || 'Failed to sign in')
    } finally {
      setLoading(false)
    }
  }

  // Handle Customer Signup with proper password
  const handleCustomerSignup = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    // Validate form
    if (!formData.fullName.trim()) {
      setError('Please enter your full name')
      setLoading(false)
      return
    }

    if (!formData.email.trim() || !/\S+@\S+\.\S+/.test(formData.email)) {
      setError('Please enter a valid email address')
      setLoading(false)
      return
    }

    if (!formData.phone.trim() || !/^\d{10}$/.test(formData.phone)) {
      setError('Please enter a valid 10-digit phone number')
      setLoading(false)
      return
    }

    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long')
      setLoading(false)
      return
    }

    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match')
      setLoading(false)
      return
    }

    if (!formData.agreeToTerms) {
      setError('Please agree to the Terms of Service and Privacy Policy')
      setLoading(false)
      return
    }

    try {
      // Store additional customer data for automatic profile creation
      const nameParts = formData.fullName.trim().split(' ')
      const customerData = {
        firstName: nameParts[0] || '',
        lastName: nameParts.slice(1).join(' ') || '',
        email: formData.email,
        phone: formData.phone,
        isVendor: false
      }
      localStorage.setItem('pendingUserData', JSON.stringify(customerData))

      // Create customer account with proper password
      await signUp(formData.email, formData.fullName, formData.password, false)
      setIsConfirmation(true)
      setIsOTPStep(false) // We'll use the confirmation form instead
      setSuccessMessage(`Verification code sent to ${formData.email}`)
      startResendTimer()
    } catch (error: any) {
      setError(error.message || 'Sign up failed')
    } finally {
      setLoading(false)
    }
  }

  // Handle OTP Login (for quick login without password)
  const handleOTPLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (loginMethod === 'phone') {
      if (!formData.phone.trim() || !/^\d{10}$/.test(formData.phone)) {
        setError('Please enter a valid 10-digit phone number')
        setLoading(false)
        return
      }

      try {
        // Send OTP to phone for login
        const result = await sendOTP(formData.phone, countryCode)
        if (result.success) {
          setOtpSession(result.session || '')
          setIsOTPStep(true)
          setSuccessMessage(`OTP sent to ${countryCode}${formData.phone}`)
          startResendTimer()
        } else {
          setError(result.message)
        }
      } catch (error: any) {
        setError(error.message || 'Failed to send OTP')
      }
    } else {
      if (!formData.email.trim() || !/\S+@\S+\.\S+/.test(formData.email)) {
        setError('Please enter a valid email address')
        setLoading(false)
        return
      }

      try {
        // For email OTP login, we'll use password reset flow to send OTP
        await requestPasswordReset(formData.email)
        setIsOTPStep(true)
        setSuccessMessage(`Login code sent to ${formData.email}`)
        startResendTimer()
      } catch (error: any) {
        setError(error.message || 'Failed to send login code')
      }
    }

    setLoading(false)
  }

  // Handle OTP verification for both signup and login
  const handleOTPVerification = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!formData.otp || formData.otp.length !== 6) {
      setError('Please enter the 6-digit OTP')
      setLoading(false)
      return
    }

    try {
      if (isConfirmation) {
        // Customer signup OTP verification
        await confirmSignUp(formData.email, formData.otp)

        // Auto sign in after verification
        try {
          await signInWithPassword(formData.email, formData.password)

          // Create customer profile after successful authentication (same as vendor-login)
          try {
            // Get customer data from localStorage
            const pendingUserData = localStorage.getItem('pendingUserData')
            if (pendingUserData) {
              const customerData = JSON.parse(pendingUserData)

              // Create user profile with customer information
              await profileService.createProfile({
                firstName: customerData.firstName,
                lastName: customerData.lastName,
                email: customerData.email,
                phone: customerData.phone,
                isVendor: false
              })

              // Force refresh the auth context to pick up the new customer profile
              await refreshUserProfile()

              // Clear the temporary data
              localStorage.removeItem('pendingUserData')
              console.log('✅ Customer profile created successfully!')
            }
          } catch (profileError) {
            console.error('Error creating customer profile:', profileError)
            // Don't block the user from proceeding even if profile creation fails
          }

          // Redirect after sign-in
          setTimeout(() => {
            handlePostLoginRedirect()
          }, 1000)
        } catch (signInError) {
          console.error('Auto sign-in failed:', signInError)
          setSuccessMessage('Account created successfully! Please login with your email and password.')
          setIsSignUp(false)
          setIsConfirmation(false)
          setIsOTPStep(false)
        }
      } else {
        // Handle other OTP verification scenarios (mobile login, etc.)
        let result
        if (signupMethod === 'mobile') {
          result = await HybridMobileAuthService.verifyMobileOTP(otpSession, formData.otp)
        } else {
          result = await MobileAuthService.verifyEmailOTP(formData.emailOrPhone, formData.otp)
        }

        if (result.success) {
          setTimeout(() => {
            handlePostLoginRedirect()
          }, 1000)
        } else {
          setError(result.message)
        }
      }
    } catch (error: any) {
      setError(error.message || 'OTP verification failed')
    } finally {
      setLoading(false)
    }
  }

  // Handle resend OTP
  const handleResendOTP = async () => {
    if (resendTimer > 0) return

    setLoading(true)
    setError('')

    try {
      if (isConfirmation) {
        // Resend confirmation code for signup
        await resendConfirmationCode(formData.email)
        setSuccessMessage(`New verification code sent to ${formData.email}`)
        startResendTimer()
      } else {
        // Resend OTP for mobile login
        let result
        if (signupMethod === 'mobile') {
          result = await HybridMobileAuthService.resendOTP(otpSession)
        } else {
          result = await MobileAuthService.sendEmailOTP(formData.emailOrPhone)
        }

        if (result.success) {
          startResendTimer()
        } else {
          setError(result.message)
        }
      }
    } catch (error: any) {
      setError('Failed to resend code. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleForgotPassword = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (!validateInput(formData.emailOrPhone)) {
      setError('Please enter a valid email address or phone number')
      setLoading(false)
      return
    }

    try {
      await requestPasswordReset(formData.emailOrPhone)
      setIsResetCode(true)
    } catch (error: any) {
      setError(error.message || 'Failed to send reset code')
    } finally {
      setLoading(false)
    }
  }

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    if (formData.newPassword.length < 8) {
      setError('Password must be at least 8 characters long')
      setLoading(false)
      return
    }

    try {
      await confirmPasswordReset(formData.emailOrPhone, formData.resetCode, formData.newPassword)
      setIsForgotPassword(false)
      setIsResetCode(false)
      setError('')
      // Auto sign in with new password
      await signInWithPassword(formData.emailOrPhone, formData.newPassword)
      setTimeout(() => {
        handlePostLoginRedirect()
      }, 1000)
    } catch (error: any) {
      setError(error.message || 'Failed to reset password')
    } finally {
      setLoading(false)
    }
  }

  const handleConfirmSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      await confirmSignUp(formData.emailOrPhone, formData.confirmationCode)
      // After confirming sign up, automatically sign in
      await signInWithPassword(formData.emailOrPhone, formData.password)
      setTimeout(() => {
        handlePostLoginRedirect()
      }, 1000)
    } catch (error: any) {
      setError(error.message || 'Failed to confirm sign up')
    } finally {
      setLoading(false)
    }
  }

  const handleResendCode = async () => {
    setLoading(true)
    setError('')

    try {
      if (isConfirmation) {
        await resendConfirmationCode(formData.emailOrPhone)
        setError('') // Clear any previous errors
      } else if (isResetCode) {
        await requestPasswordReset(formData.emailOrPhone)
        setError('') // Clear any previous errors
      }
    } catch (error: any) {
      setError(error.message || 'Failed to resend code')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-white flex flex-col">
      <TopHeader />
      <Header />
      <section className="flex flex-1 items-center justify-center bg-gradient-to-br from-primary/5 to-secondary/5" style={{paddingTop: 80, paddingBottom: 40}}>
        <div className="w-full max-w-6xl mx-auto bg-white rounded-xl shadow-lg overflow-hidden flex flex-col lg:flex-row">
          {/* Left: Wedding Planning Benefits */}
          <div className="lg:w-1/2 w-full bg-gradient-to-br from-primary to-pink-600 p-8 lg:p-12 text-white">
            <div className="h-full flex flex-col justify-center">
              <div className="mb-8">
                <h1 className="text-3xl lg:text-4xl font-bold mb-4">
                  Plan Your Perfect Wedding
                </h1>
                <p className="text-lg opacity-90 mb-6">
                  Join thousands of couples who trust Thirumanam 360 to plan their dream wedding with the best vendors and venues.
                </p>
              </div>

              <div className="space-y-4 mb-8">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <span>Discover verified wedding vendors</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <span>Compare prices and packages</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <span>Save your favorite vendors</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold">✓</span>
                  </div>
                  <span>Get personalized recommendations</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold">50,000+</div>
                  <div className="text-sm opacity-80">Happy Couples</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">5,000+</div>
                  <div className="text-sm opacity-80">Trusted Vendors</div>
                </div>
              </div>
            </div>
          </div>
          {/* Right: Login Form */}
          <div className="lg:w-1/2 w-full flex flex-col justify-center p-8 lg:p-12">
            {/* Business Registration Quick Access */}
            {!isConfirmation && !isResetCode && !isForgotPassword && !isOTPStep && !isSignUp && (
              <div className="mb-4">
                <div className="bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Building2 className="w-4 h-4 text-primary" />
                      <span className="text-primary font-medium text-sm">Are you a vendor?</span>
                    </div>
                    <Link href="/vendor-signup">
                      <Button
                        size="sm"
                        className="bg-primary text-white hover:bg-primary/90 text-xs px-3 py-1 h-7"
                      >
                        Register Now
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            )}

            {/* Only show titles for specific flows that need explanation */}
            {(isConfirmation || isResetCode || isForgotPassword || isOTPStep || isSignUp) && (
              <div className="text-center mb-6">
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                  {isConfirmation ? 'Verify Your Email' :
                   isResetCode ? 'Create New Password' :
                   isForgotPassword ? 'Reset Your Password' :
                   isOTPStep ? 'Enter Verification Code' :
                   'Create Account'}
                </h1>

                {/* Verification/Reset Descriptions */}
                {isConfirmation && (
                  <p className="text-gray-600 text-sm">
                    We've sent a verification code to your email address. Please check your inbox and enter the code below.
                  </p>
                )}

                {isOTPStep && (
                  <p className="text-gray-600 text-sm">
                    Enter the 6-digit verification code we sent to your email to continue.
                  </p>
                )}

                {isForgotPassword && !isResetCode && (
                  <p className="text-gray-600 text-sm">
                    No worries! Enter your email address and we'll send you a reset code.
                  </p>
                )}

                {isResetCode && (
                  <p className="text-gray-600 text-sm">
                    Enter the reset code from your email and create a new secure password.
                  </p>
                )}

                {isSignUp && (
                  <p className="text-gray-600 text-sm">
                    Join our wedding planning community
                  </p>
                )}
              </div>
            )}

            {error && (
              <div className="mb-6 p-4 bg-gradient-to-r from-red-50 to-red-50/50 border border-red-200 rounded-lg flex items-start gap-3 text-red-700 shadow-sm">
                <div className="flex-shrink-0 mt-0.5">
                  <AlertCircle className="w-5 h-5" />
                </div>
                <div>
                  <p className="text-sm font-medium">Oops! Something went wrong</p>
                  <p className="text-sm text-red-600 mt-1">{error}</p>
                </div>
              </div>
            )}

            {successMessage && (
              <div className="mb-6 p-4 bg-gradient-to-r from-green-50 to-green-50/50 border border-green-200 rounded-lg flex items-start gap-3 text-green-700 shadow-sm">
                <div className="flex-shrink-0 mt-0.5">
                  <CheckCircle className="w-5 h-5" />
                </div>
                <div>
                  <p className="text-sm font-medium">Success!</p>
                  <p className="text-sm text-green-600 mt-1">{successMessage}</p>
                </div>
              </div>
            )}

            {isConfirmation ? (
              <form onSubmit={handleOTPVerification} className="space-y-4">
                <div className="text-sm text-gray-600 mb-4">
                  We've sent a verification code to {formData.email}. Please enter it below.
                </div>
                <div className="relative">
                  <Input
                    name="otp"
                    type="text"
                    placeholder="Enter 6-digit verification code"
                    className="pl-12 text-center text-lg tracking-widest"
                    value={formData.otp}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '').slice(0, 6)
                      setFormData(prev => ({ ...prev, otp: value }))
                      setError('')
                    }}
                    maxLength={6}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <MessageSquare className="h-5 w-5" />
                  </span>
                </div>
                <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                  {loading ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Verify Account
                    </>
                  )}
                </Button>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={() => {
                      setIsConfirmation(false)
                      setFormData(prev => ({ ...prev, otp: '' }))
                    }}
                  >
                    Back to Sign Up
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={handleResendOTP}
                    disabled={loading || resendTimer > 0}
                  >
                    {resendTimer > 0 ? (
                      <>
                        <Clock className="w-4 h-4 mr-2" />
                        Resend in {resendTimer}s
                      </>
                    ) : (
                      'Resend Code'
                    )}
                  </Button>
                </div>
              </form>
            ) : isResetCode ? (
              <form onSubmit={handleResetPassword} className="space-y-4">
                <div className="text-sm text-gray-600 mb-4">
                  We've sent a reset code to {formData.emailOrPhone}. Enter the code and your new password.
                </div>
                <div className="relative">
                  <Input
                    name="resetCode"
                    type="text"
                    placeholder="Enter reset code"
                    className="pl-12"
                    value={formData.resetCode}
                    onChange={handleInputChange}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <Mail className="h-5 w-5" />
                  </span>
                </div>
                <div className="relative">
                  <Input
                    name="newPassword"
                    type={showPassword ? "text" : "password"}
                    placeholder="New Password*"
                    className="pl-12 pr-12"
                    value={formData.newPassword}
                    onChange={handleInputChange}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <Lock className="h-5 w-5" />
                  </span>
                  <button
                    type="button"
                    className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>
                <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                  {loading ? 'Resetting...' : 'Reset Password'}
                </Button>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={() => { setIsResetCode(false); setIsForgotPassword(false); }}
                  >
                    Back to Login
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={handleResendCode}
                    disabled={loading}
                  >
                    Resend Code
                  </Button>
                </div>
              </form>
            ) : isForgotPassword ? (
              <form onSubmit={handleForgotPassword} className="space-y-4">
                <div className="text-sm text-gray-600 mb-4">
                  Enter your email or phone number to receive a password reset code.
                </div>
                <div className="relative">
                  <Input
                    name="emailOrPhone"
                    type="text"
                    placeholder="Enter email or phone number (e.g., 9876543210)*"
                    className="pl-12"
                    value={formData.emailOrPhone}
                    onChange={handleInputChange}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <Mail className="h-5 w-5" />
                  </span>
                </div>
                <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                  {loading ? 'Sending...' : 'Send Reset Code'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => setIsForgotPassword(false)}
                >
                  Back to Login
                </Button>
              </form>
            ) : isOTPStep ? (
              <form onSubmit={handleOTPVerification} className="space-y-4">
                <div className="text-sm text-gray-600 mb-4 text-center">
                  We've sent a 6-digit OTP to your {signupMethod === 'email' ? 'email address' : 'mobile number'}
                  <br />
                  <span className="font-medium">
                    {signupMethod === 'email' ? formData.emailOrPhone : `${countryCode} ${HybridMobileAuthService.formatPhoneNumber(formData.emailOrPhone)}`}
                  </span>
                </div>
                <div className="relative">
                  <Input
                    name="otp"
                    type="text"
                    placeholder="Enter 6-digit OTP"
                    className="pl-12 text-center text-lg tracking-widest"
                    value={formData.otp}
                    onChange={(e) => setFormData({...formData, otp: e.target.value.replace(/\D/g, '').slice(0, 6)})}
                    maxLength={6}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <MessageSquare className="h-5 w-5" />
                  </span>
                </div>
                <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading || formData.otp.length !== 6}>
                  {loading ? (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Verify & Create Account
                    </>
                  )}
                </Button>
                <div className="flex gap-2">
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={() => {
                      setIsOTPStep(false)
                      setFormData({...formData, otp: ''})
                      setError('')
                    }}
                  >
                    Back
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    className="flex-1"
                    onClick={handleResendOTP}
                    disabled={resendTimer > 0 || loading}
                  >
                    {resendTimer > 0 ? (
                      <>
                        <Clock className="w-4 h-4 mr-1" />
                        {resendTimer}s
                      </>
                    ) : (
                      <>
                        <RefreshCw className="w-4 h-4 mr-1" />
                        Resend
                      </>
                    )}
                  </Button>
                </div>
              </form>
            ) : (
              <form onSubmit={isSignUp ? handleCustomerSignup : handleSignIn} className="space-y-4">
                {isSignUp && (
                  <>
                    {/* Full Name */}
                    <div className="relative">
                      <Input
                        name="fullName"
                        type="text"
                        placeholder="Full Name*"
                        className="pl-12"
                        value={formData.fullName}
                        onChange={handleInputChange}
                        required
                      />
                      <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                        <User className="h-5 w-5" />
                      </span>
                    </div>

                    {/* Email */}
                    <div className="relative">
                      <Input
                        name="email"
                        type="email"
                        placeholder="Email Address*"
                        className="pl-12"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                      />
                      <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                        <Mail className="h-5 w-5" />
                      </span>
                    </div>

                    {/* Phone */}
                    <div className="relative">
                      <Input
                        name="phone"
                        type="tel"
                        placeholder="Phone Number (10 digits)*"
                        className="pl-12"
                        value={formData.phone}
                        onChange={(e) => {
                          const value = e.target.value.replace(/\D/g, '').slice(0, 10)
                          setFormData(prev => ({ ...prev, phone: value }))
                          setError('')
                        }}
                        maxLength={10}
                        required
                      />
                      <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                        <Phone className="h-5 w-5" />
                      </span>
                    </div>
                  </>
                )}

                {/* Login Email/Phone Input - only for login mode */}
                {!isSignUp && (
                  <div className="relative">
                    <Input
                      name="emailOrPhone"
                      type="text"
                      placeholder="Enter email or phone number*"
                      className="pl-12"
                      value={formData.emailOrPhone}
                      onChange={handleInputChange}
                      required
                    />
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                      <Mail className="h-5 w-5" />
                    </span>
                  </div>
                )}

                {/* Password field */}
                <div className="relative">
                  <Input
                    name="password"
                    type={showPassword ? "text" : "password"}
                    placeholder={isSignUp ? "Password (min 8 characters)*" : "Password*"}
                    className="pl-12 pr-12"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                  />
                  <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                    <Lock className="h-5 w-5" />
                  </span>
                  <button
                    type="button"
                    className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                  </button>
                </div>

                {/* Confirm Password field - only for signup */}
                {isSignUp && (
                  <div className="relative">
                    <Input
                      name="confirmPassword"
                      type="password"
                      placeholder="Confirm Password*"
                      className="pl-12"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      required
                    />
                    <span className="absolute left-4 top-1/2 -translate-y-1/2 text-primary">
                      <Lock className="h-5 w-5" />
                    </span>
                  </div>
                )}

                {/* Terms and Conditions - only for signup */}
                {isSignUp && (
                  <div className="flex items-start space-x-2">
                    <Checkbox
                      id="agreeToTerms"
                      checked={formData.agreeToTerms}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, agreeToTerms: checked as boolean }))}
                      className="mt-1"
                    />
                    <label htmlFor="agreeToTerms" className="text-sm text-gray-600">
                      I agree to the{" "}
                      <Link href="/terms" className="text-primary hover:text-primary/90">
                        Terms of Service
                      </Link>{" "}
                      and{" "}
                      <Link href="/privacy" className="text-primary hover:text-primary/90">
                        Privacy Policy
                      </Link>
                    </label>
                  </div>
                )}

                {/* Forgot Password */}
                {!isSignUp && (
                  <div className="text-right">
                    <button
                      type="button"
                      className="text-sm text-primary hover:underline"
                      onClick={() => setIsForgotPassword(true)}
                    >
                      Forgot Password?
                    </button>
                  </div>
                )}
                <Button type="submit" className="w-full bg-primary hover:bg-primary/90" disabled={loading}>
                  {loading ? (
                    isSignUp ? 'Creating Account...' : 'Signing In...'
                  ) : (
                    isSignUp ? (
                      <>
                        <User className="w-4 h-4 mr-2" />
                        Create Account
                      </>
                    ) : 'Sign In'
                  )}
                </Button>
                <div className="text-center">
                  <button
                    type="button"
                    className="text-sm text-primary hover:underline"
                    onClick={() => setIsSignUp(!isSignUp)}
                  >
                    {isSignUp ? 'Already have an account? Sign In' : "Don't have an account? Sign Up"}
                  </button>


                </div>
              </form>
            )}
            {!isConfirmation && !isResetCode && !isForgotPassword && !isOTPStep && (
              <>
                <div className="flex items-center my-6">
                  <div className="flex-grow h-px bg-gray-300" />
                  <span className="mx-4 text-gray-400">OR</span>
                  <div className="flex-grow h-px bg-gray-300" />
                </div>
                <div className="text-center mb-4 font-semibold text-gray-700">Continue With</div>
                <div className="flex gap-4 mb-6">
                  <Button variant="outline" className="flex-1 flex items-center justify-center gap-2 border-gray-300" disabled>
                    <svg className="w-5 h-5" viewBox="0 0 24 24"><g><path d="M21.805 10.023h-9.765v3.955h5.625c-.242 1.242-1.484 3.648-5.625 3.648-3.375 0-6.125-2.797-6.125-6.25s2.75-6.25 6.125-6.25c1.922 0 3.211.82 3.953 1.523l2.703-2.633c-1.719-1.594-3.945-2.57-6.656-2.57-5.523 0-10 4.477-10 10s4.477 10 10 10c5.781 0 9.594-4.055 9.594-9.766 0-.656-.07-1.156-.156-1.605z" fill="#4285F4"/><path d="M3.545 7.345l3.242 2.379c.883-1.18 2.367-2.016 4.213-2.016 1.172 0 2.273.406 3.125 1.211l2.625-2.57c-1.719-1.594-3.945-2.57-6.656-2.57-3.617 0-6.68 2.055-8.211 5.045z" fill="#34A853"/><path d="M12 22c2.672 0 4.922-.883 6.563-2.406l-3.047-2.492c-.844.57-1.922.914-3.516.914-2.703 0-4.992-1.828-5.813-4.297l-3.211 2.484c1.523 3.008 4.586 5.047 8.024 5.047z" fill="#FBBC05"/><path d="M21.805 10.023h-9.765v3.955h5.625c-.242 1.242-1.484 3.648-5.625 3.648-3.375 0-6.125-2.797-6.125-6.25s2.75-6.25 6.125-6.25c1.922 0 3.211.82 3.953 1.523l2.703-2.633c-1.719-1.594-3.945-2.57-6.656-2.57-5.523 0-10 4.477-10 10s4.477 10 10 10c5.781 0 9.594-4.055 9.594-9.766 0-.656-.07-1.156-.156-1.605z" fill="#EA4335"/></g></svg>
                    Google (Coming Soon)
                  </Button>
                  <Button variant="outline" className="flex-1 flex items-center justify-center gap-2 border-gray-300" disabled>
                    <svg className="w-5 h-5" viewBox="0 0 24 24"><path fill="#1877F3" d="M22.675 0h-21.35C.6 0 0 .6 0 1.326v21.348C0 23.4.6 24 1.325 24h11.495v-9.294H9.692v-3.622h3.128V8.413c0-3.1 1.893-4.788 4.659-4.788 1.325 0 2.463.099 2.797.143v3.24l-1.918.001c-1.504 0-1.797.715-1.797 1.763v2.313h3.587l-.467 3.622h-3.12V24h6.116C23.4 24 24 23.4 24 22.674V1.326C24 .6 23.4 0 22.675 0"/></svg>
                    Facebook (Coming Soon)
                  </Button>
                </div>

              </>
            )}
          </div>
        </div>
      </section>
      <Footer />
    </div>
  )
}