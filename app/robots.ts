import { MetadataRoute } from 'next';
import { SITE_URL } from '@/lib/config/seo';

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/admin/',
          '/dashboard/',
          '/api/',
          '/login/',
          '/register/',
          '/forgot-password/',
          '/vendor-login/',
          '/admin-login/',
          '/checkout/',
          '/payment/',
          '/user-review-demo/',
          '/review-system-demo/',
          '/state-vendor-demo/',
          '/multilingual-state-vendor-demo/',
          '/header-demo/',
          '/image-upload-demo/',
          '/language-demo/',
          '/*?*', // Disallow URLs with query parameters
          '/search?*', // Allow search but disallow with parameters
        ],
      },
      {
        userAgent: 'Googlebot',
        allow: '/',
        disallow: [
          '/admin/',
          '/dashboard/',
          '/api/',
          '/login/',
          '/register/',
          '/forgot-password/',
          '/vendor-login/',
          '/admin-login/',
          '/checkout/',
          '/payment/',
        ],
      },
      {
        userAgent: 'Bingbot',
        allow: '/',
        disallow: [
          '/admin/',
          '/dashboard/',
          '/api/',
          '/login/',
          '/register/',
          '/forgot-password/',
          '/vendor-login/',
          '/admin-login/',
          '/checkout/',
          '/payment/',
        ],
      },
    ],
    sitemap: `${SITE_URL}/sitemap.xml`,
    host: SITE_URL,
  };
}
