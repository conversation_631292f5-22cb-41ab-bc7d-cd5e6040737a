"use client"

import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { EntityReviews } from "@/components/entity-reviews"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Star, Heart, Share2, ShoppingCart, Truck, Shield, RotateCcw, Plus, Minus, Check, X, Loader2 } from "lucide-react"
import Image from "next/image"
import { useState, useEffect } from "react"
import { ClientRoot, useCart } from "@/components/ClientRoot"
import { shopService, ShopResponse } from "@/lib/services/shopService"
import { showToast, toastMessages } from '@/lib/toast'
import { Amplify } from 'aws-amplify'
import awsExports from '@/src/aws-exports'
import { useParams } from 'next/navigation'
import { Carousel } from '@/components/ui/carousel';
import { useRouter } from "next/navigation";
import FavoriteButton from '@/components/FavoriteButton'
import AddToCartButton from '@/components/AddToCartButton';

// Configure Amplify
Amplify.configure(awsExports)

function ProductDetailsPageContent() {
  const [quantity, setQuantity] = useState(1)
  const [selectedSize, setSelectedSize] = useState("")
  const [selectedColor, setSelectedColor] = useState("")
  const [product, setProduct] = useState<ShopResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const [relatedProducts, setRelatedProducts] = useState<ShopResponse[]>([])
  const [isShared, setIsShared] = useState(false)
  const [shareClicked, setShareClicked] = useState(false)
  const params = useParams()
  const router = useRouter();

  // Load product data on component mount
  useEffect(() => {
    if (params.id) {
      loadProduct(params.id as string)
    }
  }, [params.id])

  // Ensure quantity is always a valid number
  useEffect(() => {
    if (isNaN(quantity) || quantity < 1) {
      setQuantity(1)
    }
  }, [quantity])



  // Handle share
  const handleShare = async () => {
    setShareClicked(true)
    setIsShared(true)
    try {
      if (navigator.share) {
        await navigator.share({
          title: product?.name || 'Check out this product',
          text: `Look at this amazing product: ${product?.name}`,
          url: window.location.href,
        })
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(window.location.href)
        showToast.success('Link copied to clipboard!')
      }
    } catch (error) {
      console.error('Error sharing:', error)
      showToast.error('Failed to share')
    } finally {
      setTimeout(() => setIsShared(false), 1000)
      setTimeout(() => setShareClicked(false), 250)
    }
  }

  const loadProduct = async (productId: string) => {
    try {
      setLoading(true)
      const productData = await shopService.getShop(productId)
      setProduct(productData)

      // Load related products from the same category
      if (productData.category) {
        const allProducts = await shopService.listShops()
        const related = allProducts.items
          .filter(p => p.category === productData.category && p.id !== productId)
          .slice(0, 4)
        setRelatedProducts(related)
      }
    } catch (error) {
      console.error('Error loading product:', error)
    } finally {
      setLoading(false)
    }
  }

  // Animation classes for creative effect
  const shareAnimClass = shareClicked ? 'scale-125 -rotate-6 shadow-lg' : '';

  if (loading) {
    return (
      <ClientRoot>
        <TopHeader />
        <Header />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading product details...</p>
          </div>
        </div>
        <Footer />
      </ClientRoot>
    )
  }

  if (!product) {
    return (
      <ClientRoot>
        <TopHeader />
        <Header />
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Product Not Found</h1>
            <p className="text-gray-600 mb-4">The product you're looking for doesn't exist.</p>
            <Button onClick={() => window.history.back()}>Go Back</Button>
          </div>
        </div>
        <Footer />
      </ClientRoot>
    )
  }

  // Removed mockProduct - using real data from database


  return (
    <div className="min-h-screen bg-white">
      <TopHeader />
      <Header />

      <section className="py-12 mt-10">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Product Images */}
            <div className="space-y-4">
              <div className="relative">
                <Carousel
                  images={(product.images || []).slice(0, 3).map((img, idx) => ({
                    src: img || '/placeholder.svg',
                    alt: `${product.name} ${idx + 1}`
                  }))}
                  showArrows={true}
                  showDots={true}
                  className="w-full h-96 lg:h-[600px] rounded-lg"
                />
                <div className="absolute top-4 right-4 flex space-x-2">
                  <FavoriteButton
                    entityId={product.id}
                    entityType="SHOP_ITEM"
                    entityData={{
                      name: product.name,
                      image: product.images?.[0],
                      price: product.price,
                      location: product.brand,
                      city: product.category,
                      state: '',
                      rating: product.rating,
                      reviewCount: product.reviewCount,
                      description: product.description
                    }}
                    variant="secondary"
                    size="sm"
                    className="bg-white/90 hover:bg-white transition-all duration-300"
                  />
                  <Button 
                    size="sm" 
                    variant="secondary" 
                    className={`bg-white/90 hover:bg-white transition-all duration-300 ${
                      isShared ? 'text-[#8B0000]' : 'text-[#CD5C5C]'
                    } ${shareAnimClass}`}
                    onClick={handleShare}
                  >
                    <Share2 className={`h-10 w-10 ${isShared ? 'fill-current' : ''} transition-transform duration-300`} />
                  </Button>
                </div>
              </div>
            </div>

            {/* Product Details */}
            <div className="space-y-6">
              <div>
                <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
                <p className="text-gray-600 mb-4">by {product.brand}</p>
                {/* Price */}
                <div className="flex items-center space-x-3 mb-2">
                  <span className="text-3xl font-bold text-primary">{product.price}</span>
                  {product.originalPrice && (
                    <span className="text-xl text-gray-500 line-through">
                      {product.originalPrice}
                    </span>
                  )}
                  {product.discount && product.discount > 0 && (
                    <span className="text-base font-bold text-red-600">{product.discount}% OFF</span>
                  )}
                </div>
                {/* Rating - moved below price */}
                <div className="flex items-center space-x-2 mb-4">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < Math.floor(product.rating || 0) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                        }`}
                      />
                    ))}
                  </div>
                  <span className="font-medium">{product.rating || 0}</span>
                  <span className="text-gray-500">({product.reviewCount ?? 0} reviews)</span>
                </div>

                {/* Stock Status */}
                <div className="mb-6">
                  {product.inStock ? (
                    <div className="flex items-center text-green-600">
                      <Check className="h-4 w-4 mr-2" />
                      <span>In Stock ({product.stock || 0} left)</span>
                    </div>
                  ) : (
                    <div className="flex items-center text-red-600">
                      <X className="h-4 w-4 mr-2" />
                      <span>Out of Stock</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Size Selection */}
              <div>
                <h3 className="font-semibold mb-3">Size</h3>
                <div className="grid grid-cols-4 gap-2">
                  {(product.sizes || []).map((size) => (
                    <Button
                      key={size}
                      variant={selectedSize === size ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedSize(size)}
                      className={selectedSize === size ? "bg-primary hover:bg-primary/90" : ""}
                    >
                      {size}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Color Selection */}
              <div>
                <h3 className="font-semibold mb-3">Color</h3>
                <div className="flex flex-wrap gap-2">
                  {(product.colors || []).map((color) => (
                    <Button
                      key={color}
                      variant={selectedColor === color ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedColor(color)}
                      className={selectedColor === color ? "bg-primary hover:bg-primary/90" : ""}
                    >
                      {color}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Quantity */}
              <div>
                <h3 className="font-semibold mb-3">Quantity</h3>
                <div className="flex items-center space-x-3">
                  <Button variant="outline" size="sm" onClick={() => setQuantity(Math.max(1, (quantity || 1) - 1))}>
                    <Minus className="h-4 w-4" />
                  </Button>
                  <span className="w-12 text-center font-medium">{quantity || 1}</span>
                  <Button variant="outline" size="sm" onClick={() => setQuantity((quantity || 1) + 1)}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <AddToCartButton
                  productId={product.id}
                  productData={{
                    name: product.name,
                    image: product.images?.[0],
                    price: typeof product.price === 'string'
                      ? Number(product.price.replace(/[₹,]/g, ""))
                      : Number(product.price),
                    originalPrice: product.originalPrice ?
                      (typeof product.originalPrice === 'string'
                        ? Number(product.originalPrice.replace(/[₹,]/g, ""))
                        : Number(product.originalPrice)) : undefined,
                    discount: product.discount,
                    brand: product.brand,
                    category: product.category,
                    description: product.description
                  }}
                  className="flex-1 text-lg py-6"
                  initialQuantity={quantity}
                />
                <Button
                  variant="outline"
                  className="flex-1 py-6 bg-transparent"
                  disabled={!product.inStock}
                  onClick={() => {
                    try {
                      // Parse price correctly - handle both ₹1,000 and 1000 formats
                      const priceStr = product.price.toString()
                      const numericPrice = priceStr.includes('₹')
                        ? Number(priceStr.replace(/[₹,]/g, ""))
                        : Number(priceStr.replace(/[^\d]/g, ""))

                      // Validate price and quantity
                      if (isNaN(numericPrice) || numericPrice <= 0) {
                        console.error('Invalid price:', product.price)
                        showToast.error('Error: Invalid product price')
                        return
                      }

                      if (isNaN(quantity) || quantity <= 0) {
                        console.error('Invalid quantity:', quantity)
                        showToast.error('Error: Invalid quantity')
                        return
                      }

                      // Create checkout URL with product details
                      const checkoutParams = new URLSearchParams()
                      checkoutParams.set('productId', product.id)
                      checkoutParams.set('productName', encodeURIComponent(product.name))
                      checkoutParams.set('productPrice', numericPrice.toString())
                      checkoutParams.set('productImage', encodeURIComponent(product.images?.[0] || '/placeholder.svg'))
                      checkoutParams.set('quantity', quantity.toString())

                      if (selectedSize) {
                        checkoutParams.set('size', encodeURIComponent(selectedSize))
                      }
                      if (selectedColor) {
                        checkoutParams.set('color', encodeURIComponent(selectedColor))
                      }

                      // Redirect to checkout page
                      router.push(`/checkout?productId=${product.id}&quantity=${quantity}${selectedSize ? `&size=${encodeURIComponent(selectedSize)}` : ''}${selectedColor ? `&color=${encodeURIComponent(selectedColor)}` : ''}`);
                    } catch (error) {
                      console.error('Error processing buy now:', error)
                      showToast.error('Error processing purchase. Please try again.')
                    }
                  }}
                >
                  Buy Now
                </Button>
              </div>

              {/* Features */}
              <div className="border-t pt-6">
                <h3 className="font-semibold mb-3">Key Features</h3>
                <ul className="space-y-2">
                  {(product.features || []).map((feature, index) => (
                    <li key={index} className="flex items-center text-sm">
                      <Check className="h-4 w-4 text-green-600 mr-2" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>

              {/* Services */}
              <div className="grid grid-cols-3 gap-4 pt-6 border-t">
                <div className="text-center">
                  <Truck className="h-6 w-6 mx-auto mb-2 text-blue-600" />
                  <p className="text-xs font-medium">Free Delivery</p>
                  <p className="text-xs text-gray-500">Above ₹50,000</p>
                </div>
                <div className="text-center">
                  <RotateCcw className="h-6 w-6 mx-auto mb-2 text-green-600" />
                  <p className="text-xs font-medium">15 Days Return</p>
                  <p className="text-xs text-gray-500">Easy returns</p>
                </div>
                <div className="text-center">
                  <Shield className="h-6 w-6 mx-auto mb-2 text-purple-600" />
                  <p className="text-xs font-medium">Secure Payment</p>
                  <p className="text-xs text-gray-500">100% secure</p>
                </div>
              </div>
            </div>
          </div>

          {/* Product Details Tabs */}
          <div className="mt-16">
            <Tabs defaultValue="description" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="description">Description</TabsTrigger>
                <TabsTrigger value="specifications">Specifications</TabsTrigger>
                <TabsTrigger value="reviews">Reviews ({product.reviewCount || 0})</TabsTrigger>
              </TabsList>

              <TabsContent value="description" className="mt-6">
                <Card>
                  <CardContent className="p-6">
                    <h3 className="text-xl font-bold mb-4">Product Description</h3>
                    <p className="text-gray-700 leading-relaxed">{product.description || 'No description available.'}</p>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="specifications" className="mt-6">
                <Card>
                  <CardContent className="p-6">
                    <h3 className="text-xl font-bold mb-4">Specifications</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {product.specifications ? Object.entries(product.specifications).map(([key, value]) => (
                        <div key={key} className="flex justify-between py-2 border-b">
                          <span className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}:</span>
                          <span className="text-gray-600">{value || 'Not specified'}</span>
                        </div>
                      )) : (
                        <p className="text-gray-500">No specifications available.</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="reviews" className="mt-6">
                <EntityReviews
                  entityType="SHOP"
                  entityId={product.id}
                  entityName={product.name}
                />
              </TabsContent>
            </Tabs>
          </div>

          {/* Related Products */}
          <div className="mt-16">
            <h2 className="text-2xl font-bold mb-8">You May Also Like</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {relatedProducts.map((relatedProduct) => (
                <Card key={relatedProduct.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative">
                    <Image
                      src={relatedProduct.images?.[0] || "/placeholder.svg"}
                      alt={relatedProduct.name}
                      width={300}
                      height={300}
                      className="w-full h-48 object-cover"
                    />
                    <FavoriteButton
                      entityId={relatedProduct.id}
                      entityType="SHOP_ITEM"
                      entityData={{
                        name: relatedProduct.name,
                        image: relatedProduct.images?.[0],
                        price: relatedProduct.price,
                        location: relatedProduct.brand,
                        city: relatedProduct.category,
                        state: '',
                        rating: relatedProduct.rating,
                        reviewCount: relatedProduct.reviewCount,
                        description: relatedProduct.description
                      }}
                      variant="secondary"
                      size="sm"
                      className="absolute top-2 right-2 bg-white/90 hover:bg-white"
                    />
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold mb-2 line-clamp-2">{relatedProduct.name}</h3>
                    <div className="flex items-center mb-2">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                      <span className="text-sm">{relatedProduct.rating || 0}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-bold text-primary">{relatedProduct.price}</span>
                      {relatedProduct.originalPrice && (
                        <span className="text-sm text-gray-500 line-through">
                          {relatedProduct.originalPrice}
                        </span>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}

export default function ProductDetailsPage() {
  return (
    <ClientRoot>
      <ProductDetailsPageContent />
    </ClientRoot>
  )
}
