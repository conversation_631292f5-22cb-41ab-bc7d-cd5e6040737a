'use client'

import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { Foot<PERSON> } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Heart, ShoppingCart, Star, Filter, Loader2, Eye } from "lucide-react"

import Link from "next/link"
import { ClientRoot, useCart } from "@/components/ClientRoot"
import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { shopService, ShopResponse } from "@/lib/services/shopService"
import { useSafeTranslation } from '@/hooks/use-safe-translation'
import { Amplify } from 'aws-amplify'
import awsExports from '@/src/aws-exports'
import FavoriteButton from '@/components/FavoriteButton'
import AddToCartButton from '@/components/AddToCartButton'

// Configure Amplify
Amplify.configure(awsExports)

function ShopPageContent() {

  const { t } = useSafeTranslation()
  const { cart } = useCart()
  const searchParams = useSearchParams()
  const [products, setProducts] = useState<ShopResponse[]>([])
  const [loading, setLoading] = useState(true)
  const [nextToken, setNextToken] = useState<string | undefined>(undefined)
  const [hasMoreData, setHasMoreData] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [priceRange, setPriceRange] = useState("all")
  const [selectedBrand, setSelectedBrand] = useState("all")
  const [selectedRating, setSelectedRating] = useState("all")
  const [selectedAvailability, setSelectedAvailability] = useState("all")
  const [selectedDiscount, setSelectedDiscount] = useState("all")
  const [sortBy, setSortBy] = useState("popular")

  const [wishlist, setWishlist] = useState<string[]>([])
  const [showCartPreview, setShowCartPreview] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set())

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(12) // 12 products per page
  const [totalServerItems, setTotalServerItems] = useState(0)
  const [serverPages, setServerPages] = useState<{[key: number]: ShopResponse[]}>({})
  const [loadedPages, setLoadedPages] = useState<Set<number>>(new Set([1]))

  // Load products on component mount
  useEffect(() => {
    loadProducts()

    // Handle URL search parameters
    const searchParam = searchParams.get('search')
    const locationParam = searchParams.get('location')
    const dateParam = searchParams.get('date')

    if (searchParam) {
      setSearchTerm(searchParam)
    }
    if (locationParam) {
      // You can add location-based filtering here if needed
      console.log('Location filter:', locationParam)
    }
    if (dateParam) {
      // setSelectedDate(new Date(dateParam)) // This line was commented out in the original file
    }
  }, [searchParams])

  // Helper functions
  const isInCart = (productId: string) => {
    return cart.some(item => item.id === productId)
  }

  const isInWishlist = (productId: string) => {
    return wishlist.includes(productId)
  }

  const toggleWishlist = (productId: string) => {
    setWishlist(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    )
  }



  const loadProducts = async (reset: boolean = false) => {
    try {
      setLoading(true)

      if (reset) {
        // Reset all pagination state
        setServerPages({})
        setLoadedPages(new Set())
        setCurrentPage(1)
        setNextToken(undefined)
      }

      const response = await shopService.listShops(undefined, 50) // Load larger chunks for server-side pagination

      // Store all products for client-side pagination
      setProducts(response.items)
      setTotalServerItems(response.items.length)

      // Organize products into pages for traditional pagination
      const pages: {[key: number]: ShopResponse[]} = {}
      for (let i = 0; i < response.items.length; i += itemsPerPage) {
        const pageNumber = Math.floor(i / itemsPerPage) + 1
        pages[pageNumber] = response.items.slice(i, i + itemsPerPage)
      }
      setServerPages(pages)
      setLoadedPages(new Set(Object.keys(pages).map(Number)))

      setNextToken(response.nextToken)
      setHasMoreData(!!response.nextToken)
    } catch (error) {
      console.error('Error loading products:', error)
    } finally {
      setLoading(false)
    }
  }

  const clearAllFilters = () => {
    setSearchTerm("")
    setSelectedCategory("all")
    setPriceRange("all")
    setSelectedBrand("all")
    setSelectedRating("all")
    setSelectedAvailability("all")
    setSelectedDiscount("all")
    setSortBy("popular")
  }

  const hasActiveFilters = () => {
    return searchTerm || selectedCategory !== "all" || priceRange !== "all" ||
           selectedBrand !== "all" || selectedRating !== "all" ||
           selectedAvailability !== "all" || selectedDiscount !== "all"
  }

  // Helper function to get product image with fallback
  const getProductImage = (product: ShopResponse) => {
    if (imageErrors.has(product.id)) {
      return "/placeholder.svg"
    }

    // Check if product has images and the first image is valid
    const firstImage = product.images?.[0];
    if (firstImage && firstImage.trim() !== '' && firstImage !== 'undefined' && firstImage !== 'null') {
      return firstImage;
    }

    return "/placeholder.svg"
  }

  // Handle image load errors
  const handleImageError = (productId: string) => {
    setImageErrors(prev => new Set([...prev, productId]))
  }

  // Get unique values for filter options
  const getUniqueCategories = () => {
    const categories = products.map(p => p.category).filter(Boolean)
    return [...new Set(categories)]
  }

  const getUniqueBrands = () => {
    const brands = products.map(p => p.brand).filter(Boolean)
    return [...new Set(brands)]
  }

  // Filter and sort products
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (product.brand && product.brand.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesCategory = selectedCategory === "all" || product.category.toLowerCase() === selectedCategory.toLowerCase()

    const matchesBrand = selectedBrand === "all" || (product.brand && product.brand.toLowerCase() === selectedBrand.toLowerCase())

    let matchesPrice = true
    if (priceRange !== "all") {
      const price = parseInt(product.price.replace(/[^\d]/g, ''))
      switch (priceRange) {
        case "0-5000":
          matchesPrice = price <= 5000
          break
        case "5000-25000":
          matchesPrice = price > 5000 && price <= 25000
          break
        case "25000-50000":
          matchesPrice = price > 25000 && price <= 50000
          break
        case "50000+":
          matchesPrice = price > 50000
          break
      }
    }

    const matchesRating = selectedRating === "all" || (() => {
      const rating = product.rating || 0
      switch (selectedRating) {
        case "4-plus": return rating >= 4
        case "3-plus": return rating >= 3
        case "2-plus": return rating >= 2
        default: return true
      }
    })()

    const matchesAvailability = selectedAvailability === "all" || (() => {
      switch (selectedAvailability) {
        case "in-stock": return product.inStock
        case "out-of-stock": return !product.inStock
        default: return true
      }
    })()

    const matchesDiscount = selectedDiscount === "all" || (() => {
      const discount = product.discount || 0
      switch (selectedDiscount) {
        case "on-sale": return discount > 0
        case "10-plus": return discount >= 10
        case "20-plus": return discount >= 20
        case "50-plus": return discount >= 50
        default: return true
      }
    })()

    return matchesSearch && matchesCategory && matchesBrand && matchesPrice &&
           matchesRating && matchesAvailability && matchesDiscount
  }).sort((a, b) => {
    switch (sortBy) {
      case "price-low":
        return parseInt(a.price.replace(/[^\d]/g, '')) - parseInt(b.price.replace(/[^\d]/g, ''))
      case "price-high":
        return parseInt(b.price.replace(/[^\d]/g, '')) - parseInt(a.price.replace(/[^\d]/g, ''))
      case "rating":
        return (b.rating || 0) - (a.rating || 0)
      default:
        return 0
    }
  })

  // Hybrid pagination logic
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedProducts = filteredProducts.slice(startIndex, endIndex)

  // Reset and reload data when filters change
  useEffect(() => {
    setCurrentPage(1)
    loadProducts(true) // Reset and load first page
  }, [searchTerm, selectedCategory, priceRange, selectedBrand, selectedRating, selectedAvailability, selectedDiscount])

  // Traditional pagination functions
  const goToPage = (page: number) => {
    setCurrentPage(page)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const goToPrevious = () => {
    if (currentPage > 1) {
      goToPage(currentPage - 1)
    }
  }

  const goToNext = () => {
    if (currentPage < totalPages) {
      goToPage(currentPage + 1)
    }
  }

  const getPageNumbers = () => {
    const pages = []
    const maxVisiblePages = 5
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2))
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1)
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i)
    }
    return pages
  }

  // Load more data function for additional server data
  const loadMoreProducts = async () => {
    if (hasMoreData && !loading) {
      await loadProducts(false) // Load next page and append
    }
  }

  const showLoadMore = hasMoreData && !loading && totalPages <= Math.ceil(products.length / itemsPerPage)

  const categories = [
    t('shop.categories.all', 'All Categories'),
    t('shop.categories.bridalWear', 'Bridal Wear'),
    t('shop.categories.groomWear', 'Groom Wear'),
    t('shop.categories.jewelry', 'Jewelry'),
    t('shop.categories.beauty', 'Beauty'),
    t('shop.categories.decor', 'Decorations'),
    t('shop.categories.invitations', 'Invitations'),
    t('shop.categories.accessories', 'Accessories'),
    t('shop.categories.gifts', 'Gifts'),
  ]

  return (
    <div className="min-h-screen bg-white">
      <TopHeader />
      <Header />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-[#F8F5F0] to-[#F6C244] py-12">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">{t('shop.title', 'Wedding Shopping')}</h1>
            <p className="text-lg text-gray-600">{t('shop.subtitle', 'Everything you need for your perfect wedding day')}</p>
          </div>

          {/* Cart Summary Bar */}
          {cart.length > 0 && (
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6 max-w-2xl mx-auto">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <ShoppingCart className="h-5 w-5 text-primary" />
                  <div>
                    <p className="font-medium text-gray-900">
                      {cart.length} item{cart.length !== 1 ? 's' : ''} in cart
                    </p>
                    <p className="text-sm text-gray-600">
                      Total: ₹{cart.reduce((total, item) => total + (item.price * item.quantity), 0).toLocaleString()}
                    </p>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Link href="/cart">
                    <Button variant="outline" size="sm">
                      View Cart
                    </Button>
                  </Link>
                  <Link href="/checkout">
                    <Button size="sm" className="bg-primary hover:bg-primary/90">
                      Checkout
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          )}

          {/* Simple Search and Filters */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search products..."
                  className="pl-10 h-11"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="h-11">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {getUniqueCategories().map((category) => (
                    <SelectItem key={category} value={category.toLowerCase()}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="h-11">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="popular">Most Popular</SelectItem>
                  <SelectItem value="price-low">Price: Low to High</SelectItem>
                  <SelectItem value="price-high">Price: High to Low</SelectItem>
                  <SelectItem value="rating">Highest Rated</SelectItem>
                </SelectContent>
              </Select>
            </div>

      {hasActiveFilters() && (
              <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-100">
                <div className="flex flex-wrap gap-2">
              {searchTerm && (
                <Badge variant="secondary" className="rounded-full">
                  Search: "{searchTerm}"
                  <button
                    onClick={() => setSearchTerm("")}
                    className="ml-2 hover:text-red-600"
                  >
                    ×
                  </button>
                </Badge>
              )}
              {selectedCategory !== "all" && (
                <Badge variant="secondary" className="rounded-full">
                      {selectedCategory}
                  <button
                    onClick={() => setSelectedCategory("all")}
                    className="ml-2 hover:text-red-600"
                  >
                    ×
                  </button>
                </Badge>
              )}
                </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                  className="text-gray-500 hover:text-gray-700"
              >
                Clear All
              </Button>
              </div>
            )}
            </div>
          </div>
        </section>



      {/* Products Grid */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Products ({filteredProducts.length})</h2>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
              <span className="ml-2 text-gray-600">Loading products...</span>
            </div>
          ) : filteredProducts.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-500 mb-4">No products found</div>
              <Button onClick={() => loadProducts(true)} variant="outline">
                Refresh Products
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {paginatedProducts.map((product) => (
                <Card key={product.id} className="overflow-hidden hover:shadow-lg transition-all duration-300 group scale-100 mx-auto h-[500px] flex flex-col">
                  <div className="relative flex-shrink-0">
                    <Link href={`/shop/${product.id}`}>
                      <img
                        src={getProductImage(product)}
                        alt={product.name || 'Product Image'}
                        className="w-full h-80 object-cover transition-transform duration-300 group-hover:scale-105 group-hover:-translate-y-1 group-hover:shadow-lg"
                        onError={() => handleImageError(product.id)}
                      />
                    </Link>

                    {/* Favorite Button */}
                    <div className="absolute top-2 right-2">
                      <FavoriteButton
                        entityId={product.id}
                        entityType="SHOP_ITEM"
                        entityData={{
                          name: product.name,
                          image: product.images?.[0],
                          price: product.price,
                          location: product.brand,
                          city: product.category,
                          state: '',
                          rating: product.rating,
                          reviewCount: product.reviewCount,
                          description: product.description
                        }}
                        variant="secondary"
                        size="sm"
                        className="bg-white/90 hover:bg-white shadow-sm"
                      />
                    </div>
                  </div>
                  <CardContent className="p-4 flex-1 flex flex-col">
                    <Badge className="text-xs mb-2 bg-[#F6C244]/20 text-[#7c5a13] border-none w-fit hover:bg-[#F6C244]/20">
                      {product.category}
                    </Badge>

                    <Link href={`/shop/${product.id}`}>
                      <h3 className="font-medium text-base mb-0 line-clamp-2 hover:text-primary transition-colors min-h-[2.5rem]">
                        {product.name}
                      </h3>
                    </Link>

                    {/* Price and Rating Row */}
                    <div className="flex items-center justify-between mb-4 mt-auto">
                      <div className="flex items-center gap-2 flex-wrap">
                        {product.discount && product.discount > 0 ? (
                          <>
                            <span className="text-2xl font-extrabold text-primary">{product.price}</span>
                            <span className="text-base text-gray-400 line-through font-medium">{product.originalPrice || ''}</span>
                            <span className="text-sm font-bold text-red-600 ml-1">{product.discount}% OFF</span>
                          </>
                        ) : (
                          <span className="text-2xl font-extrabold text-primary">{product.price}</span>
                        )}
                      </div>
                      <div className="flex items-center ml-2 flex-shrink-0">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm font-medium ml-1">{product.rating || 0}</span>
                      </div>
                    </div>


                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Traditional Pagination */}
          {totalPages > 1 && (
            <div className="flex flex-col items-center mt-12 space-y-4">
              {/* Results info */}
              <div className="text-sm text-gray-600">
                Showing {startIndex + 1}-{Math.min(endIndex, filteredProducts.length)} of {filteredProducts.length} products
                {hasMoreData && (
                  <span className="ml-2 text-primary">
                    ({products.length} loaded from server)
                  </span>
                )}
              </div>

              {/* Pagination controls */}
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  onClick={goToPrevious}
                  disabled={currentPage === 1}
                  className="px-3 py-2"
                >
                  Previous
                </Button>

                {getPageNumbers().map((page) => (
                  <Button
                    key={page}
                    variant={currentPage === page ? "default" : "outline"}
                    onClick={() => goToPage(page)}
                    className={`px-3 py-2 ${
                      currentPage === page
                        ? "bg-primary text-white"
                        : "hover:bg-gray-100"
                    }`}
                  >
                    {page}
                  </Button>
                ))}

                <Button
                  variant="outline"
                  onClick={goToNext}
                  disabled={currentPage === totalPages}
                  className="px-3 py-2"
                >
                  Next
                </Button>
              </div>

              {/* Load More Server Data Button */}
              {showLoadMore && (
                <Button
                  onClick={loadMoreProducts}
                  disabled={loading}
                  variant="outline"
                  className="mt-4 px-6 py-2"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                      Loading more...
                    </>
                  ) : (
                    'Load More from Server'
                  )}
                </Button>
              )}
            </div>
          )}
        </div>
      </section>

      <Footer />

      {/* Cart Preview Toast */}
      {showCartPreview && (
        <div className="fixed bottom-4 right-4 z-50 bg-green-600 text-white p-4 rounded-lg shadow-lg transform transition-all duration-300 animate-in slide-in-from-bottom-2">
          <div className="flex items-center gap-3">
            <div className="bg-white/20 rounded-full p-2">
              <ShoppingCart className="h-5 w-5" />
            </div>
            <div>
              <p className="font-medium">Added to cart!</p>
              <p className="text-sm opacity-90">{cart.length} item{cart.length !== 1 ? 's' : ''} in cart</p>
            </div>
            <Link href="/cart">
              <Button variant="secondary" size="sm" className="ml-2">
                View Cart
              </Button>
            </Link>
          </div>
        </div>
      )}
    </div>
  )
}

export default function ShopPage() {
  return (
    <ClientRoot>
      <ShopPageContent />
    </ClientRoot>
  )
}
