import { MetadataRoute } from 'next';
import { SITE_URL } from '@/lib/config/seo';

// This is the main sitemap function for Next.js 13+ App Router
export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: SITE_URL,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
    },
    {
      url: `${SITE_URL}/vendors`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${SITE_URL}/venues`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${SITE_URL}/shop`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.8,
    },
    {
      url: `${SITE_URL}/blog`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.8,
    },
    {
      url: `${SITE_URL}/photos`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${SITE_URL}/real-weddings`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.7,
    },
    {
      url: `${SITE_URL}/planning`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.6,
    },
    {
      url: `${SITE_URL}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${SITE_URL}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${SITE_URL}/privacy`,
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 0.3,
    },
    {
      url: `${SITE_URL}/terms`,
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 0.3,
    },
  ];

  // Get dynamic content
  try {
    const [blogSitemap, vendorsSitemap, venuesSitemap, shopSitemap] = await Promise.all([
      generateBlogSitemap(),
      generateVendorsSitemap(),
      generateVenuesSitemap(),
      generateShopSitemap(),
    ]);

    return [
      ...staticPages,
      ...blogSitemap,
      ...vendorsSitemap,
      ...venuesSitemap,
      ...shopSitemap,
    ];
  } catch (error) {
    console.error('Error generating dynamic sitemap content:', error);
    return staticPages;
  }
}

// Generate dynamic sitemaps for different content types
export async function generateDynamicSitemap(type: 'blog' | 'vendors' | 'venues' | 'shop'): Promise<MetadataRoute.Sitemap> {
  try {
    switch (type) {
      case 'blog':
        return await generateBlogSitemap();
      case 'vendors':
        return await generateVendorsSitemap();
      case 'venues':
        return await generateVenuesSitemap();
      case 'shop':
        return await generateShopSitemap();
      default:
        return [];
    }
  } catch (error) {
    console.error(`Error generating ${type} sitemap:`, error);
    return [];
  }
}

async function generateBlogSitemap(): Promise<MetadataRoute.Sitemap> {
  // TODO: Replace with actual blog post fetching
  // const blogPosts = await BlogService.getAllPublishedPosts();
  
  // For now, return empty array - you can implement this when you have blog data
  const blogPosts: any[] = [];
  
  return blogPosts.map((post) => ({
    url: `${SITE_URL}/blog/${post.slug}`,
    lastModified: new Date(post.updatedAt || post.createdAt),
    changeFrequency: 'weekly' as const,
    priority: 0.7,
  }));
}

async function generateVendorsSitemap(): Promise<MetadataRoute.Sitemap> {
  // TODO: Replace with actual vendor fetching
  // const vendors = await VendorService.getAllActiveVendors();
  
  // For now, return empty array - you can implement this when you have vendor data
  const vendors: any[] = [];
  
  return vendors.map((vendor) => ({
    url: `${SITE_URL}/vendors/${vendor.slug}`,
    lastModified: new Date(vendor.updatedAt || vendor.createdAt),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }));
}

async function generateVenuesSitemap(): Promise<MetadataRoute.Sitemap> {
  // TODO: Replace with actual venue fetching
  // const venues = await VenueService.getAllActiveVenues();
  
  // For now, return empty array - you can implement this when you have venue data
  const venues: any[] = [];
  
  return venues.map((venue) => ({
    url: `${SITE_URL}/venues/${venue.slug}`,
    lastModified: new Date(venue.updatedAt || venue.createdAt),
    changeFrequency: 'weekly' as const,
    priority: 0.8,
  }));
}

async function generateShopSitemap(): Promise<MetadataRoute.Sitemap> {
  // TODO: Replace with actual shop product fetching
  // const products = await ShopService.getAllActiveProducts();
  
  // For now, return empty array - you can implement this when you have product data
  const products: any[] = [];
  
  return products.map((product) => ({
    url: `${SITE_URL}/shop/${product.slug}`,
    lastModified: new Date(product.updatedAt || product.createdAt),
    changeFrequency: 'weekly' as const,
    priority: 0.6,
  }));
}
