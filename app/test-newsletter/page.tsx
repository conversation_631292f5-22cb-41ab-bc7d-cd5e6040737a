'use client';

import React from 'react';
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { TopHeader } from "@/components/top-header";
import NewsletterSubscription from '@/components/newsletter/NewsletterSubscription';

export default function TestNewsletterPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold mb-4">Newsletter API Test</h1>
            <p className="text-gray-600">
              Test the newsletter subscription functionality with different variants
            </p>
          </div>

          {/* Compact Variant */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Compact Variant</h2>
            <NewsletterSubscription 
              source="TEST_PAGE"
              variant="compact"
            />
          </div>

          {/* Default Variant */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Default Variant</h2>
            <NewsletterSubscription 
              source="TEST_PAGE"
              variant="default"
            />
          </div>

          {/* Detailed Variant with Interests */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Detailed Variant with Interests</h2>
            <NewsletterSubscription 
              source="TEST_PAGE"
              variant="detailed"
              showInterests={true}
            />
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
