"use client"

import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import UnifiedLoginForm from "@/components/auth/UnifiedLoginForm"
import Link from "next/link"
import { ArrowLeft, Users, Shield, Heart } from "lucide-react"

export default function NewLoginPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-purple-50">
      <TopHeader />
      <Header />
      
      {/* Hero Section */}
      <div className="relative py-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            
            {/* Left Side - Branding & Features */}
            <div className="space-y-8">
              <div>
                <Link 
                  href="/" 
                  className="inline-flex items-center text-primary hover:text-primary/80 transition-colors mb-6"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Link>
                
                <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                  Welcome to
                  <span className="text-primary block">Thirumanam 360</span>
                </h1>
                
                <p className="text-xl text-gray-600 mb-8">
                  Your complete wedding planning platform. Connect with vendors, plan your dream wedding, and create memories that last a lifetime.
                </p>
              </div>

              {/* Features */}
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Trusted Vendors</h3>
                    <p className="text-gray-600">Connect with verified wedding vendors across India</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Shield className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Secure Platform</h3>
                    <p className="text-gray-600">Your data is protected with enterprise-grade security</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Heart className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Dream Wedding</h3>
                    <p className="text-gray-600">Plan every detail of your perfect wedding day</p>
                  </div>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-3 gap-6 pt-8 border-t border-gray-200">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">10K+</div>
                  <div className="text-sm text-gray-600">Happy Couples</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">5K+</div>
                  <div className="text-sm text-gray-600">Trusted Vendors</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">50+</div>
                  <div className="text-sm text-gray-600">Cities</div>
                </div>
              </div>
            </div>

            {/* Right Side - Login Form */}
            <div className="flex justify-center lg:justify-end">
              <div className="w-full max-w-md">
                <UnifiedLoginForm />
                
                {/* Additional Links */}
                <div className="mt-6 text-center space-y-4">
                  <div className="text-sm text-gray-600">
                    Don't have an account?{' '}
                    <Link href="/register" className="text-primary hover:text-primary/80 font-medium">
                      Sign up here
                    </Link>
                  </div>
                  
                  <div className="text-sm text-gray-600">
                    Are you a vendor?{' '}
                    <Link href="/vendor-login" className="text-primary hover:text-primary/80 font-medium">
                      Vendor Login
                    </Link>
                  </div>
                  
                  <div className="flex items-center justify-center space-x-4 text-xs text-gray-500">
                    <Link href="/privacy" className="hover:text-gray-700">Privacy Policy</Link>
                    <span>•</span>
                    <Link href="/terms" className="hover:text-gray-700">Terms of Service</Link>
                    <span>•</span>
                    <Link href="/contact" className="hover:text-gray-700">Support</Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Login Methods Info */}
      <div className="bg-white border-t border-gray-200 py-12">
        <div className="container mx-auto max-w-4xl px-4">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Multiple Ways to Login</h2>
            <p className="text-gray-600">Choose the method that works best for you</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 rounded-lg bg-blue-50 border border-blue-200">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Shield className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Email & Password</h3>
              <p className="text-sm text-gray-600">Traditional login with your email and password</p>
            </div>
            
            <div className="text-center p-6 rounded-lg bg-green-50 border border-green-200">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Email OTP</h3>
              <p className="text-sm text-gray-600">Quick login with OTP sent to your email</p>
            </div>
            
            <div className="text-center p-6 rounded-lg bg-purple-50 border border-purple-200">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Heart className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Mobile OTP</h3>
              <p className="text-sm text-gray-600">Instant login with OTP sent to your mobile</p>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
