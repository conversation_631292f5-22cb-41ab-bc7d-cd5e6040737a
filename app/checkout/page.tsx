"use client"
import { use<PERSON><PERSON>, ClientRoot } from "@/components/ClientRoot";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { TopHeader } from "@/components/top-header";
import { Header } from "@/components/header";
import { Footer } from "@/components/footer";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { CreditCard, Truck, Shield, ArrowLeft, MapPin, Phone, Mail, User, Package } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { shopService } from '@/lib/services/shopService';
import { OrderService, CreateOrderInput, OrderItemInput, ShippingAddressInput } from "@/lib/services/orderService";
import { PaymentService } from "@/lib/services/paymentService";
import { CartService, CartItemData } from "@/lib/services/cartService";
import { useAuth } from "@/contexts/AuthContext";
import { showToast } from "@/lib/toast";

function CheckoutPageContent() {
  const { cart, clearCart } = useCart();
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isProcessing, setIsProcessing] = useState(false);
  const [checkoutItems, setCheckoutItems] = useState<CartItemData[]>([]);
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});
  const [loadingCart, setLoadingCart] = useState(true);
  const [formData, setFormData] = useState({
    // Personal Information
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    
    // Shipping Address
    address: '',
    city: '',
    state: 'Tamil Nadu',
    pincode: '',
    
    // Payment Information
    paymentMethod: 'RAZORPAY',
    
    // Additional
    notes: ''
  });

  // Add Indian states array at the top of the component (inside CheckoutPageContent)
  const indianStates = [
    "Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "Meghalaya", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "Tripura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "Andaman and Nicobar Islands", "Chandigarh", "Dadra and Nagar Haveli and Daman and Diu", "Delhi", "Jammu and Kashmir", "Ladakh", "Lakshadweep", "Puducherry"
  ];

  // Initialize checkout items based on source (direct buy or database cart)
  useEffect(() => {
    async function loadCheckoutData() {
      try {
        setLoadingCart(true);

        // Check if this is a direct buy (Buy Now button)
        const productId = searchParams.get('productId');
        const quantity = Number(searchParams.get('quantity')) || 1;
        const size = searchParams.get('size');
        const color = searchParams.get('color');

        if (productId) {
          // Direct buy - fetch product data
          const product = await shopService.getShop(productId);
          if (product) {
            // Create a cart item structure that matches database cart
            const directBuyItem: CartItemData = {
              id: `direct-${productId}`,
              userId: user?.userId || '',
              productId: product.id,
              productName: product.name,
              productImage: product.images?.[0] || '/placeholder.svg',
              productPrice: Number(product.price),
              originalPrice: Number(product.originalPrice || product.price),
              discount: Number(product.discount || 0),
              quantity,
              selectedSize: size || undefined,
              selectedColor: color || undefined,
              productBrand: product.brand || '',
              productCategory: product.category || '',
              productDescription: product.description || '',
              status: 'ACTIVE',
              dateAdded: new Date().toISOString(),
              dateUpdated: new Date().toISOString()
            };
            setCheckoutItems([directBuyItem]);
            setLoadingCart(false);
            return;
          }
        }

        // Load from database cart
        if (isAuthenticated) {
          const cartResult = await CartService.getCartItems('ACTIVE');
          if (cartResult.success) {
            setCheckoutItems(cartResult.items);
          } else {
            setCheckoutItems([]);
          }
        } else {
          // Fallback to local storage cart for non-authenticated users
          setCheckoutItems(cart && cart.length > 0 ? cart.map(item => ({
            id: item.id.toString(),
            userId: '',
            productId: item.id.toString(),
            productName: item.name,
            productImage: item.image || '/placeholder.svg',
            productPrice: item.price,
            originalPrice: item.price,
            discount: 0,
            quantity: item.quantity,
            selectedSize: item.size,
            selectedColor: item.color,
            productBrand: '',
            productCategory: '',
            productDescription: '',
            status: 'ACTIVE',
            dateAdded: new Date().toISOString(),
            dateUpdated: new Date().toISOString()
          })) : []);
        }
      } catch (error) {
        console.error('Error loading checkout data:', error);
        setCheckoutItems([]);
      } finally {
        setLoadingCart(false);
      }
    }

    loadCheckoutData();
  }, [searchParams, isAuthenticated, user]);

  // Calculate actual order totals using database cart structure
  const subtotal = checkoutItems.reduce((sum, item) => sum + (item.productPrice * item.quantity), 0);
  const shippingCost = subtotal > 500 ? 0 : 50; // Free shipping above ₹500
  const tax = subtotal * 0.18; // 18% GST
  const discount = checkoutItems.reduce((sum, item) => sum + ((item.originalPrice - item.productPrice) * item.quantity), 0);
  const finalTotal = subtotal + shippingCost + tax - discount;

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Real-time validation function
  const validateField = (field: string, value: string): string | null => {
    switch (field) {
      case 'firstName':
        if (!value.trim()) return 'First name is required';
        if (value.trim().length < 2) return 'First name must be at least 2 characters';
        break;
      case 'lastName':
        if (!value.trim()) return 'Last name is required';
        if (value.trim().length < 2) return 'Last name must be at least 2 characters';
        break;
      case 'email':
        if (!value.trim()) return 'Email is required';
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value.trim())) return 'Please enter a valid email address';
        break;
      case 'phone':
        if (!value.trim()) return 'Phone number is required';
        const phoneRegex = /^[6-9]\d{9}$/;
        const cleanPhone = value.replace(/\D/g, '');
        if (!phoneRegex.test(cleanPhone)) return 'Please enter a valid 10-digit Indian mobile number';
        break;
      case 'address':
        if (!value.trim()) return 'Address is required';
        if (value.trim().length < 10) return 'Please enter a complete address (minimum 10 characters)';
        break;
      case 'city':
        if (!value.trim()) return 'City is required';
        if (value.trim().length < 2) return 'Please enter a valid city name';
        break;
      case 'state':
        if (!value.trim()) return 'State is required';
        break;
      case 'pincode':
        if (!value.trim()) return 'Pincode is required';
        const pincodeRegex = /^[1-9][0-9]{5}$/;
        if (!pincodeRegex.test(value.trim())) return 'Please enter a valid 6-digit pincode';
        break;
    }
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check authentication
    if (!isAuthenticated) {
      showToast.error('Please login to place an order');
      router.push('/login?redirect=/checkout');
      return;
    }

    // Comprehensive form validation
    const errors: {[key: string]: string} = {};
    const fieldsToValidate = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'state', 'pincode'];

    // Validate all fields
    fieldsToValidate.forEach(field => {
      const error = validateField(field, formData[field as keyof typeof formData] as string);
      if (error) {
        errors[field] = error;
      }
    });

    // Payment method validation
    if (!formData.paymentMethod) {
      errors.paymentMethod = 'Please select a payment method';
    }

    // If there are validation errors, show them and stop
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      const firstError = Object.values(errors)[0];
      showToast.error(firstError);
      // Scroll to top to show the error
      window.scrollTo({ top: 0, behavior: 'smooth' });
      return;
    }

    // Clear any existing validation errors
    setValidationErrors({});

    setIsProcessing(true);

    try {
      // Calculate totals with proper validation
      const orderSubtotal = Number(checkoutItems.reduce((sum, item) => sum + (Number(item.productPrice || 0) * Number(item.quantity || 0)), 0)) || 0;
      const orderShippingCost = Number(orderSubtotal > 500 ? 0 : 50) || 0; // Free shipping above ₹500
      const orderTax = Number(orderSubtotal * 0.18) || 0; // 18% GST
      const orderDiscount = Number(0) || 0;
      const orderTotal = Number(orderSubtotal + orderShippingCost + orderTax - orderDiscount) || 0;

      // Validate that we have valid numbers
      if (orderSubtotal <= 0) {
        throw new Error('Invalid order subtotal');
      }
      if (orderTotal <= 0) {
        throw new Error('Invalid order total');
      }

      // Prepare order items using database cart structure with strict validation
      const orderItems: OrderItemInput[] = checkoutItems.map(item => {
        const productPrice = Number(item.productPrice) || 0;
        const quantity = Number(item.quantity) || 0;
        const subtotal = Number(productPrice * quantity) || 0;

        // Ensure required fields are not zero or null
        if (productPrice <= 0) {
          throw new Error(`Invalid product price for item: ${item.productName}`);
        }
        if (quantity <= 0) {
          throw new Error(`Invalid quantity for item: ${item.productName}`);
        }

        return {
          productId: item.productId || '',
          productName: item.productName || '',
          productImage: item.productImage || null,
          productPrice: productPrice,
          originalPrice: Number(item.originalPrice) || productPrice,
          discount: Number(item.discount) || null,
          quantity: quantity,
          selectedSize: item.selectedSize || null,
          selectedColor: item.selectedColor || null,
          productBrand: item.productBrand || null,
          productCategory: item.productCategory || null,
          subtotal: subtotal
        };
      });

      // Prepare shipping address with validation
      const shippingAddress: ShippingAddressInput = {
        fullName: `${formData.firstName?.trim() || ''} ${formData.lastName?.trim() || ''}`.trim(),
        addressLine1: formData.address?.trim() || '',
        city: formData.city?.trim() || '',
        state: formData.state?.trim() || '',
        pincode: formData.pincode?.trim() || '',
        country: 'India',
        phone: formData.phone?.trim() || '',
        addressType: 'HOME'
      };

      // Create order with validated data
      const orderData: CreateOrderInput = {
        customerName: `${formData.firstName?.trim() || ''} ${formData.lastName?.trim() || ''}`.trim(),
        customerEmail: formData.email?.trim() || '',
        customerPhone: formData.phone?.trim() || '',
        shippingAddress,
        items: orderItems,
        subtotal: orderSubtotal,
        shippingCost: orderShippingCost,
        tax: orderTax,
        discount: orderDiscount,
        total: orderTotal,
        paymentMethod: formData.paymentMethod as any,
        specialInstructions: formData.notes?.trim() || '', // Use notes field
        isGift: false,
        estimatedDeliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 7 days from now
      };

      // Log order data for debugging
      console.log('Creating order with data:', {
        ...orderData,
        items: orderData.items.map(item => ({
          ...item,
          productPrice: typeof item.productPrice,
          quantity: typeof item.quantity,
          subtotal: typeof item.subtotal
        }))
      });

      const orderResult = await OrderService.createOrder(orderData);

      if (!orderResult.success) {
        throw new Error(orderResult.message);
      }

      const orderId = orderResult.order!.id;

      // Process payment based on method
      if (formData.paymentMethod === 'COD') {
        // Process COD order
        const codResult = await PaymentService.processCODOrder(orderId, orderTotal);

        if (codResult.success) {
          showToast.success('Order placed successfully! You will pay on delivery.');

          // Clear cart if checkout was from cart (not direct buy)
          if (!searchParams.get('productId')) {
            // Clear database cart
            await CartService.clearCart();
            // Also clear local storage cart for consistency
            clearCart();
          }

          router.push(`/order-confirmation?orderId=${orderId}`);
        } else {
          throw new Error(codResult.message);
        }
      } else {
        // Process Razorpay payment
        const paymentResult = await PaymentService.initiateRazorpayPayment(
          orderId,
          orderTotal,
          {
            name: `${formData.firstName} ${formData.lastName}`,
            email: formData.email,
            phone: formData.phone
          }
        );

        if (paymentResult.success) {
          showToast.success('Payment successful! Order confirmed.');

          // Clear cart if checkout was from cart (not direct buy)
          if (!searchParams.get('productId')) {
            // Clear database cart
            await CartService.clearCart();
            // Also clear local storage cart for consistency
            clearCart();
          }

          router.push(`/order-confirmation?orderId=${orderId}`);
        } else {
          throw new Error(paymentResult.message);
        }
      }

    } catch (error) {
      console.error('Checkout error:', error);
      showToast.error(error.message || 'Failed to place order. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  if (loadingCart) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#fffbe6] via-[#fff] to-[#f6c244]/20">
        <TopHeader />
        <Header />
        <div className="container mx-auto px-4 py-16 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#F6C244] mx-auto mb-4"></div>
          <h1 className="text-2xl font-bold mb-4">Loading your cart...</h1>
          <p className="text-gray-600">Please wait while we prepare your checkout.</p>
        </div>
        <Footer />
      </div>
    );
  }

  if (checkoutItems.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#fffbe6] via-[#fff] to-[#f6c244]/20">
        <TopHeader />
        <Header />
        <div className="container mx-auto px-4 py-16 text-center">
          <h1 className="text-2xl font-bold mb-4">No items to checkout</h1>
          <p className="text-gray-600 mb-6">Add some items to your cart or select a product to buy.</p>
          <Link href="/shop">
            <Button className="bg-primary hover:bg-primary/90">Continue Shopping</Button>
          </Link>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#fffbe6] via-[#fff] to-[#f6c244]/20 flex flex-col">
      <TopHeader />
      <Header />

      {/* Compact Header Section */}
      <div className="bg-white shadow-sm border-b mt-12 md:mt-12">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href={checkoutItems[0]?.id ? `/shop/${checkoutItems[0].id}` : '/shop'} className="inline-flex items-center text-sm text-gray-700 hover:underline">
              <ArrowLeft className="h-4 w-4 mr-1" /> Back to Shop
            </Link>
            <div className="text-center flex-1">
              <h1 className="text-2xl font-bold text-gray-900">Checkout</h1>
              <div className="flex items-center justify-center gap-2 text-xs text-gray-500 mt-1">
                <Shield className="h-3 w-3" />
                <span>Secure & Encrypted</span>
              </div>
            </div>
            <div className="w-20"></div> {/* Spacer for balance */}
          </div>
        </div>
      </div>

      <main className="flex-1 py-4 px-4">
        <div className="w-full max-w-7xl mx-auto">
          {/* Mobile Order Summary - Visible only on mobile */}
          <div className="lg:hidden mb-4">
            <div className="bg-white rounded-lg shadow-md border border-[#F6C244] p-4">
              <div className="flex justify-between items-center">
                <span className="font-semibold text-gray-900">Order Total</span>
                <span className="text-xl font-bold text-[#a31515]">₹{finalTotal.toLocaleString()}</span>
              </div>
              <div className="text-sm text-gray-600 mt-1">
                {checkoutItems.reduce((sum, item) => sum + item.quantity, 0)} items •
                {shippingCost === 0 ? ' Free shipping' : ` ₹${shippingCost} shipping`} •
                GST included
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
            {/* Form Section - Takes 3 columns */}
            <div className="lg:col-span-3 space-y-4">
            {/* Validation Error Summary */}
            {Object.keys(validationErrors).length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                    <ul className="mt-2 text-sm text-red-700 list-disc list-inside space-y-1">
                      {Object.values(validationErrors).map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            <form id="checkout-form" onSubmit={handleSubmit} className="space-y-6">
              {/* Personal Information */}
              <section className="bg-white rounded-2xl shadow-lg border-t-4 border-[#F6C244] p-6 space-y-4">
                <h2 className="text-xl font-bold mb-4 flex items-center gap-2 text-[#a31515]"><User className="w-6 h-6 text-[#F6C244]" /> Personal Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      value={formData.firstName}
                      onChange={e => handleInputChange('firstName', e.target.value)}
                      required
                      className={`mt-1 text-base h-12 focus:shadow-lg ${validationErrors.firstName ? 'border-red-500 focus:border-red-500' : ''}`}
                    />
                    {validationErrors.firstName && (
                      <p className="text-red-500 text-sm mt-1">{validationErrors.firstName}</p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="lastName">Last Name *</Label>
                    <Input
                      id="lastName"
                      value={formData.lastName}
                      onChange={e => handleInputChange('lastName', e.target.value)}
                      required
                      className={`mt-1 text-base h-12 focus:shadow-lg ${validationErrors.lastName ? 'border-red-500 focus:border-red-500' : ''}`}
                    />
                    {validationErrors.lastName && (
                      <p className="text-red-500 text-sm mt-1">{validationErrors.lastName}</p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={e => handleInputChange('email', e.target.value)}
                      required
                      className={`mt-1 text-base h-12 focus:shadow-lg ${validationErrors.email ? 'border-red-500 focus:border-red-500' : ''}`}
                    />
                    {validationErrors.email && (
                      <p className="text-red-500 text-sm mt-1">{validationErrors.email}</p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="phone">Phone Number *</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={e => handleInputChange('phone', e.target.value)}
                      required
                      placeholder="10-digit mobile number"
                      className={`mt-1 text-base h-12 focus:shadow-lg ${validationErrors.phone ? 'border-red-500 focus:border-red-500' : ''}`}
                    />
                    {validationErrors.phone && (
                      <p className="text-red-500 text-sm mt-1">{validationErrors.phone}</p>
                    )}
                  </div>
                </div>
              </section>
              {/* Shipping Address */}
              <section className="bg-white rounded-2xl shadow-lg border-t-4 border-[#F6C244] p-6 space-y-4">
                <h2 className="text-xl font-bold mb-4 flex items-center gap-2 text-[#a31515]"><MapPin className="w-6 h-6 text-[#F6C244]" /> Shipping Address</h2>
                <div className="mb-4">
                  <Label htmlFor="address">Address *</Label>
                  <Input
                    id="address"
                    value={formData.address}
                    onChange={e => handleInputChange('address', e.target.value)}
                    required
                    placeholder="Enter your full address (House/Flat No., Street, Area)"
                    className={`mt-1 text-base h-12 focus:shadow-lg ${validationErrors.address ? 'border-red-500 focus:border-red-500' : ''}`}
                  />
                  {validationErrors.address && (
                    <p className="text-red-500 text-sm mt-1">{validationErrors.address}</p>
                  )}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <Label htmlFor="city">City *</Label>
                    <Input
                      id="city"
                      value={formData.city}
                      onChange={e => handleInputChange('city', e.target.value)}
                      required
                      className={`mt-1 text-base h-12 focus:shadow-lg ${validationErrors.city ? 'border-red-500 focus:border-red-500' : ''}`}
                    />
                    {validationErrors.city && (
                      <p className="text-red-500 text-sm mt-1">{validationErrors.city}</p>
                    )}
                  </div>
                  <div className="relative">
                    <Label htmlFor="state">State *</Label>
                    <select
                      id="state"
                      value={formData.state}
                      onChange={e => handleInputChange('state', e.target.value)}
                      required
                      className={`flex h-12 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#F6C244] focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-base appearance-none pr-10 shadow-sm ${validationErrors.state ? 'border-red-500 focus:border-red-500' : ''}`}
                    >
                      {indianStates.map((state) => (
                        <option key={state} value={state}>{state}</option>
                      ))}
                    </select>
                    <span className="pointer-events-none absolute right-3 top-[60%] -translate-y-1/2 flex items-center text-[#F6C244]">
                      <svg className="h-5 w-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>
                    </span>
                    {validationErrors.state && (
                      <p className="text-red-500 text-sm mt-1">{validationErrors.state}</p>
                    )}
                  </div>
                  <div>
                    <Label htmlFor="pincode">Pincode *</Label>
                    <Input
                      id="pincode"
                      value={formData.pincode}
                      onChange={e => handleInputChange('pincode', e.target.value)}
                      required
                      placeholder="6-digit pincode"
                      maxLength={6}
                      className={`mt-1 text-base h-12 focus:shadow-lg ${validationErrors.pincode ? 'border-red-500 focus:border-red-500' : ''}`}
                    />
                    {validationErrors.pincode && (
                      <p className="text-red-500 text-sm mt-1">{validationErrors.pincode}</p>
                    )}
                  </div>
                </div>
                <div>
                  <Label htmlFor="landmark">Landmark</Label>
                  <Input id="landmark" value={formData.notes} onChange={e => handleInputChange('notes', e.target.value)} className="mt-1 text-base h-12 focus:shadow-lg" />
                </div>
              </section>
              {/* Payment Information */}
              <section className="bg-white rounded-2xl shadow-lg border-t-4 border-[#F6C244] p-6 space-y-4">
                <h2 className="text-xl font-bold mb-4 flex items-center gap-2 text-[#a31515]"><CreditCard className="w-6 h-6 text-[#F6C244]" /> Payment Information</h2>

                {/* Payment Method Selection */}
                <div className="space-y-4">
                  <Label className="text-base font-semibold">Choose Payment Method</Label>
                  {validationErrors.paymentMethod && (
                    <p className="text-red-500 text-sm">{validationErrors.paymentMethod}</p>
                  )}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Razorpay Option */}
                    <div
                      className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                        formData.paymentMethod === 'RAZORPAY'
                          ? 'border-[#F6C244] bg-[#F6C244]/10'
                          : 'border-gray-200 hover:border-[#F6C244]/50'
                      }`}
                      onClick={() => handleInputChange('paymentMethod', 'RAZORPAY')}
                    >
                      <div className="flex items-center space-x-3">
                        <input
                          type="radio"
                          name="paymentMethod"
                          value="RAZORPAY"
                          checked={formData.paymentMethod === 'RAZORPAY'}
                          onChange={() => handleInputChange('paymentMethod', 'RAZORPAY')}
                          className="text-[#F6C244] focus:ring-[#F6C244]"
                        />
                        <div>
                          <div className="font-semibold text-gray-900">Online Payment</div>
                          <div className="text-sm text-gray-600">Credit/Debit Card, UPI, Net Banking, Wallets</div>
                          <div className="text-xs text-green-600 font-medium">Secure & Instant</div>
                        </div>
                      </div>
                    </div>

                    {/* COD Option */}
                    <div
                      className={`border-2 rounded-lg p-4 cursor-pointer transition-all ${
                        formData.paymentMethod === 'COD'
                          ? 'border-[#F6C244] bg-[#F6C244]/10'
                          : 'border-gray-200 hover:border-[#F6C244]/50'
                      }`}
                      onClick={() => handleInputChange('paymentMethod', 'COD')}
                    >
                      <div className="flex items-center space-x-3">
                        <input
                          type="radio"
                          name="paymentMethod"
                          value="COD"
                          checked={formData.paymentMethod === 'COD'}
                          onChange={() => handleInputChange('paymentMethod', 'COD')}
                          className="text-[#F6C244] focus:ring-[#F6C244]"
                        />
                        <div>
                          <div className="font-semibold text-gray-900">Cash on Delivery</div>
                          <div className="text-sm text-gray-600">Pay when you receive your order</div>
                          <div className="text-xs text-blue-600 font-medium">No advance payment required</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Payment Method Info */}
                {formData.paymentMethod === 'RAZORPAY' && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <Shield className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div>
                        <div className="font-medium text-blue-900">Secure Online Payment</div>
                        <div className="text-sm text-blue-700 mt-1">
                          Your payment will be processed securely through Razorpay.
                          You can pay using Credit/Debit Cards, UPI, Net Banking, or Digital Wallets.
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {formData.paymentMethod === 'COD' && (
                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <Package className="h-5 w-5 text-amber-600 mt-0.5" />
                      <div>
                        <div className="font-medium text-amber-900">Cash on Delivery</div>
                        <div className="text-sm text-amber-700 mt-1">
                          You can pay in cash when your order is delivered to your doorstep.
                          Please keep the exact amount ready for a smooth delivery experience.
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </section>


            </form>
          </div>
          {/* Order Summary Card - Takes 2 columns */}
          <div className="lg:col-span-2 space-y-4 self-start lg:sticky lg:top-4">
            {/* Order Summary */}
            <section className="bg-white rounded-2xl shadow-lg border-t-4 border-[#F6C244] p-6 space-y-4">
              <h2 className="text-lg font-bold flex items-center gap-2 text-[#a31515]">
                <Package className="w-5 h-5 text-[#F6C244]" /> Order Summary
              </h2>

              {/* Order Items */}
              <div className="space-y-3">
                {checkoutItems.map((item, idx) => (
                  <div key={idx} className="flex items-center gap-3">
                    <Image
                      src={item.productImage || "/placeholder.svg"}
                      alt={item.productName}
                      width={40}
                      height={40}
                      className="rounded-md object-cover border"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm truncate">{item.productName}</div>
                      <div className="text-xs text-gray-500">
                        Qty: {item.quantity}
                        {item.selectedSize && ` • Size: ${item.selectedSize}`}
                        {item.selectedColor && ` • Color: ${item.selectedColor}`}
                      </div>
                      {item.originalPrice && item.originalPrice > item.productPrice && (
                        <div className="text-xs text-gray-400 line-through">₹{item.originalPrice.toLocaleString()}</div>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="font-semibold text-sm">₹{(item.productPrice * item.quantity).toLocaleString()}</div>
                      <div className="text-xs text-gray-500">₹{item.productPrice.toLocaleString()} each</div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="border-t border-[#F6C244]/30 pt-3 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal ({checkoutItems.reduce((sum, item) => sum + item.quantity, 0)} items)</span>
                  <span>₹{subtotal.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Shipping</span>
                  {shippingCost === 0 ? (
                    <span className="text-green-600 font-semibold">Free</span>
                  ) : (
                    <span>₹{shippingCost.toLocaleString()}</span>
                  )}
                </div>
                <div className="flex justify-between text-sm">
                  <span>Tax (GST 18%)</span>
                  <span>₹{tax.toLocaleString()}</span>
                </div>
                {discount > 0 && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Discount</span>
                    <span>-₹{discount.toLocaleString()}</span>
                  </div>
                )}
                <div className="border-t border-[#F6C244]/30 pt-2">
                  <div className="flex justify-between text-lg font-bold bg-[#fffbe6] rounded-lg px-3 py-2">
                    <span>Total</span>
                    <span>₹{finalTotal.toLocaleString()}</span>
                  </div>
                </div>

                {/* Free shipping indicator */}
                {subtotal < 500 && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-3">
                    <div className="text-blue-800 text-xs font-medium">
                      Add ₹{(500 - subtotal).toLocaleString()} more for FREE shipping!
                    </div>
                  </div>
                )}
              </div>
            </section>

            {/* Place Your Order Section */}
            <section className="bg-white rounded-2xl shadow-lg border-t-4 border-green-500 p-6 space-y-4">
              <h2 className="text-lg font-bold flex items-center gap-2 text-green-700">
                <Shield className="w-5 h-5 text-green-500" /> Place Your Order
              </h2>

              <div className="space-y-3">
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <div className="flex items-center gap-2 text-green-800 text-sm font-medium">
                    <Shield className="h-4 w-4" />
                    <span>Secure Checkout</span>
                  </div>
                  <p className="text-green-700 text-xs mt-1">
                    Your payment and personal information are protected with bank-level security.
                  </p>
                </div>

                <Button
                  type="submit"
                  form="checkout-form"
                  className="w-full bg-gradient-to-r from-[#F6C244] to-[#a31515] hover:from-[#F6C244]/90 hover:to-[#a31515]/90 text-white text-lg font-bold rounded-xl h-12 shadow-lg flex items-center justify-center gap-2 transition-all duration-300"
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <CreditCard className="h-5 w-5" />
                      Place Order - ₹{finalTotal.toLocaleString()}
                    </>
                  )}
                </Button>

                <div className="text-center">
                  <div className="flex items-center justify-center gap-2 text-gray-500 text-xs">
                    <Shield className="h-3 w-3" />
                    <span>256-bit SSL encryption</span>
                  </div>
                  <p className="text-gray-400 text-xs mt-1">
                    By placing your order, you agree to our Terms & Conditions
                  </p>
                </div>
              </div>
            </section>
          </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}

export default function CheckoutPage() {
  return (
    <ClientRoot>
      <CheckoutPageContent />
    </ClientRoot>
  );
}
