import { NextRequest, NextResponse } from 'next/server';

// Email service configuration
// You can use AWS SES, SendGrid, Nodemailer, or any other email service
// This is a template that you can customize based on your email provider

interface EmailRequest {
  to: string;
  subject: string;
  html: string;
  text: string;
  type: 'newsletter_welcome' | 'user_signup_welcome' | 'user_login_welcome';
}

export async function POST(request: NextRequest) {
  try {
    const body: EmailRequest = await request.json();
    
    // Validate required fields
    if (!body.to || !body.subject || !body.html) {
      return NextResponse.json(
        { error: 'Missing required fields: to, subject, html' },
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.to)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Log the email request (for development)
    console.log('Email Request:', {
      to: body.to,
      subject: body.subject,
      type: body.type,
      timestamp: new Date().toISOString()
    });

    // Option 1: AWS SES Implementation
    const sesResult = await sendWithAWSSES(body);
    if (sesResult.success) {
      return NextResponse.json({ 
        success: true, 
        messageId: sesResult.messageId,
        provider: 'AWS SES'
      });
    }

    // Option 2: SendGrid Implementation (fallback)
    const sendGridResult = await sendWithSendGrid(body);
    if (sendGridResult.success) {
      return NextResponse.json({ 
        success: true, 
        messageId: sendGridResult.messageId,
        provider: 'SendGrid'
      });
    }

    // Option 3: Nodemailer Implementation (fallback)
    const nodemailerResult = await sendWithNodemailer(body);
    if (nodemailerResult.success) {
      return NextResponse.json({ 
        success: true, 
        messageId: nodemailerResult.messageId,
        provider: 'Nodemailer'
      });
    }

    // If all methods fail
    throw new Error('All email providers failed');

  } catch (error: any) {
    console.error('Email sending error:', error);
    return NextResponse.json(
      { error: 'Failed to send email', details: error.message },
      { status: 500 }
    );
  }
}

// AWS SES Implementation
async function sendWithAWSSES(emailData: EmailRequest) {
  try {
    // Check if AWS SES is configured
    if (!process.env.AWS_REGION || !process.env.AWS_ACCESS_KEY_ID || !process.env.AWS_SECRET_ACCESS_KEY) {
      console.log('AWS SES not configured, skipping...');
      return { success: false };
    }

    // Import AWS SDK (install with: npm install @aws-sdk/client-ses)
    const { SESClient, SendEmailCommand } = require('@aws-sdk/client-ses');
    
    const sesClient = new SESClient({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
    });

    const params = {
      Source: process.env.FROM_EMAIL || '<EMAIL>',
      Destination: {
        ToAddresses: [emailData.to],
      },
      Message: {
        Subject: {
          Data: emailData.subject,
          Charset: 'UTF-8',
        },
        Body: {
          Html: {
            Data: emailData.html,
            Charset: 'UTF-8',
          },
          Text: {
            Data: emailData.text,
            Charset: 'UTF-8',
          },
        },
      },
    };

    const command = new SendEmailCommand(params);
    const result = await sesClient.send(command);
    
    console.log('AWS SES email sent successfully:', result.MessageId);
    return { success: true, messageId: result.MessageId };
    
  } catch (error) {
    console.error('AWS SES error:', error);
    return { success: false, error: error.message };
  }
}

// SendGrid Implementation
async function sendWithSendGrid(emailData: EmailRequest) {
  try {
    // Check if SendGrid is configured
    if (!process.env.SENDGRID_API_KEY) {
      console.log('SendGrid not configured, skipping...');
      return { success: false };
    }

    // Import SendGrid (install with: npm install @sendgrid/mail)
    const sgMail = require('@sendgrid/mail');
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);

    const msg = {
      to: emailData.to,
      from: process.env.FROM_EMAIL || '<EMAIL>',
      subject: emailData.subject,
      text: emailData.text,
      html: emailData.html,
    };

    const result = await sgMail.send(msg);
    
    console.log('SendGrid email sent successfully');
    return { success: true, messageId: result[0].headers['x-message-id'] };
    
  } catch (error) {
    console.error('SendGrid error:', error);
    return { success: false, error: error.message };
  }
}

// Nodemailer Implementation (for development/testing)
async function sendWithNodemailer(emailData: EmailRequest) {
  try {
    // Check if SMTP is configured
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.SMTP_PASS) {
      console.log('SMTP not configured, using console log for development...');
      
      // For development - just log the email
      console.log('=== EMAIL WOULD BE SENT ===');
      console.log('To:', emailData.to);
      console.log('Subject:', emailData.subject);
      console.log('Type:', emailData.type);
      console.log('HTML Length:', emailData.html.length);
      console.log('Text Length:', emailData.text.length);
      console.log('==============================');
      
      return { success: true, messageId: 'dev-' + Date.now() };
    }

    // Import Nodemailer (install with: npm install nodemailer)
    const nodemailer = require('nodemailer');

    const transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });

    const mailOptions = {
      from: process.env.FROM_EMAIL || '<EMAIL>',
      to: emailData.to,
      subject: emailData.subject,
      text: emailData.text,
      html: emailData.html,
    };

    const result = await transporter.sendMail(mailOptions);
    
    console.log('Nodemailer email sent successfully:', result.messageId);
    return { success: true, messageId: result.messageId };
    
  } catch (error) {
    console.error('Nodemailer error:', error);
    return { success: false, error: error.message };
  }
}
