import { NextRequest, NextResponse } from 'next/server'
import { generateClient } from '@aws-amplify/api'
import { createBooking, updateBooking } from '@/src/graphql/mutations'
import { listBookings, getBooking } from '@/src/graphql/queries'
import { getCurrentUser } from 'aws-amplify/auth'
import { Amplify } from 'aws-amplify'
import awsExports from '@/src/aws-exports'

// Configure Amplify for server-side operations
if (!Amplify.getConfig().Auth) {
  Amplify.configure(awsExports)
}

const client = generateClient()

// Create a new booking
export async function POST(request: NextRequest) {
  try {
    // Get current user with better error handling
    let user
    try {
      user = await getCurrentUser()
    } catch (authError) {
      console.error('Authentication error:', authError)
      return NextResponse.json(
        {
          error: 'Authentication required',
          details: 'Please log in to create a booking',
          authError: authError.message
        },
        { status: 401 }
      )
    }

    if (!user) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      )
    }

    const body = await request.json()
    
    // Validate required fields
    const requiredFields = [
      'entityId', 'entityType', 'entityName', 'eventDate', 
      'eventTime', 'guestCount', 'eventType', 'contactPreference'
    ]
    
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Get user profile for customer details
    const userAttributes = user.signInDetails?.loginId || user.username
    
    // Determine vendor ID based on entity type
    let vendorId = null
    if (body.entityType === 'VENDOR') {
      vendorId = body.entityId
    } else if (body.entityType === 'VENUE') {
      // For venues, we might need to get the vendor ID from the venue data
      // This depends on your venue data structure
      vendorId = body.vendorId || null
    }

    // Create booking input
    const bookingInput = {
      customerId: user.userId,
      customerName: body.customerName || `${user.signInDetails?.loginId}`,
      customerEmail: userAttributes,
      customerPhone: body.customerPhone || '',
      entityId: body.entityId,
      entityType: body.entityType,
      entityName: body.entityName,
      vendorId: vendorId,
      eventDate: body.eventDate,
      eventTime: body.eventTime,
      guestCount: parseInt(body.guestCount),
      eventType: body.eventType,
      duration: body.duration || '',
      specialRequests: body.specialRequests || '',
      budget: body.budget || '',
      contactPreference: body.contactPreference.toUpperCase(),
      status: 'PENDING',
      priority: 'MEDIUM',
      notes: '',
      vendorNotes: '',
      paymentStatus: 'PENDING',
      contractSigned: false,
      reminderSent: false,
      communicationLog: [],
      attachments: [],
      metadata: JSON.stringify({
        source: 'web_booking_form',
        userAgent: request.headers.get('user-agent'),
        timestamp: new Date().toISOString()
      })
    }

    // Create booking in database
    const result = await client.graphql({
      query: createBooking,
      variables: { input: bookingInput }
    })

    const booking = result.data.createBooking

    // Send notification emails (optional)
    try {
      await sendBookingNotifications(booking)
    } catch (emailError) {
      console.error('Failed to send booking notifications:', emailError)
      // Don't fail the booking creation if email fails
    }

    return NextResponse.json({
      success: true,
      booking: booking,
      message: 'Booking created successfully'
    })

  } catch (error) {
    console.error('Booking creation error:', error)
    return NextResponse.json(
      { error: 'Failed to create booking', details: error.message },
      { status: 500 }
    )
  }
}

// Get bookings (with filters)
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const customerId = searchParams.get('customerId')
    const vendorId = searchParams.get('vendorId')
    const status = searchParams.get('status')
    const entityType = searchParams.get('entityType')
    const limit = parseInt(searchParams.get('limit') || '20')

    // Build filter based on user role and parameters
    let filter: any = {}

    if (customerId) {
      filter.customerId = { eq: customerId }
    } else if (vendorId) {
      filter.vendorId = { eq: vendorId }
    } else {
      // Default to user's own bookings
      filter.customerId = { eq: user.userId }
    }

    if (status) {
      filter.status = { eq: status.toUpperCase() }
    }

    if (entityType) {
      filter.entityType = { eq: entityType.toUpperCase() }
    }

    const result = await client.graphql({
      query: listBookings,
      variables: {
        filter: filter,
        limit: limit,
        sortDirection: 'DESC'
      }
    })

    return NextResponse.json({
      success: true,
      bookings: result.data.listBookings.items,
      nextToken: result.data.listBookings.nextToken
    })

  } catch (error) {
    console.error('Get bookings error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch bookings', details: error.message },
      { status: 500 }
    )
  }
}

// Update booking status
export async function PATCH(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { bookingId, status, vendorNotes, estimatedCost, notes } = body

    if (!bookingId) {
      return NextResponse.json(
        { error: 'Booking ID is required' },
        { status: 400 }
      )
    }

    // Get existing booking to verify permissions
    const existingBooking = await client.graphql({
      query: getBooking,
      variables: { id: bookingId }
    })

    const booking = existingBooking.data.getBooking
    if (!booking) {
      return NextResponse.json(
        { error: 'Booking not found' },
        { status: 404 }
      )
    }

    // Check permissions (customer can only update their own bookings, vendors can update bookings for their services)
    const isCustomer = booking.customerId === user.userId
    const isVendor = booking.vendorId === user.userId
    
    if (!isCustomer && !isVendor) {
      return NextResponse.json(
        { error: 'Unauthorized to update this booking' },
        { status: 403 }
      )
    }

    // Prepare update input
    const updateInput: any = {
      id: bookingId
    }

    if (status) {
      updateInput.status = status.toUpperCase()
    }

    if (vendorNotes && isVendor) {
      updateInput.vendorNotes = vendorNotes
    }

    if (estimatedCost && isVendor) {
      updateInput.estimatedCost = estimatedCost
    }

    if (notes) {
      updateInput.notes = notes
    }

    // Add communication log entry
    const communicationEntry = {
      timestamp: new Date().toISOString(),
      type: 'SYSTEM',
      from: user.userId,
      to: isCustomer ? booking.vendorId : booking.customerId,
      message: `Booking status updated to ${status || 'updated'}`,
      status: 'SENT'
    }

    updateInput.communicationLog = [
      ...(booking.communicationLog || []),
      communicationEntry
    ]

    // Update booking
    const result = await client.graphql({
      query: updateBooking,
      variables: { input: updateInput }
    })

    // Send notification about status change
    try {
      await sendStatusUpdateNotification(result.data.updateBooking)
    } catch (emailError) {
      console.error('Failed to send status update notification:', emailError)
    }

    return NextResponse.json({
      success: true,
      booking: result.data.updateBooking,
      message: 'Booking updated successfully'
    })

  } catch (error) {
    console.error('Booking update error:', error)
    return NextResponse.json(
      { error: 'Failed to update booking', details: error.message },
      { status: 500 }
    )
  }
}

// Helper function to send booking notifications
async function sendBookingNotifications(booking: any) {
  // Send email to customer
  const customerEmailData = {
    to: booking.customerEmail,
    subject: `Booking Confirmation - ${booking.entityName}`,
    html: generateCustomerBookingEmail(booking),
    text: `Your booking request for ${booking.entityName} has been submitted successfully.`,
    type: 'booking_confirmation'
  }

  // Send email to vendor (if vendor email is available)
  // You might need to fetch vendor details to get their email
  
  // Call email API
  await fetch('/api/send-email', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(customerEmailData)
  })
}

// Helper function to send status update notifications
async function sendStatusUpdateNotification(booking: any) {
  const emailData = {
    to: booking.customerEmail,
    subject: `Booking Update - ${booking.entityName}`,
    html: generateStatusUpdateEmail(booking),
    text: `Your booking status has been updated to: ${booking.status}`,
    type: 'booking_status_update'
  }

  await fetch('/api/send-email', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(emailData)
  })
}

// Helper function to generate customer booking email
function generateCustomerBookingEmail(booking: any) {
  return `
    <h2>Booking Confirmation</h2>
    <p>Dear ${booking.customerName},</p>
    <p>Your booking request has been submitted successfully!</p>
    
    <h3>Booking Details:</h3>
    <ul>
      <li><strong>Service:</strong> ${booking.entityName}</li>
      <li><strong>Event Date:</strong> ${booking.eventDate}</li>
      <li><strong>Event Time:</strong> ${booking.eventTime}</li>
      <li><strong>Guest Count:</strong> ${booking.guestCount}</li>
      <li><strong>Event Type:</strong> ${booking.eventType}</li>
      <li><strong>Status:</strong> ${booking.status}</li>
    </ul>
    
    <p>The vendor will contact you within 24 hours to confirm availability and discuss details.</p>
    
    <p>Thank you for choosing Thirumanam 360!</p>
  `
}

// Helper function to generate status update email
function generateStatusUpdateEmail(booking: any) {
  return `
    <h2>Booking Status Update</h2>
    <p>Dear ${booking.customerName},</p>
    <p>Your booking status has been updated.</p>
    
    <h3>Updated Details:</h3>
    <ul>
      <li><strong>Service:</strong> ${booking.entityName}</li>
      <li><strong>Status:</strong> ${booking.status}</li>
      <li><strong>Event Date:</strong> ${booking.eventDate}</li>
    </ul>
    
    ${booking.vendorNotes ? `<p><strong>Vendor Notes:</strong> ${booking.vendorNotes}</p>` : ''}
    
    <p>Thank you for choosing Thirumanam 360!</p>
  `
}
