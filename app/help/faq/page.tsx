"use client"

import { <PERSON>Header } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import FAQ from "@/components/FAQ"
import { 
  ArrowLeft,
  MessageCircle,
  Phone,
  Mail,
  HelpCircle
} from "lucide-react"
import Link from "next/link"

export default function FAQPage() {
  return (
    <>
      <SimpleSEO
        title="Frequently Asked Questions - Thirumanam 360 Help"
        description="Find answers to common questions about Thirumanam 360. Get help with account setup, finding vendors, booking venues, wedding planning tools, and more."
        keywords="FAQ, frequently asked questions, thirumanam 360 help, wedding planning FAQ, vendor booking help"
        url="/help/faq"
      />
      
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />
        
        {/* Breadcrumb */}
        <div className="bg-muted/30 py-4">
          <div className="container mx-auto px-4">
            <div className="flex items-center gap-2 text-sm">
              <Link href="/help" className="text-muted-foreground hover:text-foreground">
                Help Center
              </Link>
              <span className="text-muted-foreground">/</span>
              <span className="text-foreground">FAQ</span>
            </div>
          </div>
        </div>

        {/* Header */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <Link href="/help" className="inline-flex items-center gap-2 text-primary hover:underline mb-6">
              <ArrowLeft className="w-4 h-4" />
              Back to Help Center
            </Link>
            
            <div className="text-center mb-12">
              <HelpCircle className="w-16 h-16 mx-auto mb-6 text-primary" />
              <h1 className="text-4xl font-bold mb-4">Frequently Asked Questions</h1>
              <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
                Find quick answers to the most common questions about using Thirumanam 360. 
                Can't find what you're looking for? Our support team is here to help.
              </p>
            </div>
          </div>
        </section>

        {/* FAQ Component */}
        <section className="py-8">
          <div className="container mx-auto px-4">
            <FAQ 
              showSearch={true}
              showCategories={true}
            />
          </div>
        </section>

        {/* Still Need Help */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-6">Still have questions?</h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Our support team is ready to help you with any questions not covered in our FAQ.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              {/* Live Chat */}
              <div className="bg-background p-6 rounded-lg shadow-sm">
                <MessageCircle className="w-12 h-12 mx-auto mb-4 text-primary" />
                <h3 className="font-semibold mb-2">Live Chat</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Get instant help from our support team
                </p>
                <Button className="w-full">
                  Start Chat
                </Button>
              </div>

              {/* Phone Support */}
              <div className="bg-background p-6 rounded-lg shadow-sm">
                <Phone className="w-12 h-12 mx-auto mb-4 text-primary" />
                <h3 className="font-semibold mb-2">Phone Support</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Call us Monday to Saturday, 9 AM to 8 PM
                </p>
                <Button variant="outline" className="w-full">
                  +91 8148376909
                </Button>
              </div>

              {/* Email Support */}
              <div className="bg-background p-6 rounded-lg shadow-sm">
                <Mail className="w-12 h-12 mx-auto mb-4 text-primary" />
                <h3 className="font-semibold mb-2">Email Support</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Send us an email and we'll respond within 24 hours
                </p>
                <Link href="/contact">
                  <Button variant="outline" className="w-full">
                    Send Email
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
