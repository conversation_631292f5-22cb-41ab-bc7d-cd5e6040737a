"use client"

import { <PERSON>Header } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { 
  ArrowLeft,
  CheckCircle,
  User,
  UserPlus,
  Settings,
  Heart,
  Search,
  MessageCircle,
  Star,
  Calendar,
  ShoppingBag,
  MapPin,
  Users
} from "lucide-react"
import Link from "next/link"

export default function GettingStartedPage() {
  const steps = [
    {
      icon: UserPlus,
      title: "1. Create Your Account",
      description: "Sign up with your email or phone number",
      details: [
        "Visit the signup page and choose your account type",
        "For couples: Select 'Customer' to plan your wedding",
        "For businesses: Select 'Vendor' to offer services",
        "Verify your email or phone number",
        "Complete your profile with basic information"
      ],
      tips: [
        "Use a valid email address for important notifications",
        "Choose a strong password for account security",
        "Add a profile photo to build trust with vendors"
      ]
    },
    {
      icon: User,
      title: "2. Complete Your Profile",
      description: "Add details to help vendors understand your needs",
      details: [
        "Add your wedding date and location",
        "Specify your budget range",
        "List your service requirements",
        "Upload photos if you're a vendor",
        "Add contact information and preferences"
      ],
      tips: [
        "Complete profiles get 3x more responses",
        "Be specific about your requirements",
        "Keep your information updated"
      ]
    },
    {
      icon: Search,
      title: "3. Explore the Platform",
      description: "Discover vendors, venues, and wedding services",
      details: [
        "Browse vendors by category and location",
        "Use filters to narrow down your search",
        "Read reviews and ratings from other couples",
        "Save favorites for easy access later",
        "Compare different options side by side"
      ],
      tips: [
        "Use specific location filters for better results",
        "Read recent reviews for current service quality",
        "Save multiple options before making decisions"
      ]
    },
    {
      icon: MessageCircle,
      title: "4. Connect with Vendors",
      description: "Reach out to vendors and get quotes",
      details: [
        "Send inquiries through the platform",
        "Use the quick inquiry form for faster responses",
        "Ask specific questions about services and pricing",
        "Schedule meetings or site visits",
        "Compare quotes and packages"
      ],
      tips: [
        "Be clear about your requirements and budget",
        "Ask for detailed quotes in writing",
        "Schedule meetings during business hours"
      ]
    },
    {
      icon: Calendar,
      title: "5. Plan Your Wedding",
      description: "Use our planning tools to organize everything",
      details: [
        "Create your wedding checklist",
        "Set up a budget tracker",
        "Manage your guest list",
        "Track vendor bookings and payments",
        "Set reminders for important tasks"
      ],
      tips: [
        "Start planning 6-12 months in advance",
        "Book popular vendors early",
        "Keep track of all contracts and payments"
      ]
    }
  ]

  const userTypes = [
    {
      icon: Heart,
      title: "Couples",
      description: "Planning your dream wedding",
      features: [
        "Search and connect with vendors",
        "Book venues and services",
        "Use wedding planning tools",
        "Read and write reviews",
        "Manage your wedding budget",
        "Create guest lists and checklists"
      ]
    },
    {
      icon: Users,
      title: "Vendors",
      description: "Wedding service providers",
      features: [
        "Create detailed business profiles",
        "Showcase your portfolio",
        "Receive and manage inquiries",
        "Track bookings and revenue",
        "Build customer relationships",
        "Get reviews and ratings"
      ]
    },
    {
      icon: MapPin,
      title: "Venues",
      description: "Wedding venue owners",
      features: [
        "List your venue with photos",
        "Manage availability calendar",
        "Set pricing and packages",
        "Handle booking requests",
        "Showcase past events",
        "Connect with couples directly"
      ]
    }
  ]

  return (
    <>
      <SimpleSEO
        title="Getting Started Guide - Thirumanam 360 Help"
        description="Learn how to get started with Thirumanam 360. Step-by-step guide for couples and vendors to create accounts, complete profiles, and start planning weddings."
        keywords="getting started, thirumanam 360 guide, wedding planning guide, vendor registration"
        url="/help/getting-started"
      />
      
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />
        
        {/* Breadcrumb */}
        <div className="bg-muted/30 py-4">
          <div className="container mx-auto px-4">
            <div className="flex items-center gap-2 text-sm">
              <Link href="/help" className="text-muted-foreground hover:text-foreground">
                Help Center
              </Link>
              <span className="text-muted-foreground">/</span>
              <span className="text-foreground">Getting Started</span>
            </div>
          </div>
        </div>

        {/* Header */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <Link href="/help" className="inline-flex items-center gap-2 text-primary hover:underline mb-6">
              <ArrowLeft className="w-4 h-4" />
              Back to Help Center
            </Link>
            
            <h1 className="text-4xl font-bold mb-4">Getting Started with Thirumanam 360</h1>
            <p className="text-xl text-muted-foreground max-w-3xl">
              Welcome to Thirumanam 360! This guide will help you get started with planning your dream wedding 
              or growing your wedding business on our platform.
            </p>
          </div>
        </section>

        {/* User Types */}
        <section className="py-12 bg-muted/30">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Choose Your Path</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {userTypes.map((type, index) => (
                <Card key={index} className="text-center">
                  <CardHeader>
                    <type.icon className="w-12 h-12 mx-auto mb-4 text-primary" />
                    <CardTitle>{type.title}</CardTitle>
                    <p className="text-muted-foreground">{type.description}</p>
                  </CardHeader>
                  <CardContent>
                    <ul className="text-left space-y-2">
                      {type.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start gap-2 text-sm">
                          <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Step-by-Step Guide */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Step-by-Step Guide</h2>
            <div className="max-w-4xl mx-auto space-y-8">
              {steps.map((step, index) => (
                <Card key={index} className="overflow-hidden">
                  <CardHeader className="bg-muted/50">
                    <div className="flex items-start gap-4">
                      <div className="bg-primary text-primary-foreground p-3 rounded-lg">
                        <step.icon className="w-6 h-6" />
                      </div>
                      <div className="flex-1">
                        <CardTitle className="text-xl mb-2">{step.title}</CardTitle>
                        <p className="text-muted-foreground">{step.description}</p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div>
                        <h4 className="font-semibold mb-3">What to do:</h4>
                        <ul className="space-y-2">
                          {step.details.map((detail, idx) => (
                            <li key={idx} className="flex items-start gap-2 text-sm">
                              <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                              {detail}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-3">Pro Tips:</h4>
                        <div className="space-y-2">
                          {step.tips.map((tip, idx) => (
                            <Badge key={idx} variant="secondary" className="block text-left p-2 h-auto">
                              💡 {tip}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Quick Actions */}
        <section className="py-16 bg-muted/30">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-8">Ready to Get Started?</h2>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/signup">
                <Button size="lg" className="px-8">
                  <UserPlus className="w-5 h-5 mr-2" />
                  Create Account
                </Button>
              </Link>
              <Link href="/vendors">
                <Button variant="outline" size="lg" className="px-8">
                  <Search className="w-5 h-5 mr-2" />
                  Browse Vendors
                </Button>
              </Link>
              <Link href="/help">
                <Button variant="outline" size="lg" className="px-8">
                  <MessageCircle className="w-5 h-5 mr-2" />
                  Get Help
                </Button>
              </Link>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
