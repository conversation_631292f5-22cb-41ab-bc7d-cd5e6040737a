"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/contexts/AuthContext"
import { profileService } from "@/lib/services/profileService"
import { showToast } from "@/lib/toast"

export default function DebugVendorStatusPage() {
  const { user, userProfile, userType, isVendor, isAdmin, refreshUserProfile } = useAuth()
  const [loading, setLoading] = useState(false)

  const handleSetVendorStatus = async () => {
    if (!user?.userId) {
      showToast.error('No user found')
      return
    }

    try {
      setLoading(true)
      
      // Update the user profile to set vendor status
      const updateResult = await profileService.updateProfile(user.userId, {
        isVendor: true,
        role: 'vendor',
        businessInfo: {
          businessName: 'Test Vendor Business',
          businessType: 'vendor'
        }
      })

      if (updateResult.success) {
        showToast.success('Vendor status updated successfully')
        // Refresh the user profile to get updated data
        await refreshUserProfile()
      } else {
        showToast.error('Failed to update vendor status')
      }
    } catch (error) {
      console.error('Error updating vendor status:', error)
      showToast.error('Error updating vendor status')
    } finally {
      setLoading(false)
    }
  }

  const handleSetAdminStatus = async () => {
    if (!user?.userId) {
      showToast.error('No user found')
      return
    }

    try {
      setLoading(true)
      
      // Update the user profile to set admin status
      const updateResult = await profileService.updateProfile(user.userId, {
        isAdmin: true,
        role: 'admin'
      })

      if (updateResult.success) {
        showToast.success('Admin status updated successfully')
        // Refresh the user profile to get updated data
        await refreshUserProfile()
      } else {
        showToast.error('Failed to update admin status')
      }
    } catch (error) {
      console.error('Error updating admin status:', error)
      showToast.error('Error updating admin status')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Debug Vendor Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Current Auth State */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Current Authentication State</h3>
              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">User ID:</span>
                    <span className="ml-2">{user?.userId || 'Not available'}</span>
                  </div>
                  <div>
                    <span className="font-medium">Email:</span>
                    <span className="ml-2">{user?.signInDetails?.loginId || 'Not available'}</span>
                  </div>
                  <div>
                    <span className="font-medium">User Type:</span>
                    <span className="ml-2">{userType || 'Not set'}</span>
                  </div>
                  <div>
                    <span className="font-medium">Is Vendor:</span>
                    <span className={`ml-2 ${isVendor ? 'text-green-600' : 'text-red-600'}`}>
                      {isVendor ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Is Admin:</span>
                    <span className={`ml-2 ${isAdmin ? 'text-green-600' : 'text-red-600'}`}>
                      {isAdmin ? 'Yes' : 'No'}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* User Profile Data */}
            <div>
              <h3 className="text-lg font-semibold mb-3">User Profile Data</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(userProfile, null, 2)}
                </pre>
              </div>
            </div>

            {/* Actions */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Actions</h3>
              <div className="flex gap-4">
                <Button 
                  onClick={handleSetVendorStatus}
                  disabled={loading}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {loading ? 'Updating...' : 'Set as Vendor'}
                </Button>
                
                <Button 
                  onClick={handleSetAdminStatus}
                  disabled={loading}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {loading ? 'Updating...' : 'Set as Admin'}
                </Button>

                <Button 
                  onClick={refreshUserProfile}
                  variant="outline"
                >
                  Refresh Profile
                </Button>
              </div>
            </div>

            {/* Instructions */}
            <div>
              <h3 className="text-lg font-semibold mb-3">Instructions</h3>
              <div className="bg-blue-50 rounded-lg p-4 text-sm">
                <p className="mb-2">
                  <strong>If you're seeing "Access denied. Vendor account required":</strong>
                </p>
                <ol className="list-decimal list-inside space-y-1">
                  <li>Check the "Current Authentication State" above</li>
                  <li>If "Is Vendor" shows "No", click "Set as Vendor" button</li>
                  <li>Wait for the success message</li>
                  <li>Navigate back to the vendor orders page</li>
                  <li>The access should now work</li>
                </ol>
                <p className="mt-3 text-gray-600">
                  This page is for debugging purposes only and should be removed in production.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
