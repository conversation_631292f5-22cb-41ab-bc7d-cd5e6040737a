'use client'

import { LayoutWrapper } from "@/components/layout-wrapper"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MapPin, Globe, Camera, Sparkles, Crown } from "lucide-react"
import { useStateContext, INDIAN_STATES } from '@/contexts/StateContext'
import { useSafeTranslation } from '@/hooks/use-safe-translation'

export default function MultilingualStateVendorDemoPage() {
  const { selectedState, setSelectedState } = useStateContext()
  const { t, i18n } = useSafeTranslation()

  const switchLanguage = (lng: string) => {
    i18n.changeLanguage(lng)
  }

  const languages = [
    { code: 'en', name: 'English', native: 'English' },
    { code: 'ta', name: 'Tamil', native: 'தமிழ்' },
    { code: 'hi', name: 'Hindi', native: 'हिंदी' },
    { code: 'kn', name: 'Kannada', native: 'ಕನ್ನಡ' },
    { code: 'ml', name: 'Malayalam', native: 'മലയാളം' },
    { code: 'te', name: 'Telugu', native: 'తెలుగు' },
    { code: 'gu', name: 'Gujarati', native: 'ગુજરાતી' },
    { code: 'mr', name: 'Marathi', native: 'मराठी' },
    { code: 'bn', name: 'Bengali', native: 'বাংলা' }
  ]

  const stateSpecificServices = [
    {
      state: 'TN',
      stateName: 'Tamil Nadu',
      services: [
        { key: 'header.stateSpecific.tamilNadu.photography.traditionalTamilWedding', fallback: 'Traditional Tamil Wedding Photography', icon: '📸' },
        { key: 'header.stateSpecific.tamilNadu.photography.templePhotography', fallback: 'Temple Photography', icon: '🏛️' },
        { key: 'header.stateSpecific.tamilNadu.makeup.traditionalTamilBridal', fallback: 'Traditional Tamil Bridal Makeup', icon: '💄' },
        { key: 'header.stateSpecific.tamilNadu.traditionalWear.kanchipuramSilk', fallback: 'Kanchipuram Silk Sarees', icon: '👗' },
        { key: 'header.stateSpecific.tamilNadu.music.nadaswaramArtists', fallback: 'Nadaswaram Artists', icon: '🎵' },
        { key: 'header.stateSpecific.tamilNadu.catering.bananaLeafMeals', fallback: 'Banana Leaf Meals', icon: '🍽️' }
      ]
    },
    {
      state: 'KA',
      stateName: 'Karnataka',
      services: [
        { key: 'header.stateSpecific.karnataka.photography.mysorePalaceStyle', fallback: 'Mysore Palace Style Photography', icon: '📸' },
        { key: 'header.stateSpecific.karnataka.makeup.mysoreTraditionalBridal', fallback: 'Mysore Traditional Bridal Look', icon: '💄' },
        { key: 'header.stateSpecific.karnataka.traditionalWear.mysoreSilk', fallback: 'Mysore Silk', icon: '👗' },
        { key: 'header.stateSpecific.karnataka.music.yakshagana', fallback: 'Yakshagana Performances', icon: '🎭' }
      ]
    },
    {
      state: 'KL',
      stateName: 'Kerala',
      services: [
        { key: 'header.stateSpecific.kerala.photography.backwaterWedding', fallback: 'Backwater Wedding Photography', icon: '📸' },
        { key: 'header.stateSpecific.kerala.makeup.keralaTraditionalBridal', fallback: 'Kerala Traditional Bridal Look', icon: '💄' },
        { key: 'header.stateSpecific.kerala.traditionalWear.kasavuSarees', fallback: 'Kasavu Sarees', icon: '👗' }
      ]
    }
  ]

  return (
    <LayoutWrapper>
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              🌐 Multi-Language State-Specific Vendor System
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              Experience how vendor services dynamically change based on both selected state and language, 
              showcasing culturally relevant translations and regional specialties.
            </p>
          </div>

          {/* Language & State Selectors */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="w-5 h-5" />
                  Language Selection
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-3 gap-2">
                  {languages.map((language) => (
                    <Button
                      key={language.code}
                      variant={i18n.language === language.code ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => switchLanguage(language.code)}
                      className="text-xs"
                    >
                      {language.native}
                    </Button>
                  ))}
                </div>
                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Current:</strong> {languages.find(l => l.code === i18n.language)?.native} ({languages.find(l => l.code === i18n.language)?.name})
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="w-5 h-5" />
                  State Selection
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-2">
                  {INDIAN_STATES.slice(0, 6).map((state) => (
                    <Button
                      key={state.code}
                      variant={selectedState.code === state.code ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setSelectedState(state)}
                      className="text-xs"
                    >
                      {state.name}
                    </Button>
                  ))}
                </div>
                <div className="mt-4 p-3 bg-green-50 rounded-lg">
                  <p className="text-sm text-green-800">
                    <strong>Current:</strong> {selectedState.name} | Language: {selectedState.language}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* State Indicator Translation */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>State Indicator Translation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg">
                <div className="flex items-center justify-center">
                  <MapPin className="w-5 h-5 mr-2 text-blue-600" />
                  <span className="text-lg font-semibold text-blue-900">
                    {t('header.stateSpecific.indicator', 'Showing {stateName} specialties & cultural services', { stateName: selectedState.name })}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* State-Specific Services */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {stateSpecificServices.map((stateData) => (
              <Card key={stateData.state} className={selectedState.code === stateData.state ? 'ring-2 ring-blue-500' : ''}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    {stateData.stateName}
                    {selectedState.code === stateData.state && (
                      <Badge variant="default">Current</Badge>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {stateData.services.map((service, index) => (
                      <div key={index} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50">
                        <span className="text-xl">{service.icon}</span>
                        <div className="flex-1">
                          <div className="font-medium text-sm">
                            {t(service.key, service.fallback)}
                          </div>
                          <div className="text-xs text-gray-500">
                            Key: {service.key}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Implementation Summary */}
          <div className="mt-12">
            <Card>
              <CardHeader>
                <CardTitle className="text-center">🎯 Implementation Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">12</div>
                    <div className="text-sm text-gray-600">Languages Supported</div>
                    <div className="text-xs text-gray-500 mt-1">
                      English, Tamil, Hindi, Kannada, Malayalam, Telugu, Gujarati, Marathi, Bengali, Punjabi, Urdu, Odia
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-600 mb-2">6+</div>
                    <div className="text-sm text-gray-600">States Configured</div>
                    <div className="text-xs text-gray-500 mt-1">
                      Tamil Nadu, Karnataka, Kerala, Andhra Pradesh, Maharashtra, Gujarat
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600 mb-2">100+</div>
                    <div className="text-sm text-gray-600">Cultural Services</div>
                    <div className="text-xs text-gray-500 mt-1">
                      State-specific traditional services and cultural specialties
                    </div>
                  </div>
                </div>

                <div className="mt-8 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg">
                  <h3 className="text-lg font-semibold text-green-900 mb-4">✨ Key Features Implemented:</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <h4 className="font-semibold text-green-800 mb-2">🌐 Multi-Language Support:</h4>
                      <ul className="space-y-1 text-green-700">
                        <li>• Dynamic language switching</li>
                        <li>• Cultural context preservation</li>
                        <li>• Native script rendering</li>
                        <li>• Fallback support</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-semibold text-green-800 mb-2">📍 State-Specific Services:</h4>
                      <ul className="space-y-1 text-green-700">
                        <li>• Regional specialties</li>
                        <li>• Cultural traditions</li>
                        <li>• Traditional wear</li>
                        <li>• Local customs</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </LayoutWrapper>
  )
}
