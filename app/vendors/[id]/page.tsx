"use client"

import { <PERSON>Header } from "@/components/top-header";
import { <PERSON><PERSON> } from "@/components/header";
import { Footer } from "@/components/footer";
import { EntityReviews } from "@/components/entity-reviews";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Star, MapPin, Heart, Share2, CheckCircle, Award, Users, Clock, Phone, Mail, Globe, Facebook, Instagram, Youtube, Camera, Calendar, MessageCircle } from "@/lib/icon-imports";
import { generateClient } from '@aws-amplify/api';
import { getVendor } from '@/src/graphql/queries';

import '@/lib/amplify-singleton'; // Auto-configures Amplify
import QuickInquiryForm from '@/components/QuickInquiryForm';
import AuthenticatedContactVendor from '@/components/AuthenticatedContactVendor';
import FavoriteButton from '@/components/FavoriteButton';

const client = generateClient();

export default async function VendorDetailsPage({ params }: { params: { id: string } }) {
  const { id } = params;
  let vendor = null;
  try {
    const result: any = await client.graphql({ query: getVendor, variables: { id } });
    vendor = result.data.getVendor;
  } catch (e) {
    vendor = null;
  }
  if (!vendor) {
    return <div style={{ padding: 40 }}>Vendor not found.</div>;
  }
  return (
    <div className="min-h-screen bg-white">
      <TopHeader />
      <Header />

      {/* Hero Section with Image Gallery */}
      <section className="relative">
        <div className="h-96 bg-gradient-to-r from-primary/20 to-primary/10 relative overflow-hidden">
          {vendor.profilePhoto ? (
            <img
              src={vendor.profilePhoto}
              alt={vendor.name}
              className="absolute inset-0 w-full h-full object-cover"
            />
          ) : (
            <div className="flex items-center justify-center h-full bg-gradient-to-br from-primary/20 to-primary/10">
              <Camera className="h-24 w-24 text-primary/40" />
            </div>
          )}
          <div className="absolute inset-0 bg-black/30"></div>

          {/* Floating Action Buttons */}
          {/* Removed Heart and Share2 action buttons */}
        </div>

        {/* Vendor Info Overlay */}
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-8">
          <div className="container mx-auto">
            <div className="flex items-end justify-between text-white">
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <h1 className="text-4xl font-bold">{vendor.name}</h1>
                  {/* Removed Verified and Featured badges */}
                </div>
                <p className="text-xl text-white/90 mb-2">{vendor.category}</p>
                <div className="flex items-center text-white/80">
                  <MapPin className="h-5 w-5 mr-2" />
                  <span className="text-lg">{vendor.city}, {vendor.state}</span>
                </div>
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-white mb-1">
                  {vendor.priceRange || "Contact for pricing"}
                </div>
                <div className="flex items-center gap-2">
                  <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  <span className="text-lg font-semibold">{vendor.rating || "N/A"}</span>
                  <span className="text-white/80">({vendor.reviewCount || 0} reviews)</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* About Section - Moved to Top */}
              <Card className="mb-6">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                    <div className="flex-1">
                      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between lg:mb-4">
                        <h3 className="text-xl font-semibold mb-4 lg:mb-0">About {vendor.name}</h3>

                        {/* Like, Share & Social Media Icons */}
                        <div className="flex gap-3 justify-center lg:justify-end">
                          {/* Like and Share Icons */}
                          <FavoriteButton
                            entityId={vendor.id}
                            entityType="VENDOR"
                            entityData={{
                              name: vendor.name,
                              image: vendor.images?.[0],
                              price: vendor.priceRange,
                              location: vendor.location,
                              city: vendor.city,
                              state: vendor.state,
                              rating: vendor.rating,
                              reviewCount: vendor.reviewCount,
                              description: vendor.description
                            }}
                            variant="outline"
                            size="icon"
                            className="rounded-full"
                          />
                          <Button size="icon" variant="outline" className="rounded-full hover:bg-primary/10 hover:text-primary hover:border-primary transition-all duration-200">
                            <Share2 className="h-4 w-4" />
                          </Button>

                          {/* Social Media Icons */}
                          {vendor.socialMedia && (
                            <>
                              {vendor.socialMedia.facebook && (
                                <Button
                                  size="icon"
                                  variant="outline"
                                  className="rounded-full hover:bg-blue-600 hover:text-white hover:border-blue-600 transition-all duration-200"
                                  onClick={() => window.open(vendor.socialMedia.facebook, '_blank')}
                                >
                                  <Facebook className="h-4 w-4" />
                                </Button>
                              )}
                              {vendor.socialMedia.instagram && (
                                <Button
                                  size="icon"
                                  variant="outline"
                                  className="rounded-full hover:bg-pink-600 hover:text-white hover:border-pink-600 transition-all duration-200"
                                  onClick={() => window.open(vendor.socialMedia.instagram, '_blank')}
                                >
                                  <Instagram className="h-4 w-4" />
                                </Button>
                              )}
                              {vendor.socialMedia.youtube && (
                                <Button
                                  size="icon"
                                  variant="outline"
                                  className="rounded-full hover:bg-red-600 hover:text-white hover:border-red-600 transition-all duration-200"
                                  onClick={() => window.open(vendor.socialMedia.youtube, '_blank')}
                                >
                                  <Youtube className="h-4 w-4" />
                                </Button>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                      <p className="text-gray-700 leading-relaxed">
                        {vendor.description || "No description available."}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-4 mb-8">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="services">Services</TabsTrigger>
                  <TabsTrigger value="gallery">Gallery</TabsTrigger>
                  <TabsTrigger value="reviews">Reviews</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-6">
                  {/* Quick Stats */}
                  <Card>
                    <CardContent className="p-6">
                      <h3 className="text-xl font-semibold mb-4">Quick Stats</h3>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                        <div className="text-center p-4 bg-yellow-50 rounded-xl">
                          <Star className="h-8 w-8 fill-yellow-400 text-yellow-400 mx-auto mb-2" />
                          <div className="font-bold text-xl text-gray-900">{vendor.rating || 'N/A'}</div>
                          <p className="text-sm text-gray-600">Rating</p>
                        </div>
                        <div className="text-center p-4 bg-blue-50 rounded-xl">
                          <Award className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                          <div className="font-bold text-xl text-gray-900">{vendor.experience || 'N/A'}</div>
                          <p className="text-sm text-gray-600">Experience</p>
                        </div>
                        <div className="text-center p-4 bg-green-50 rounded-xl">
                          <Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
                          <div className="font-bold text-xl text-gray-900">{vendor.events || 'N/A'}</div>
                          <p className="text-sm text-gray-600">Events</p>
                        </div>
                        <div className="text-center p-4 bg-purple-50 rounded-xl">
                          <Clock className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                          <div className="font-bold text-xl text-gray-900">{vendor.responseTime || 'N/A'}</div>
                          <p className="text-sm text-gray-600">Response</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Specializations */}
                  {vendor.specializations && vendor.specializations.length > 0 && (
                    <Card>
                      <CardContent className="p-6">
                        <h3 className="text-xl font-semibold mb-4">Specializations</h3>
                        <div className="flex flex-wrap gap-2">
                          {vendor.specializations.map((spec: string, idx: number) => (
                            <Badge key={idx} variant="secondary" className="px-3 py-1">
                              {spec}
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {/* Languages */}
                  {vendor.languages && vendor.languages.length > 0 && (
                    <Card>
                      <CardContent className="p-6">
                        <h3 className="text-xl font-semibold mb-4">Languages Spoken</h3>
                        <div className="flex flex-wrap gap-2">
                          {vendor.languages.map((lang: string, idx: number) => (
                            <Badge key={idx} variant="outline" className="px-3 py-1">
                              {lang}
                            </Badge>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                <TabsContent value="services" className="space-y-6">
                  <Card>
                    <CardContent className="p-6">
                      <h3 className="text-xl font-semibold mb-6">Services & Pricing</h3>
                      {vendor.services && vendor.services.length > 0 ? (
                        <div className="space-y-4">
                          {vendor.services.map((service: any, idx: number) => (
                            <div key={idx} className="border rounded-xl p-4 hover:shadow-md transition-shadow">
                              <div className="flex justify-between items-start">
                                <div>
                                  <h4 className="font-semibold text-lg text-gray-900">{service.name}</h4>
                                  {service.description && (
                                    <p className="text-gray-600 mt-1">{service.description}</p>
                                  )}
                                </div>
                                <div className="text-right">
                                  <div className="font-bold text-xl text-primary">{service.price}</div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500">No services listed. Contact vendor for details.</p>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="gallery" className="space-y-6">
                  <Card>
                    <CardContent className="p-6">
                      <h3 className="text-xl font-semibold mb-6">Photo Gallery</h3>
                      {vendor.gallery && vendor.gallery.length > 0 ? (
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                          {vendor.gallery.map((url: string, idx: number) => (
                            <div key={idx} className="relative aspect-square rounded-xl overflow-hidden group cursor-pointer">
                              <img
                                src={url}
                                alt={`Gallery ${idx + 1}`}
                                className="absolute inset-0 w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                              />
                              <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300"></div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-12">
                          <Camera className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                          <p className="text-gray-500">No photos available</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="reviews" className="space-y-6">
                  <EntityReviews
                    entityType="VENDOR"
                    entityId={vendor.id}
                    entityName={vendor.name}
                  />
                </TabsContent>
              </Tabs>
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1">
              <div className="sticky top-8 space-y-6">
                {/* Authenticated Contact Vendor Component */}
                <AuthenticatedContactVendor
                  vendor={{
                    id: vendor.id,
                    userId: vendor.userId,
                    name: vendor.name,
                    category: vendor.category,
                    contact: vendor.contact,
                    email: vendor.email,
                    website: vendor.website,
                    rating: vendor.rating,
                    reviewCount: vendor.reviewCount,
                    priceRange: vendor.priceRange
                  }}
                  showInquiryForm={true}
                />

                {/* Location */}
                <Card>
                  <CardContent className="p-6">
                    <h3 className="text-lg font-bold mb-4">Location</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-start">
                        <MapPin className="h-4 w-4 text-gray-500 mr-2 mt-0.5" />
                        <div>
                          {vendor.location && <p>{vendor.location}</p>}
                          {vendor.city && vendor.state && <p>{vendor.city}, {vendor.state}</p>}
                          {vendor.pincode && <p>{vendor.pincode}</p>}
                          {vendor.fullAddress && <p className="mt-1">{vendor.fullAddress}</p>}
                          {!vendor.location && !vendor.city && !vendor.state && (
                            <p className="text-gray-500 italic">Location information not available</p>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="mt-4 h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                      <span className="text-gray-500">Map View</span>
                    </div>
                  </CardContent>
                </Card>

              </div>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  );
} 