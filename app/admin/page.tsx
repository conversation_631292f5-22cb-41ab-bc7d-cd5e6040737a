'use client';

import React from 'react';
import { TopHeader } from '@/components/top-header';
import { Header } from '@/components/header';
import NewsletterDashboardWidget from '@/components/admin/NewsletterDashboardWidget';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MessageSquare,
  Users,
  ShoppingBag,
  MapPin,
  UserCheck,
  BarChart3,
  Settings,
  Shield,
  ExternalLink,
  Mail
} from 'lucide-react';

export default function AdminDashboard() {
  const dashboardSections = [
    {
      title: 'Reviews Management',
      description: 'Moderate platform and product reviews',
      icon: MessageSquare,
      href: '/dashboard/admin-reviews',
      badge: 'New Reviews',
      color: 'bg-blue-500'
    },
    {
      title: 'Vendor Reviews',
      description: 'Manage vendor-specific reviews',
      icon: UserCheck,
      href: '/dashboard/vendor-reviews',
      badge: 'Vendor Portal',
      color: 'bg-green-500'
    },
    {
      title: 'User Management',
      description: 'Manage user accounts and permissions',
      icon: Users,
      href: '/dashboard/admin-users',
      badge: 'Admin Users',
      color: 'bg-purple-500'
    },
    {
      title: 'Newsletter Management',
      description: 'Manage newsletter subscriptions and campaigns',
      icon: Mail,
      href: '/dashboard/admin-newsletter',
      badge: 'Email Marketing',
      color: 'bg-indigo-500'
    },
    {
      title: 'Shop Management',
      description: 'Manage shop listings and products',
      icon: ShoppingBag,
      href: '/dashboard/admin-shops',
      badge: 'Active',
      color: 'bg-orange-500'
    },
    {
      title: 'Venue Management',
      description: 'Manage venue listings and bookings',
      icon: MapPin,
      href: '/dashboard/admin-venues',
      badge: 'Active',
      color: 'bg-pink-500'
    },
    {
      title: 'Analytics',
      description: 'View platform analytics and reports',
      icon: BarChart3,
      href: '/dashboard/analytics',
      badge: 'Reports',
      color: 'bg-indigo-500'
    }
  ];

  const quickActions = [
    {
      title: 'Test Review System',
      description: 'Test the review functionality',
      href: '/test-entity-reviews',
      icon: MessageSquare
    },
    {
      title: 'Review Test Suite',
      description: 'Run automated review tests',
      href: '/test-reviews',
      icon: Shield
    },
    {
      title: 'Platform Reviews',
      description: 'View public reviews page',
      href: '/reviews',
      icon: ExternalLink
    },
    {
      title: 'Create My Admin Profile',
      description: 'Create your admin profile (works with auth)',
      href: '/create-my-admin-profile',
      icon: Shield
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Admin Dashboard</h1>
          <p className="text-gray-600 text-lg">
            Manage your wedding platform with comprehensive admin tools
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Reviews</p>
                  <p className="text-2xl font-bold text-gray-900">--</p>
                </div>
                <MessageSquare className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Reviews</p>
                  <p className="text-2xl font-bold text-gray-900">--</p>
                </div>
                <Shield className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Vendors</p>
                  <p className="text-2xl font-bold text-gray-900">--</p>
                </div>
                <Users className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Platform Rating</p>
                  <p className="text-2xl font-bold text-gray-900">--</p>
                </div>
                <BarChart3 className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Newsletter Overview Widget */}
        <div className="mb-8">
          <NewsletterDashboardWidget />
        </div>

        {/* Main Dashboard Sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {dashboardSections.map((section, index) => {
            const IconComponent = section.icon;
            return (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className={`p-2 rounded-lg ${section.color}`}>
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                    <Badge variant="secondary">{section.badge}</Badge>
                  </div>
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{section.description}</p>
                  <Button asChild className="w-full">
                    <a href={section.href}>
                      Access Dashboard
                      <ExternalLink className="ml-2 h-4 w-4" />
                    </a>
                  </Button>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <p className="text-gray-600">Frequently used admin tools and testing utilities</p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {quickActions.map((action, index) => {
                const IconComponent = action.icon;
                return (
                  <Button
                    key={index}
                    variant="outline"
                    className="h-auto p-4 flex flex-col items-start space-y-2"
                    asChild
                  >
                    <a href={action.href}>
                      <div className="flex items-center space-x-2">
                        <IconComponent className="h-5 w-5" />
                        <span className="font-medium">{action.title}</span>
                      </div>
                      <p className="text-sm text-gray-600 text-left">{action.description}</p>
                    </a>
                  </Button>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* System Status */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>System Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <span className="text-green-800 font-medium">Review System</span>
                <Badge className="bg-green-100 text-green-800">Operational</Badge>
              </div>
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <span className="text-blue-800 font-medium">GraphQL API</span>
                <Badge className="bg-blue-100 text-blue-800">Connected</Badge>
              </div>
              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <span className="text-yellow-800 font-medium">Backend Deployment</span>
                <Badge className="bg-yellow-100 text-yellow-800">Partial</Badge>
              </div>
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <span className="text-green-800 font-medium">Authentication</span>
                <Badge className="bg-green-100 text-green-800">Active</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
