'use client';

import React, { useState, useEffect } from 'react';
import { TopHeader } from '@/components/top-header';
import { Header } from '@/components/header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Shield, 
  UserPlus, 
  UserMinus, 
  Crown, 
  User,
  Mail,
  Phone,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import AdminUserService from '@/lib/services/adminUserService';
import { useAuth } from '@/contexts/AuthContext';

interface AdminUser {
  id: string;
  userId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  role: string;
  permissions: string[];
  createdAt: string;
}

export default function AdminUsersPage() {
  const { user, isAuthenticated } = useAuth();
  const [adminUsers, setAdminUsers] = useState<AdminUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCurrentUserAdmin, setIsCurrentUserAdmin] = useState(false);
  
  // Form state for making user admin
  const [showMakeAdminForm, setShowMakeAdminForm] = useState(false);
  const [makeAdminForm, setMakeAdminForm] = useState({
    userId: '',
    adminLevel: 'ADMIN' as 'ADMIN' | 'SUPER_ADMIN'
  });
  const [actionLoading, setActionLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    if (isAuthenticated) {
      checkAdminStatus();
      loadAdminUsers();
    }
  }, [isAuthenticated]);

  const checkAdminStatus = async () => {
    try {
      const isAdmin = await AdminUserService.isCurrentUserAdmin();
      setIsCurrentUserAdmin(isAdmin);
    } catch (error) {
      console.error('Error checking admin status:', error);
    }
  };

  const loadAdminUsers = async () => {
    try {
      setLoading(true);
      const result = await AdminUserService.listAdminUsers();
      
      if (result.success) {
        setAdminUsers(result.data || []);
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to load admin users' });
      }
    } catch (error) {
      console.error('Error loading admin users:', error);
      setMessage({ type: 'error', text: 'Failed to load admin users' });
    } finally {
      setLoading(false);
    }
  };

  const handleMakeAdmin = async () => {
    if (!makeAdminForm.userId.trim()) {
      setMessage({ type: 'error', text: 'Please enter a user ID' });
      return;
    }

    try {
      setActionLoading(true);
      const result = await AdminUserService.makeUserAdmin(
        makeAdminForm.userId.trim(),
        makeAdminForm.adminLevel
      );

      if (result.success) {
        setMessage({ type: 'success', text: 'User successfully made admin' });
        setMakeAdminForm({ userId: '', adminLevel: 'ADMIN' });
        setShowMakeAdminForm(false);
        loadAdminUsers(); // Refresh the list
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to make user admin' });
      }
    } catch (error) {
      console.error('Error making user admin:', error);
      setMessage({ type: 'error', text: 'Failed to make user admin' });
    } finally {
      setActionLoading(false);
    }
  };

  const handleRemoveAdmin = async (userId: string) => {
    if (!confirm('Are you sure you want to remove admin privileges from this user?')) {
      return;
    }

    try {
      setActionLoading(true);
      const result = await AdminUserService.removeAdminPrivileges(userId);

      if (result.success) {
        setMessage({ type: 'success', text: 'Admin privileges removed successfully' });
        loadAdminUsers(); // Refresh the list
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to remove admin privileges' });
      }
    } catch (error) {
      console.error('Error removing admin privileges:', error);
      setMessage({ type: 'error', text: 'Failed to remove admin privileges' });
    } finally {
      setActionLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Authentication Required</h2>
            <p className="text-gray-600">Please log in to access admin user management.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!isCurrentUserAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <XCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-gray-600">You don't have admin privileges to access this page.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Admin User Management</h1>
            <p className="text-gray-600 mt-2">Manage admin users and permissions</p>
          </div>
          <Button 
            onClick={() => setShowMakeAdminForm(!showMakeAdminForm)}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Make User Admin
          </Button>
        </div>

        {/* Message */}
        {message && (
          <div className={`mb-6 p-4 rounded-lg ${
            message.type === 'success' 
              ? 'bg-green-50 border border-green-200 text-green-800' 
              : 'bg-red-50 border border-red-200 text-red-800'
          }`}>
            <div className="flex items-center">
              {message.type === 'success' ? (
                <CheckCircle className="h-5 w-5 mr-2" />
              ) : (
                <AlertCircle className="h-5 w-5 mr-2" />
              )}
              {message.text}
            </div>
          </div>
        )}

        {/* Make Admin Form */}
        {showMakeAdminForm && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Make User Admin</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="userId">User ID</Label>
                <Input
                  id="userId"
                  value={makeAdminForm.userId}
                  onChange={(e) => setMakeAdminForm(prev => ({ ...prev, userId: e.target.value }))}
                  placeholder="Enter user ID (from Cognito or user profile)"
                />
              </div>
              
              <div>
                <Label htmlFor="adminLevel">Admin Level</Label>
                <Select 
                  value={makeAdminForm.adminLevel} 
                  onValueChange={(value: 'ADMIN' | 'SUPER_ADMIN') => 
                    setMakeAdminForm(prev => ({ ...prev, adminLevel: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ADMIN">Admin</SelectItem>
                    <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex space-x-2">
                <Button 
                  onClick={handleMakeAdmin}
                  disabled={actionLoading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {actionLoading ? 'Processing...' : 'Make Admin'}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setShowMakeAdminForm(false)}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Admin Users List */}
        <Card>
          <CardHeader>
            <CardTitle>Current Admin Users</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading admin users...</p>
              </div>
            ) : adminUsers.length === 0 ? (
              <div className="text-center py-8">
                <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No admin users found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {adminUsers.map((adminUser) => (
                  <div key={adminUser.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className={`p-2 rounded-full ${
                          adminUser.isSuperAdmin ? 'bg-purple-100' : 'bg-blue-100'
                        }`}>
                          {adminUser.isSuperAdmin ? (
                            <Crown className="h-5 w-5 text-purple-600" />
                          ) : (
                            <Shield className="h-5 w-5 text-blue-600" />
                          )}
                        </div>
                        
                        <div>
                          <h3 className="font-semibold text-lg">
                            {adminUser.firstName} {adminUser.lastName}
                          </h3>
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <div className="flex items-center">
                              <Mail className="h-4 w-4 mr-1" />
                              {adminUser.email}
                            </div>
                            {adminUser.phone && (
                              <div className="flex items-center">
                                <Phone className="h-4 w-4 mr-1" />
                                {adminUser.phone}
                              </div>
                            )}
                          </div>
                          <div className="flex items-center space-x-2 mt-2">
                            <Badge variant={adminUser.isSuperAdmin ? "default" : "secondary"}>
                              {adminUser.role}
                            </Badge>
                            <span className="text-xs text-gray-500">
                              User ID: {adminUser.userId}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRemoveAdmin(adminUser.userId)}
                          disabled={actionLoading}
                          className="text-red-600 hover:text-red-700"
                        >
                          <UserMinus className="h-4 w-4 mr-1" />
                          Remove Admin
                        </Button>
                      </div>
                    </div>
                    
                    {adminUser.permissions && adminUser.permissions.length > 0 && (
                      <div className="mt-3 pt-3 border-t">
                        <p className="text-sm font-medium text-gray-700 mb-2">Permissions:</p>
                        <div className="flex flex-wrap gap-1">
                          {adminUser.permissions.map((permission, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {permission}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>How to Make a User Admin</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <p><strong>Step 1:</strong> Get the user's User ID from AWS Cognito or their user profile</p>
              <p><strong>Step 2:</strong> Click "Make User Admin" button above</p>
              <p><strong>Step 3:</strong> Enter the User ID and select admin level</p>
              <p><strong>Step 4:</strong> Click "Make Admin" to grant privileges</p>
              
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-blue-800"><strong>Note:</strong> The user must have a UserProfile record in the database before they can be made admin. Users get profiles when they first log in or register.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
