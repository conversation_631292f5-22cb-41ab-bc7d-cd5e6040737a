'use client'

import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { CreateTestBlogs } from "@/components/CreateTestBlogs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Info, Database, Users, FileText } from "lucide-react"

export default function CreateTestBlogsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-12">
        {/* Page Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-4">Create Test Blogs</h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            This tool creates 10 sample blog posts across different categories to populate your blog system for testing purposes.
          </p>
        </div>

        {/* Information Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <FileText className="w-5 h-5 text-blue-600" />
                Blog Categories
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• Wedding Planning</li>
                <li>• Venue Selection</li>
                <li>• Fashion & Style</li>
                <li>• Photography</li>
                <li>• Catering & Food</li>
                <li>• Decorations</li>
                <li>• Budget & Finance</li>
                <li>• Real Weddings</li>
                <li>• Expert Tips</li>
                <li>• Vendor Spotlight</li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Users className="w-5 h-5 text-green-600" />
                Author Types
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• <strong>Vendors:</strong> Wedding service providers</li>
                <li>• <strong>Experts:</strong> Wedding planning professionals</li>
                <li>• <strong>Admin:</strong> Editorial team content</li>
              </ul>
              <p className="text-xs text-gray-500 mt-3">
                Each blog will be assigned an appropriate author type based on the content.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Database className="w-5 h-5 text-purple-600" />
                Database Impact
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="text-sm space-y-1 text-gray-600">
                <li>• 10 new blog entries</li>
                <li>• Published status</li>
                <li>• Featured & pinned flags</li>
                <li>• Tags and categories</li>
                <li>• Sample images</li>
              </ul>
              <p className="text-xs text-gray-500 mt-3">
                All blogs will be saved to your Amplify database.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Important Notice */}
        <Alert className="mb-8">
          <Info className="h-4 w-4" />
          <AlertDescription>
            <strong>Important:</strong> Make sure you're logged in with appropriate permissions before creating blogs. 
            This action will create real database entries that will appear in your blog management dashboard and community forums.
          </AlertDescription>
        </Alert>

        {/* Main Component */}
        <CreateTestBlogs />

        {/* Additional Information */}
        <div className="mt-12 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>What happens after creation?</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-2">Blog Management Dashboard</h4>
                  <p className="text-sm text-gray-600">
                    All created blogs will appear in your <strong>/dashboard/blogs</strong> page where you can:
                  </p>
                  <ul className="text-sm text-gray-600 mt-2 space-y-1">
                    <li>• View all created blogs</li>
                    <li>• Edit blog content</li>
                    <li>• Change status (draft/published)</li>
                    <li>• Delete unwanted blogs</li>
                    <li>• Manage featured/pinned status</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Community Forums</h4>
                  <p className="text-sm text-gray-600">
                    Published blogs will be visible in the <strong>/community/forums</strong> page where users can:
                  </p>
                  <ul className="text-sm text-gray-600 mt-2 space-y-1">
                    <li>• Browse by category</li>
                    <li>• Search blog content</li>
                    <li>• Read full blog posts</li>
                    <li>• View featured articles</li>
                    <li>• See author information</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Sample Blog Content</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                The test blogs include comprehensive content covering various aspects of wedding planning:
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                <div className="bg-gray-50 p-3 rounded">
                  <strong>Planning Guides</strong>
                  <p className="text-gray-600 mt-1">Step-by-step wedding planning tips and checklists</p>
                </div>
                <div className="bg-gray-50 p-3 rounded">
                  <strong>Vendor Insights</strong>
                  <p className="text-gray-600 mt-1">Professional advice from wedding service providers</p>
                </div>
                <div className="bg-gray-50 p-3 rounded">
                  <strong>Real Stories</strong>
                  <p className="text-gray-600 mt-1">Actual wedding experiences and case studies</p>
                </div>
                <div className="bg-gray-50 p-3 rounded">
                  <strong>Budget Tips</strong>
                  <p className="text-gray-600 mt-1">Money-saving strategies and cost breakdowns</p>
                </div>
                <div className="bg-gray-50 p-3 rounded">
                  <strong>Style Trends</strong>
                  <p className="text-gray-600 mt-1">Latest fashion and decoration trends</p>
                </div>
                <div className="bg-gray-50 p-3 rounded">
                  <strong>Expert Advice</strong>
                  <p className="text-gray-600 mt-1">Professional tips and common mistake warnings</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  )
}
