'use client'

import { LayoutWrapper } from "@/components/layout-wrapper"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Star, ShoppingBag, MapPin, Users, Database, Zap, Shield } from "lucide-react"
import { EntityReviews } from "@/components/entity-reviews"

export default function ReviewSystemDemoPage() {
  // Mock entity data for demonstration
  const mockEntities = {
    shop: {
      id: "shop-demo-1",
      name: "Elegant Bridal Collection",
      type: "SHOP"
    },
    venue: {
      id: "venue-demo-1", 
      name: "Grand Palace Wedding Hall",
      type: "VENUE"
    },
    vendor: {
      id: "vendor-demo-1",
      name: "Artistic Wedding Photography",
      type: "VENDOR"
    }
  }

  return (
    <LayoutWrapper>
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20 py-8">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              🌟 Amplify Review System Demo
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              Experience our comprehensive review system powered by AWS Amplify. 
              See how customers can leave detailed reviews for shops, venues, and vendors with real-time data.
            </p>
          </div>

          {/* System Overview */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="w-6 h-6" />
                Review System Architecture
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <Zap className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-blue-900 mb-2">Real-time API</h3>
                  <p className="text-sm text-blue-700">
                    AWS Amplify GraphQL API with real-time subscriptions for instant review updates
                  </p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <Shield className="w-8 h-8 text-green-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-green-900 mb-2">Secure Authentication</h3>
                  <p className="text-sm text-green-700">
                    User authentication with Cognito, ensuring only verified users can submit reviews
                  </p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <Star className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                  <h3 className="font-semibold text-purple-900 mb-2">Rich Reviews</h3>
                  <p className="text-sm text-purple-700">
                    Multi-dimensional ratings, images, recommendations, and detailed feedback
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Features Overview */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>🚀 Key Features Implemented</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">📝 Review Features:</h4>
                  <ul className="space-y-2 text-sm text-gray-700">
                    <li>• <strong>Multi-dimensional Ratings:</strong> Overall, Service, Value, Communication, Professionalism</li>
                    <li>• <strong>Rich Content:</strong> Title, detailed review, images, recommendations</li>
                    <li>• <strong>User Information:</strong> Name, location, wedding date, verification status</li>
                    <li>• <strong>Helpful Voting:</strong> Users can mark reviews as helpful</li>
                    <li>• <strong>Moderation:</strong> Reviews require approval before publication</li>
                    <li>• <strong>Entity-Specific:</strong> Separate reviews for shops, venues, and vendors</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">📊 Analytics & Display:</h4>
                  <ul className="space-y-2 text-sm text-gray-700">
                    <li>• <strong>Review Statistics:</strong> Average ratings, distribution, recommendation rates</li>
                    <li>• <strong>Detailed Breakdowns:</strong> Service quality, value, communication metrics</li>
                    <li>• <strong>Visual Ratings:</strong> Star displays, progress bars, rating distributions</li>
                    <li>• <strong>Responsive Design:</strong> Mobile-friendly review forms and displays</li>
                    <li>• <strong>Real-time Updates:</strong> Instant review submission and display</li>
                    <li>• <strong>Authentication Integration:</strong> Seamless login/signup flow</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Entity-Specific Review Demos */}
          <Tabs defaultValue="shop" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="shop" className="flex items-center gap-2">
                <ShoppingBag className="w-4 h-4" />
                Shop Reviews
              </TabsTrigger>
              <TabsTrigger value="venue" className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                Venue Reviews
              </TabsTrigger>
              <TabsTrigger value="vendor" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Vendor Reviews
              </TabsTrigger>
            </TabsList>

            <TabsContent value="shop">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ShoppingBag className="w-5 h-5" />
                    {mockEntities.shop.name} - Shop Reviews
                  </CardTitle>
                  <p className="text-gray-600">
                    Reviews for wedding shop products including bridal wear, accessories, and decorations.
                  </p>
                </CardHeader>
                <CardContent>
                  <EntityReviews 
                    entityType="SHOP"
                    entityId={mockEntities.shop.id}
                    entityName={mockEntities.shop.name}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="venue">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="w-5 h-5" />
                    {mockEntities.venue.name} - Venue Reviews
                  </CardTitle>
                  <p className="text-gray-600">
                    Reviews for wedding venues including halls, outdoor spaces, and destination venues.
                  </p>
                </CardHeader>
                <CardContent>
                  <EntityReviews 
                    entityType="VENUE"
                    entityId={mockEntities.venue.id}
                    entityName={mockEntities.venue.name}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="vendor">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    {mockEntities.vendor.name} - Vendor Reviews
                  </CardTitle>
                  <p className="text-gray-600">
                    Reviews for wedding vendors including photographers, caterers, decorators, and musicians.
                  </p>
                </CardHeader>
                <CardContent>
                  <EntityReviews 
                    entityType="VENDOR"
                    entityId={mockEntities.vendor.id}
                    entityName={mockEntities.vendor.name}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Technical Implementation */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>🔧 Technical Implementation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">📁 Files Created/Updated:</h4>
                  <ul className="space-y-1 text-sm text-gray-700 font-mono">
                    <li>• <code>amplify/backend/api/thirumanam/schema.graphql</code></li>
                    <li>• <code>src/services/entityReviewService.js</code></li>
                    <li>• <code>components/entity-reviews.tsx</code></li>
                    <li>• <code>components/entity-review-form.tsx</code></li>
                    <li>• <code>app/shop/[id]/page.tsx</code></li>
                    <li>• <code>app/venues/[id]/page.tsx</code></li>
                    <li>• <code>app/vendors/[id]/page.tsx</code></li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">🛠️ Technologies Used:</h4>
                  <ul className="space-y-1 text-sm text-gray-700">
                    <li>• <strong>AWS Amplify:</strong> Backend API and authentication</li>
                    <li>• <strong>GraphQL:</strong> Type-safe API with real-time subscriptions</li>
                    <li>• <strong>Amazon Cognito:</strong> User authentication and authorization</li>
                    <li>• <strong>React/Next.js:</strong> Frontend framework with TypeScript</li>
                    <li>• <strong>Tailwind CSS:</strong> Responsive styling and components</li>
                    <li>• <strong>Shadcn/ui:</strong> Modern UI component library</li>
                  </ul>
                </div>
              </div>

              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 className="font-semibold text-yellow-900 mb-2">💡 How to Test:</h4>
                <ol className="list-decimal list-inside space-y-1 text-sm text-yellow-800">
                  <li>Click on any tab above to see entity-specific reviews</li>
                  <li>Click "Write a Review" to open the review form (requires authentication)</li>
                  <li>Fill out the detailed review form with ratings and feedback</li>
                  <li>Submit the review and see it appear in the list (after moderation)</li>
                  <li>Try the "Helpful" button to vote on existing reviews</li>
                  <li>View detailed statistics and rating breakdowns</li>
                </ol>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </LayoutWrapper>
  )
}
