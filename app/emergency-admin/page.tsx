'use client';

import React, { useState } from 'react';
import { TopHeader } from '@/components/top-header';
import { Header } from '@/components/header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Shield, 
  Crown, 
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import AdminUserService from '@/lib/services/adminUserService';

export default function EmergencyAdminPage() {
  const [formData, setFormData] = useState({
    userId: '',
    adminLevel: 'ADMIN' as 'ADMIN' | 'SUPER_ADMIN'
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const handleMakeAdmin = async () => {
    if (!formData.userId.trim()) {
      setMessage({ type: 'error', text: 'Please enter a User ID' });
      return;
    }

    try {
      setLoading(true);
      setMessage(null);
      
      const result = await AdminUserService.makeUserAdmin(
        formData.userId.trim(),
        formData.adminLevel
      );

      if (result.success) {
        setMessage({ 
          type: 'success', 
          text: `Successfully made user ${formData.userId} a ${formData.adminLevel}!` 
        });
        setFormData({ userId: '', adminLevel: 'ADMIN' });
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to make user admin' });
      }
    } catch (error) {
      console.error('Error making user admin:', error);
      setMessage({ type: 'error', text: 'Failed to make user admin' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <AlertTriangle className="h-8 w-8 text-orange-500 mr-2" />
            <h1 className="text-3xl font-bold text-gray-900">Emergency Admin Access</h1>
          </div>
          <p className="text-gray-600">Direct admin creation utility - no authentication required</p>
        </div>

        {/* Warning */}
        <Card className="mb-6 border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-orange-500 mr-2 mt-0.5" />
              <div className="text-orange-800">
                <p className="font-semibold">Development Tool Only</p>
                <p className="text-sm">This utility bypasses normal authentication and should only be used in development environments.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Message */}
        {message && (
          <div className={`mb-6 p-4 rounded-lg ${
            message.type === 'success' 
              ? 'bg-green-50 border border-green-200 text-green-800'
              : 'bg-red-50 border border-red-200 text-red-800'
          }`}>
            <div className="flex items-center">
              {message.type === 'success' ? (
                <CheckCircle className="h-5 w-5 mr-2" />
              ) : (
                <XCircle className="h-5 w-5 mr-2" />
              )}
              {message.text}
            </div>
          </div>
        )}

        {/* Admin Creation Form */}
        <Card>
          <CardHeader>
            <CardTitle>Create Admin User</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="userId">User ID</Label>
              <Input
                id="userId"
                value={formData.userId}
                onChange={(e) => setFormData(prev => ({ ...prev, userId: e.target.value }))}
                placeholder="Enter User ID (from AWS Cognito)"
              />
              <p className="text-xs text-gray-500 mt-1">
                This is the unique identifier from AWS Cognito (usually starts with letters/numbers)
              </p>
            </div>
            
            <div>
              <Label htmlFor="adminLevel">Admin Level</Label>
              <Select 
                value={formData.adminLevel} 
                onValueChange={(value: 'ADMIN' | 'SUPER_ADMIN') => 
                  setFormData(prev => ({ ...prev, adminLevel: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ADMIN">
                    <div className="flex items-center">
                      <Shield className="h-4 w-4 mr-2 text-blue-600" />
                      Admin
                    </div>
                  </SelectItem>
                  <SelectItem value="SUPER_ADMIN">
                    <div className="flex items-center">
                      <Crown className="h-4 w-4 mr-2 text-purple-600" />
                      Super Admin
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button 
              onClick={handleMakeAdmin}
              disabled={loading || !formData.userId.trim()}
              className="w-full bg-orange-600 hover:bg-orange-700"
            >
              {loading ? 'Creating Admin...' : 'Make User Admin'}
            </Button>
          </CardContent>
        </Card>

        {/* How to Get User ID */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>How to Get Your User ID</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <p><strong>Method 1: From Login</strong></p>
              <ol className="list-decimal list-inside space-y-1 ml-4">
                <li>Log in to your account</li>
                <li>Go to <a href="/make-me-admin" className="text-blue-600 underline">/make-me-admin</a></li>
                <li>Your User ID will be displayed in the debug info</li>
              </ol>
              
              <p><strong>Method 2: From AWS Console</strong></p>
              <ol className="list-decimal list-inside space-y-1 ml-4">
                <li>Go to AWS Cognito console</li>
                <li>Find your user pool</li>
                <li>Look for your user in the Users tab</li>
                <li>Copy the Username/User ID</li>
              </ol>
              
              <p><strong>Method 3: Browser Console</strong></p>
              <ol className="list-decimal list-inside space-y-1 ml-4">
                <li>Log in to your account</li>
                <li>Open browser developer tools (F12)</li>
                <li>Go to Console tab</li>
                <li>Type: <code className="bg-gray-100 px-1 rounded">localStorage.getItem('amplify-current-user')</code></li>
                <li>Look for the userId in the response</li>
              </ol>
            </div>
          </CardContent>
        </Card>

        {/* Quick Links */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Quick Links</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <Button asChild variant="outline">
                <a href="/login">Login Page</a>
              </Button>
              <Button asChild variant="outline">
                <a href="/make-me-admin">Make Me Admin</a>
              </Button>
              <Button asChild variant="outline">
                <a href="/admin">Admin Dashboard</a>
              </Button>
              <Button asChild variant="outline">
                <a href="/admin/users">User Management</a>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Success Instructions */}
        {message?.type === 'success' && (
          <Card className="mt-6 border-green-200 bg-green-50">
            <CardContent className="p-4">
              <div className="text-green-800">
                <p className="font-semibold mb-2">✅ Admin Created Successfully!</p>
                <p className="text-sm mb-2">Next steps:</p>
                <ol className="list-decimal list-inside space-y-1 text-sm ml-4">
                  <li>Go to <a href="/admin" className="underline">Admin Dashboard</a></li>
                  <li>Access <a href="/dashboard/admin-reviews" className="underline">Review Management</a></li>
                  <li>Manage other users at <a href="/admin/users" className="underline">User Management</a></li>
                </ol>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
