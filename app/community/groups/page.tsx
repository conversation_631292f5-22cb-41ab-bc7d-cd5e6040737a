"use client"

import { TopHeader } from "@/components/top-header"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"

const GROUPS = [
  { name: "<PERSON> Mahal", image: "/mahal.jpg" },
  { name: "Photo & Videographers", image: "/photographer.jpg" },
  { name: "Cooks & Caterings", image: "/catering.jpg" },
  { name: "Makeup & Mehandi Artists", image: "/bride_makeup.jpg" },
  { name: "Musical Artists", image: "/nadasvaram.jpg" },
  { name: "Invitations", image: "/invitations.jpg" },
  { name: "Wedding Jewellery Sets", image: "/wedding_jewels.jpg" },
  { name: "Marriage Outfits", image: "/dress_store.jpg" },
  { name: "Astrologers", image: "/astrologer.jpg" },
  { name: "DJ Music", image: "/dj_music.jpg" },
  { name: "Decorators", image: "/decorators.jpg" },
  { name: "Snacks Stall", image: "/snacks_shop.jpg" },
  { name: "Event Organizers", image: "/event_organizers.jpg" },
  { name: "Iyer/Pandit", image: "/iyer_image.jpg" },
  { name: "Return Gift", image: "/return_gift.jpg" },
  { name: "Flower Shops", image: "/flower_shops.jpg" },
  { name: "Travels", image: "/transportations.jpg" },
]

export default function GroupsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      <main className="container mx-auto px-4 py-12">
        <div className="relative mb-10">
          <div className="absolute inset-0 w-full h-full rounded-2xl bg-gradient-to-r from-pink-100 via-purple-100 to-blue-100 -z-10" />
          <h1 className="text-3xl md:text-4xl font-bold text-center py-12">Wedding Groups</h1>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
          {GROUPS.map((group) => (
            <div key={group.name} className="bg-white rounded-xl shadow-sm hover:shadow-lg transition flex flex-col items-center p-4">
              <div className="w-full h-40 relative mb-4 rounded-lg overflow-hidden bg-gray-100">
                <Image
                  src={group.image}
                  alt={group.name}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 25vw"
                />
              </div>
              <div className="text-lg font-semibold text-center mb-4">{group.name}</div>
              <Button className="w-full">Join</Button>
            </div>
          ))}
        </div>
      </main>
      <Footer />
    </div>
  )
}
