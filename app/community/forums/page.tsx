"use client"

import { useState, useEffect } from 'react'
import { TopHeader } from "@/components/top-header"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Search, MessageCircle, Pin, Plus, Filter, Calendar, Clock, Eye, Heart, Loader2 } from "lucide-react"
import { useAuth } from '@/contexts/AuthContext'
import { BlogService, BLOG_CATEGORIES, Blog, BlogCategory } from '@/lib/services/blogService'
import Image from 'next/image'
import Link from 'next/link'



export default function ForumsPage() {
  const { user, isAuthenticated } = useAuth()
  const [selectedCategory, setSelectedCategory] = useState<BlogCategory | 'ALL'>('ALL')
  const [searchTerm, setSearchTerm] = useState('')
  const [blogs, setBlogs] = useState<Blog[]>([])
  const [featuredBlogs, setFeaturedBlogs] = useState<Blog[]>([])
  const [loading, setLoading] = useState(true)
  const [searchLoading, setSearchLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load blogs on component mount
  useEffect(() => {
    loadBlogs()
  }, [])

  const loadBlogs = async () => {
    try {
      setLoading(true)
      setError(null)

      // Load all published blogs
      const publishedResult = await BlogService.listPublishedBlogs(50)
      setBlogs(publishedResult.blogs)

      // Load featured blogs
      const featuredResult = await BlogService.getFeaturedBlogs(5)
      setFeaturedBlogs(featuredResult)

    } catch (error) {
      console.error('Error loading blogs:', error)
      setError('Failed to load blog posts. Please try again later.')
    } finally {
      setLoading(false)
    }
  }

  // Filter blogs based on selected category
  const filteredBlogs = selectedCategory === 'ALL' ? blogs : blogs.filter(blog => blog.category === selectedCategory)

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      loadBlogs()
      return
    }

    try {
      setSearchLoading(true)
      const searchResult = await BlogService.searchBlogs(searchTerm, 50)
      setBlogs(searchResult.blogs)
    } catch (error) {
      console.error('Error searching blogs:', error)
    } finally {
      setSearchLoading(false)
    }
  }

  const handleCategoryFilter = async (category: BlogCategory | 'ALL') => {
    setSelectedCategory(category)

    if (category === 'ALL') {
      loadBlogs()
    } else {
      try {
        setLoading(true)
        const categoryResult = await BlogService.listBlogsByCategory(category, 50)
        setBlogs(categoryResult.blogs)
      } catch (error) {
        console.error('Error filtering blogs by category:', error)
      } finally {
        setLoading(false)
      }
    }
  }

  const getReadTime = (content: string) => {
    if (!content) return '1 min read'
    const wordsPerMinute = 200
    const words = content.split(' ').length
    const minutes = Math.ceil(words / wordsPerMinute)
    return `${minutes} min read`
  }



  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      {/* Hero Section */}
      <div className="bg-primary text-white py-28">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="text-5xl md:text-6xl font-bold mb-6">Community Forums</h1>
            <p className="text-2xl opacity-90 max-w-2xl mx-auto">
              Connect with other couples, share experiences, and get expert advice for your wedding planning journey
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Search and Actions */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              placeholder="Search blog posts..."
              className="pl-10 h-12"
              value={searchTerm}
              onChange={(e) => {
                setSearchTerm(e.target.value)
                // Clear search results when input is empty
                if (!e.target.value.trim()) {
                  loadBlogs()
                }
              }}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            />
          </div>
          <Button onClick={handleSearch} disabled={searchLoading} className="h-12 px-6">
            {searchLoading ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Search className="w-4 h-4 mr-2" />
            )}
            {searchLoading ? 'Searching...' : 'Search'}
          </Button>
          {isAuthenticated && (
            <Link href="/dashboard/blogs/create">
              <Button className="h-12 px-6">
                <Plus className="w-4 h-4 mr-2" />
                Create Blog Post
              </Button>
            </Link>
          )}
        </div>

        {/* Featured Blog Posts */}
        {!loading && featuredBlogs.length > 0 && (
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-6">Featured Articles</h2>
            <div className="space-y-6">
              {featuredBlogs.map((blog) => (
                <Card key={blog.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="grid grid-cols-1 lg:grid-cols-2">
                    {blog.featuredImage && (
                      <div className="relative h-64 lg:h-full">
                        <Image
                          src={blog.featuredImage || '/placeholder.jpg'}
                          alt={blog.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}
                    <CardContent className="p-6 flex flex-col justify-center">
                      <div className="flex items-center gap-2 mb-3">
                        <Badge className={BLOG_CATEGORIES[blog.category]?.color}>
                          {BLOG_CATEGORIES[blog.category]?.icon} {BLOG_CATEGORIES[blog.category]?.title}
                        </Badge>
                        {blog.isPinned && <Pin className="w-4 h-4 text-primary" />}
                      </div>
                      <h3 className="text-xl font-bold mb-3 line-clamp-2">
                        <Link href={`/community/forums/blog/${blog.id}`} className="hover:text-primary">
                          {blog.title}
                        </Link>
                      </h3>
                      {blog.excerpt && (
                        <p className="text-gray-600 mb-4 line-clamp-3">{blog.excerpt}</p>
                      )}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            {blog.publishedAt && new Date(blog.publishedAt).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: '2-digit',
                              day: '2-digit'
                            })}
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            {getReadTime(blog.excerpt || blog.content)}
                          </span>
                        </div>
                        <Link href={`/community/forums/blog/${blog.id}`}>
                          <Button variant="outline" size="sm">
                            Read More
                          </Button>
                        </Link>
                      </div>
                    </CardContent>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Category Tabs */}
        <div className="mb-8">
          <Tabs value={selectedCategory} onValueChange={(value) => handleCategoryFilter(value as BlogCategory | 'ALL')}>
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="ALL">All</TabsTrigger>
              <TabsTrigger value="WEDDING_PLANNING">Planning</TabsTrigger>
              <TabsTrigger value="VENUE_SELECTION">Venues</TabsTrigger>
              <TabsTrigger value="PHOTOGRAPHY_VIDEOGRAPHY">Photo</TabsTrigger>
              <TabsTrigger value="CATERING_FOOD">Food</TabsTrigger>
              <TabsTrigger value="DECORATIONS_THEMES">Decor</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {/* Blog Posts Grid */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">
              {selectedCategory === 'ALL' ? 'All Blog Posts' : `${BLOG_CATEGORIES[selectedCategory]?.title} Posts`}
            </h2>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Filter className="w-4 h-4" />
              {loading ? '...' : `${filteredBlogs.length} posts`}
            </div>
          </div>

          {error ? (
            <div className="text-center py-12">
              <div className="text-red-400 mb-4">
                <MessageCircle className="w-16 h-16 mx-auto" />
              </div>
              <h3 className="text-lg font-semibold mb-2 text-red-600">Error Loading Blogs</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={loadBlogs} variant="outline">
                Try Again
              </Button>
            </div>
          ) : loading ? (
            <div className="flex justify-center items-center py-12">
              <Loader2 className="w-8 h-8 animate-spin text-primary" />
              <span className="ml-2 text-gray-600">Loading blog posts...</span>
            </div>
          ) : filteredBlogs.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredBlogs.map((blog) => (
                <Card key={blog.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  {blog.featuredImage && (
                    <div className="relative h-48">
                      <Image
                        src={blog.featuredImage || '/placeholder.jpg'}
                        alt={blog.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  <CardContent className="p-6">
                    <div className="flex items-center gap-2 mb-3">
                      <Badge className={BLOG_CATEGORIES[blog.category]?.color}>
                        {BLOG_CATEGORIES[blog.category]?.icon} {BLOG_CATEGORIES[blog.category]?.title}
                      </Badge>
                      {blog.isPinned && <Pin className="w-4 h-4 text-primary" />}
                    </div>

                    <h3 className="font-bold text-lg mb-2 line-clamp-2">
                      <Link href={`/community/forums/blog/${blog.id}`} className="hover:text-primary">
                        {blog.title}
                      </Link>
                    </h3>

                    {blog.excerpt && (
                      <p className="text-gray-600 text-sm mb-4 line-clamp-2">{blog.excerpt}</p>
                    )}

                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center gap-3">
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 h-3" />
                          {blog.publishedAt && new Date(blog.publishedAt).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit'
                          })}
                        </span>
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 h-3" />
                          {getReadTime(blog.excerpt || blog.content)}
                        </span>
                      </div>

                      <div className="flex items-center gap-3">
                        {blog.views && blog.views > 0 && (
                          <span className="flex items-center gap-1">
                            <Eye className="h-3 h-3" />
                            {blog.views}
                          </span>
                        )}
                        {blog.likes && blog.likes > 0 && (
                          <span className="flex items-center gap-1">
                            <Heart className="h-3 h-3" />
                            {blog.likes}
                          </span>
                        )}
                        {blog.comments && blog.comments > 0 && (
                          <span className="flex items-center gap-1">
                            <MessageCircle className="h-3 h-3" />
                            {blog.comments}
                          </span>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <MessageCircle className="w-16 h-16 mx-auto" />
              </div>
              <h3 className="text-lg font-semibold mb-2">No blog posts found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm ? `No posts match "${searchTerm}"` : 'No posts in this category yet'}
              </p>
              {isAuthenticated && (
                <Link href="/dashboard/blogs/create">
                  <Button className="bg-primary hover:bg-primary/90">
                    <Plus className="w-4 h-4 mr-2" />
                    Create Blog Post
                  </Button>
                </Link>
              )}
            </div>
          )}
        </div>

        {/* Community Guidelines */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">Community Guidelines</h3>
          <ul className="text-blue-800 space-y-2 text-sm">
            <li>• Be respectful and kind to all community members</li>
            <li>• Stay on topic and use appropriate blog categories</li>
            <li>• No spam, self-promotion, or commercial posts</li>
            <li>• Share your experiences and help others with their wedding planning</li>
            <li>• Report inappropriate content to moderators</li>
          </ul>
        </div>

        {/* Vendor Information */}
        {isAuthenticated && (
          <div className="mt-8 bg-green-50 border border-green-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-green-900 mb-3">Share Your Expertise</h3>
            <p className="text-green-800 mb-4">
              Share your wedding planning experiences, tips, and insights with the community. Create blog posts to help other couples and showcase your expertise.
            </p>
            <Link href="/dashboard/blogs/create">
              <Button className="bg-green-600 hover:bg-green-700">
                <Plus className="w-4 h-4 mr-2" />
                Start Creating Content
              </Button>
            </Link>
          </div>
        )}
      </div>

      <Footer />
    </div>
  )
}
