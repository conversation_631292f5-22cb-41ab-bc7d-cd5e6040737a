"use client"

import { TopHeader } from "@/components/top-header"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search, HelpCircle } from "lucide-react"

export default function QAPage() {
  const featuredQuestions = [
    {
      id: 1,
      title: "What's the average cost of a wedding in Chennai?",
      description: "I'm planning my wedding in Chennai and trying to set a realistic budget. What should I expect to spend on average?",
      author: "PriyaK",
      answers: 23,
    },
    {
      id: 2,
      title: "Best wedding venues in Bangalore under 5 lakhs?",
      description: "Looking for beautiful wedding venues in Bangalore that can accommodate 200-300 guests within a budget of 5 lakhs.",
      author: "RajeshM",
      answers: 18,
    },
    {
      id: 3,
      title: "How to choose the right wedding photographer?",
      description: "What questions should I ask potential wedding photographers? What should I look for in their portfolio?",
      author: "MeeraS",
      answers: 31,
    },
    {
      id: 4,
      title: "Traditional vs modern wedding decorations - need advice",
      description: "Torn between traditional South Indian decorations and modern contemporary themes. How did you decide?",
      author: "Kavya<PERSON>",
      answers: 15,
    },
    {
      id: 5,
      title: "Wedding planning timeline - when to start what?",
      description: "Getting married in 8 months. What should I prioritize first? Looking for a detailed timeline.",
      author: "ArjunV",
      answers: 42,
    }
  ]

  const categories = [
    { name: "Budget" },
    { name: "Venues" },
    { name: "Photography" },
    { name: "Decorations" },
    { name: "Planning" },
    { name: "Catering" }
  ]

  return (
    <div className="min-h-screen bg-white">
      <TopHeader />
      <Header />

      {/* Hero Section */}
      <div className="bg-primary text-white py-28 mb-4">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="text-5xl md:text-6xl font-bold mb-6">Wedding Q&amp;A</h1>
            <p className="text-2xl opacity-90 max-w-2xl mx-auto">
              Get expert answers to your wedding planning questions from experienced couples and professionals
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pb-16">
        {/* Search and Ask */}
        <div className="flex flex-col md:flex-row gap-3 mb-8">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              placeholder="Search questions or ask something new..."
              className="pl-10 h-10 bg-white border-gray-200"
            />
          </div>
          <Button className="h-10 px-5 flex items-center gap-2">
            <HelpCircle className="w-4 h-4" />
            Ask Question
          </Button>
        </div>

        {/* Categories - horizontal scroll */}
        <div className="mb-8 overflow-x-auto">
          <div className="flex gap-3">
            {categories.map((category) => (
              <Badge key={category.name} className="bg-gray-200 text-gray-700 px-4 py-2 rounded-full cursor-pointer whitespace-nowrap hover:bg-gray-300 transition">
                {category.name}
              </Badge>
            ))}
          </div>
        </div>

        {/* Trending Questions */}
        <div>
          <h2 className="text-xl font-semibold mb-6">Trending Questions</h2>
          <div className="space-y-4">
            {featuredQuestions.map((question) => (
              <div key={question.id} className="border border-gray-100 rounded-lg p-5 bg-white hover:shadow-sm transition">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2 mb-1">
                  <h3 className="text-lg font-medium text-gray-900">{question.title}</h3>
                  <span className="text-xs text-gray-500">By {question.author}</span>
                </div>
                <p className="text-gray-700 text-sm mb-2 line-clamp-2">{question.description}</p>
                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <span>{question.answers} answers</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
