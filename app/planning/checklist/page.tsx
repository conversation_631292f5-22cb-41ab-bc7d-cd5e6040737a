'use client'
import { TopHeader } from "@/components/top-header"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"
import { useState, useEffect } from "react"
import { CheckCircle2, Circle, Plus, Trash2, Calendar, CheckSquare, Square, ChevronDown, ChevronRight, BarChart3, Target, Clock, Users, Building2, Sparkles, Camera, Shirt, Utensils, Car, Gift, Music, Mail, Sparkle, CalendarCheck, List } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"
import ChecklistService, { ChecklistCategory, ChecklistItem } from "@/lib/services/checklistService"
import toast from 'react-hot-toast'



export default function ChecklistPage() {
  const { user } = useAuth()
  const [categories, setCategories] = useState<ChecklistCategory[]>([])
  const [newItem, setNewItem] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState({
    totalItems: 0,
    completedItems: 0,
    completionPercentage: 0,
    highPriorityPending: 0
  })

  function getCategoryIcon(categoryName: string) {
    const name = categoryName.toLowerCase();
    if (name.includes('venue') || name.includes('ceremony')) return <Calendar className="w-6 h-6 text-primary" />;
    if (name.includes('vendor')) return <Building2 className="w-6 h-6 text-primary" />;
    if (name.includes('budget') || name.includes('payment') || name.includes('finance')) return <BarChart3 className="w-6 h-6 text-primary" />;
    if (name.includes('guest')) return <Users className="w-6 h-6 text-primary" />;
    if (name.includes('task') || name.includes('checklist') || name.includes('todo')) return <CheckSquare className="w-6 h-6 text-primary" />;
    if (name.includes('goal') || name.includes('milestone')) return <Target className="w-6 h-6 text-primary" />;
    if (name.includes('decor') || name.includes('decoration')) return <Sparkles className="w-6 h-6 text-primary" />;
    if (name.includes('photo') || name.includes('photography')) return <Camera className="w-6 h-6 text-primary" />;
    if (name.includes('dress') || name.includes('attire') || name.includes('outfit')) return <Shirt className="w-6 h-6 text-primary" />;
    if (name.includes('catering') || name.includes('food') || name.includes('menu') || name.includes('cake')) return <Utensils className="w-6 h-6 text-primary" />;
    if (name.includes('transport') || name.includes('travel') || name.includes('car') || name.includes('bus')) return <Car className="w-6 h-6 text-primary" />;
    if (name.includes('gift') || name.includes('present')) return <Gift className="w-6 h-6 text-primary" />;
    if (name.includes('music') || name.includes('dj') || name.includes('band')) return <Music className="w-6 h-6 text-primary" />;
    if (name.includes('invitation') || name.includes('invite')) return <Mail className="w-6 h-6 text-primary" />;
    if (name.includes('beauty') || name.includes('makeup') || name.includes('salon')) return <Sparkle className="w-6 h-6 text-primary" />;
    if (name.includes('planner') || name.includes('plan') || name.includes('schedule')) return <CalendarCheck className="w-6 h-6 text-primary" />;
    return <List className="w-6 h-6 text-primary" />;
  }

  // Load checklist data
  useEffect(() => {
    loadChecklistData()
  }, [user])

  const loadChecklistData = async () => {
    try {
      setLoading(true)
      const userId = user?.userId
      const data = await ChecklistService.getChecklistData(userId)
      setCategories(data)
      setStats(ChecklistService.getChecklistStats(data))
    } catch (error) {
      console.error('Error loading checklist:', error)
      toast.error('Failed to load checklist data')
    } finally {
      setLoading(false)
    }
  }

  const toggleItem = async (categoryId: string, itemId: string) => {
    try {
      // Find the item to get its current state
      const category = categories.find(cat => cat.id === categoryId)
      const item = category?.items?.find(item => item.id === itemId)

      if (!item) return

      // For non-logged-in users, just update local state
      if (!user?.userId) {
        setCategories(cats => cats.map(cat =>
          cat.id === categoryId
            ? { ...cat, items: cat.items?.map(item =>
                item.id === itemId
                  ? { ...item, completed: !item.completed }
                  : item
              )}
            : cat
        ))
        // Update stats for local changes
        const updatedCategories = categories.map(cat =>
          cat.id === categoryId
            ? { ...cat, items: cat.items?.map(item =>
                item.id === itemId
                  ? { ...item, completed: !item.completed }
                  : item
              )}
            : cat
        )
        setStats(ChecklistService.getChecklistStats(updatedCategories))
        return
      }

      // For logged-in users, update via API
      await ChecklistService.updateChecklistItem(itemId, {
        completed: !item.completed
      })

      // Update local state
      setCategories(cats => cats.map(cat =>
        cat.id === categoryId
          ? { ...cat, items: cat.items?.map(item =>
              item.id === itemId
                ? { ...item, completed: !item.completed }
                : item
            )}
          : cat
      ))

      // Update stats
      const updatedCategories = categories.map(cat =>
        cat.id === categoryId
          ? { ...cat, items: cat.items?.map(item =>
              item.id === itemId
                ? { ...item, completed: !item.completed }
                : item
            )}
          : cat
      )
      setStats(ChecklistService.getChecklistStats(updatedCategories))

      toast.success(item.completed ? 'Item marked as pending' : 'Item completed!')
    } catch (error) {
      console.error('Error toggling item:', error)
      toast.error('Failed to update item')
    }
  }

  const toggleCategory = async (categoryId: string) => {
    try {
      const category = categories.find(cat => cat.id === categoryId)
      if (!category) return

      // For non-logged-in users, just update local state
      if (!user?.userId) {
        setCategories(cats => cats.map(cat =>
          cat.id === categoryId
            ? { ...cat, expanded: !cat.expanded }
            : cat
        ))
        return
      }

      // For logged-in users, update via API
      await ChecklistService.updateChecklistCategory(categoryId, {
        expanded: !category.expanded
      })

      // Update local state
      setCategories(cats => cats.map(cat =>
        cat.id === categoryId
          ? { ...cat, expanded: !cat.expanded }
          : cat
      ))
    } catch (error) {
      console.error('Error toggling category:', error)
      // Still update local state even if API fails
      setCategories(cats => cats.map(cat =>
        cat.id === categoryId
          ? { ...cat, expanded: !cat.expanded }
          : cat
      ))
    }
  }

  const addItem = async () => {
    if (!newItem.trim() || !selectedCategory) return

    try {
      // For non-logged-in users, just update local state
      if (!user?.userId) {
        const newChecklistItem: ChecklistItem = {
          id: Date.now().toString(),
          categoryId: selectedCategory,
          text: newItem.trim(),
          completed: false,
          priority: 'MEDIUM',
          isDefault: false
        }

        setCategories(cats => cats.map(cat =>
          cat.id === selectedCategory
            ? { ...cat, items: [...(cat.items || []), newChecklistItem] }
            : cat
        ))

        setNewItem('')
        setSelectedCategory('')
        return
      }

      // For logged-in users, create via API
      const newChecklistItem = await ChecklistService.createChecklistItem(
        user.userId,
        selectedCategory,
        {
          text: newItem.trim(),
          completed: false,
          priority: 'MEDIUM'
        }
      )

      // Update local state
      setCategories(cats => cats.map(cat =>
        cat.id === selectedCategory
          ? { ...cat, items: [...(cat.items || []), newChecklistItem] }
          : cat
      ))

      setNewItem('')
      setSelectedCategory('')
      toast.success('Item added successfully!')
    } catch (error) {
      console.error('Error adding item:', error)
      toast.error('Failed to add item')
    }
  }

  const deleteItem = async (categoryId: string, itemId: string) => {
    try {
      // For non-logged-in users, just update local state
      if (!user?.userId) {
        setCategories(cats => cats.map(cat =>
          cat.id === categoryId
            ? { ...cat, items: cat.items?.filter(item => item.id !== itemId) }
            : cat
        ))
        return
      }

      // For logged-in users, delete via API
      await ChecklistService.deleteChecklistItem(itemId)

      // Update local state
      setCategories(cats => cats.map(cat =>
        cat.id === categoryId
          ? { ...cat, items: cat.items?.filter(item => item.id !== itemId) }
          : cat
      ))

      toast.success('Item deleted successfully!')
    } catch (error) {
      console.error('Error deleting item:', error)
      toast.error('Failed to delete item')
    }
  }

  const removeItem = (categoryId: string, itemId: string) => {
    setCategories(cats => cats.map(cat => 
      cat.id === categoryId 
        ? { ...cat, items: cat.items.filter(item => item.id !== itemId) }
        : cat
    ))
  }

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high': return 'text-red-600'
      case 'medium': return 'text-yellow-600'
      case 'low': return 'text-green-600'
      default: return 'text-gray-600'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <TopHeader />
      <Header />
      
      <main className="py-12 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-pink-600 rounded-full mb-6 shadow-lg">
              <CheckSquare className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Wedding Planning Checklist
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto mb-8">
              Stay organized with our comprehensive wedding planning checklist.
              Check off tasks as you complete them and never miss an important detail.
            </p>

            {/* User Status */}
            <div className="text-sm text-gray-500">
              {user?.userId ? (
                <span className="inline-flex items-center gap-2 bg-green-50 text-green-700 px-3 py-1 rounded-full">
                  <CheckCircle2 className="w-4 h-4" />
                  Your progress is automatically saved
                </span>
              ) : (
                <span className="inline-flex items-center gap-2 bg-yellow-50 text-yellow-700 px-3 py-1 rounded-full">
                  <Clock className="w-4 h-4" />
                  Sign in to save your progress
                </span>
              )}
            </div>
          </div>

          {/* Progress Stats */}
          {!loading && (
            <div className="relative mb-16">
              <div className="flex justify-between items-start">
                {/* Total Tasks - left */}
                <div className="bg-white rounded-lg p-8 shadow-md border border-gray-200 w-1/4 min-w-[180px]">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Tasks</p>
                      <p className="text-4xl font-bold text-primary">{stats.totalItems}</p>
                    </div>
                    <div className="w-16 h-16 bg-gradient-to-br from-primary to-pink-600 rounded-lg flex items-center justify-center shadow-lg">
                      <CheckSquare className="w-8 h-8 text-white" />
                    </div>
                  </div>
                </div>
                {/* Progress - center */}
                <div className="absolute left-1/2 -translate-x-1/2 top-0 bg-white rounded-lg p-8 shadow-md border border-gray-200 w-1/4 min-w-[180px] flex flex-col items-center">
                  <div className="flex items-center justify-between w-full">
                    <div>
                      <p className="text-sm font-medium text-gray-600 text-center">Progress</p>
                      <p className="text-4xl font-bold text-primary text-center">{stats.completionPercentage}%</p>
                    </div>
                    <div className="w-16 h-16 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center shadow-lg">
                      <BarChart3 className="w-8 h-8 text-white" />
                    </div>
                  </div>
                </div>
                {/* Completed - right */}
                <div className="bg-white rounded-lg p-8 shadow-md border border-gray-200 w-1/4 min-w-[180px] ml-auto">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600 text-right">Completed</p>
                      <p className="text-4xl font-bold text-primary text-right">{stats.completedItems}</p>
                    </div>
                    <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center shadow-lg">
                      <CheckCircle2 className="w-8 h-8 text-white" />
                    </div>
                  </div>
                </div>
              </div>
              {/* Progress Bar below cards */}
              <div className="mt-24">
                <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                  <div
                    className="bg-primary h-3 rounded-full transition-all duration-300"
                    style={{ width: `${stats.completionPercentage}%` }}
                  ></div>
                </div>
                <div className="flex justify-between text-sm text-gray-600">
                  <span>{stats.completedItems} of {stats.totalItems} tasks completed</span>
                  <span>{stats.totalItems - stats.completedItems} remaining</span>
                </div>
              </div>
            </div>
          )}

          {/* Add New Item */}
          <div className="bg-card rounded-xl shadow-sm border p-6 mb-8">
            <h3 className="text-lg font-semibold text-card-foreground mb-4">
              Add New Task
            </h3>
            <div className="flex flex-col sm:flex-row gap-4">
              <select
                className="flex-1 bg-background border border-border rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-primary/20"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <option value="">Select category</option>
                {categories.map(cat => (
                  <option key={cat.id} value={cat.id}>{cat.name}</option>
                ))}
              </select>
              <input
                type="text"
                placeholder="Enter task description"
                className="flex-1 bg-background border border-border rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-primary/20"
                value={newItem}
                onChange={(e) => setNewItem(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addItem()}
              />
              <button
                className="bg-primary text-primary-foreground rounded-lg px-6 py-3 font-semibold hover:bg-primary/90 transition-colors flex items-center gap-2"
                onClick={addItem}
                disabled={!newItem.trim() || !selectedCategory}
              >
                <Plus className="w-4 h-4" />
                Add
              </button>
            </div>
          </div>

          {/* Loading State */}
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
              <p className="text-gray-600">Loading your checklist...</p>
            </div>
          ) : (
            /* Categories */
            <div className="space-y-6">
              {categories.map(category => (
              <div key={category.id} className="bg-card rounded-xl shadow-sm border overflow-hidden">
                {/* Category Header */}
                <div 
                  className="flex items-center justify-between p-6 cursor-pointer hover:bg-muted/30 transition-colors"
                  onClick={() => toggleCategory(category.id)}
                >
                  <div className="flex items-center gap-4">
                    {getCategoryIcon(category.name) && (
                      <span>{getCategoryIcon(category.name)}</span>
                    )}
                    <div>
                      <h3 className="text-xl font-semibold text-card-foreground">
                        {category.name}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {category.items.filter(item => item.completed).length} of {category.items.length} completed
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {category.expanded ? (
                      <ChevronDown className="w-5 h-5 text-muted-foreground" />
                    ) : (
                      <ChevronRight className="w-5 h-5 text-muted-foreground" />
                    )}
                  </div>
                </div>

                {/* Category Items */}
                {category.expanded && (
                  <div className="border-t bg-muted/20">
                    <div className="p-6 space-y-3">
                      {category.items
                        ?.slice()
                        .sort((a, b) => {
                          const priorityOrder = { high: 0, medium: 1, low: 2 };
                          return priorityOrder[a.priority.toLowerCase()] - priorityOrder[b.priority.toLowerCase()];
                        })
                        .map(item => (
                        <div 
                          key={item.id} 
                          className="flex items-center gap-3 p-3 bg-background rounded-lg hover:bg-muted/30 transition-colors"
                        >
                          <button
                            onClick={() => toggleItem(category.id, item.id)}
                            className="flex-shrink-0"
                          >
                            {item.completed ? (
                              <CheckCircle2 className="w-5 h-5 text-primary" />
                            ) : (
                              <Circle className="w-5 h-5 text-muted-foreground hover:text-primary transition-colors" />
                            )}
                          </button>
                          <span 
                            className={`flex-1 text-sm ${
                              item.completed 
                                ? 'line-through text-muted-foreground' 
                                : 'text-card-foreground'
                            }`}
                          >
                            {item.text}
                          </span>
                          <button
                            onClick={() => deleteItem(category.id, item.id)}
                            className="p-1 text-red-600 hover:bg-red-50 rounded transition-colors"
                            title="Delete item"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                </div>
              ))}
            </div>
          )}
        </div>
      </main>

      <Footer />
    </div>
  )
} 