"use client"

import { TopHeader } from "@/components/top-header"
import { Head<PERSON> } from "@/components/header"
import { Foot<PERSON> } from "@/components/footer"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, CheckCircle, AlertCircle, Star } from "lucide-react"

export default function WeddingTimelinePage() {
  const timelinePhases = [
    {
      phase: "12+ Months Before",
      color: "bg-purple-100 text-purple-800",
      icon: "🎯",
      priority: "High Priority",
      tasks: [
        { task: "Set wedding date and budget", priority: "high", category: "Planning" },
        { task: "Create guest list (rough estimate)", priority: "high", category: "Planning" },
        { task: "Book wedding venue", priority: "high", category: "Venue" },
        { task: "Hire wedding photographer", priority: "high", category: "Photography" },
        { task: "Book caterer or catering venue", priority: "high", category: "Catering" },
        { task: "Start shopping for wedding outfits", priority: "medium", category: "Shopping" },
        { task: "Research and shortlist vendors", priority: "medium", category: "Vendors" }
      ]
    },
    {
      phase: "9-11 Months Before",
      color: "bg-blue-100 text-blue-800",
      icon: "📋",
      priority: "High Priority",
      tasks: [
        { task: "Finalize guest list", priority: "high", category: "Planning" },
        { task: "Book decorator and florist", priority: "high", category: "Decoration" },
        { task: "Hire music/DJ/band", priority: "high", category: "Entertainment" },
        { task: "Book makeup artist and hairstylist", priority: "high", category: "Beauty" },
        { task: "Order wedding invitations", priority: "medium", category: "Invitations" },
        { task: "Book honeymoon travel", priority: "medium", category: "Travel" },
        { task: "Start wedding shopping", priority: "medium", category: "Shopping" }
      ]
    },
    {
      phase: "6-8 Months Before",
      color: "bg-green-100 text-green-800",
      icon: "🛍️",
      priority: "Medium Priority",
      tasks: [
        { task: "Finalize wedding outfits", priority: "high", category: "Shopping" },
        { task: "Book wedding jewelry", priority: "high", category: "Jewelry" },
        { task: "Arrange transportation", priority: "medium", category: "Transport" },
        { task: "Plan pre-wedding photoshoot", priority: "medium", category: "Photography" },
        { task: "Book accommodation for guests", priority: "medium", category: "Accommodation" },
        { task: "Finalize menu with caterer", priority: "medium", category: "Catering" },
        { task: "Order wedding favors", priority: "low", category: "Gifts" }
      ]
    },
    {
      phase: "3-5 Months Before",
      color: "bg-yellow-100 text-yellow-800",
      icon: "📝",
      priority: "Medium Priority",
      tasks: [
        { task: "Send wedding invitations", priority: "high", category: "Invitations" },
        { task: "Finalize decoration theme", priority: "high", category: "Decoration" },
        { task: "Book wedding videographer", priority: "medium", category: "Photography" },
        { task: "Plan sangeet and mehendi", priority: "medium", category: "Events" },
        { task: "Arrange wedding insurance", priority: "medium", category: "Legal" },
        { task: "Book spa and beauty treatments", priority: "low", category: "Beauty" },
        { task: "Plan bachelor/bachelorette party", priority: "low", category: "Events" }
      ]
    }
  ]

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return <AlertCircle className="w-4 h-4" />
      case 'medium': return <Clock className="w-4 h-4" />
      case 'low': return <CheckCircle className="w-4 h-4" />
      default: return <CheckCircle className="w-4 h-4" />
    }
  }

  return (
    <>
      <SimpleSEO
        title="Wedding Planning Timeline - Thirumanam 360"
        description="Complete wedding planning timeline with month-by-month checklist. Plan your perfect wedding with our comprehensive timeline guide from 12 months to wedding day."
        keywords="wedding timeline, wedding planning checklist, wedding schedule, month by month wedding planning, thirumanam 360"
        url="/planning/timeline"
      />
      
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />
        
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-primary to-accent text-primary-foreground py-16">
          <div className="container mx-auto px-4 text-center">
            <Calendar className="w-16 h-16 mx-auto mb-6" />
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Wedding Planning Timeline
            </h1>
            <p className="text-xl mb-4 max-w-2xl mx-auto opacity-90">
              Your complete month-by-month guide to planning the perfect wedding
            </p>
          </div>
        </section>

        {/* Timeline Overview */}
        <section className="py-16">
          <div className="container mx-auto px-4 max-w-6xl">
            {/* Timeline Phases */}
            <div className="space-y-8">
              {timelinePhases.map((phase, index) => (
                <Card key={index} className="overflow-hidden">
                  <CardHeader className="bg-gradient-to-r from-primary/10 to-accent/10">
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-3">
                        <span className="text-3xl">{phase.icon}</span>
                        <div>
                          <div className="text-2xl font-bold">{phase.phase}</div>
                          <Badge className={phase.color}>
                            {phase.priority}
                          </Badge>
                        </div>
                      </CardTitle>
                      <div className="text-right">
                        <div className="text-sm text-muted-foreground">
                          {phase.tasks.length} tasks
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {phase.tasks.map((item, taskIndex) => (
                        <div 
                          key={taskIndex}
                          className="p-4 border rounded-lg hover:shadow-md transition-shadow"
                        >
                          <div className="flex items-start gap-3">
                            <div className="mt-1">
                              {getPriorityIcon(item.priority)}
                            </div>
                            <div className="flex-1">
                              <h4 className="font-medium mb-2">{item.task}</h4>
                              <div className="flex items-center gap-2">
                                <Badge 
                                  variant="outline" 
                                  className={`text-xs ${getPriorityColor(item.priority)}`}
                                >
                                  {item.priority}
                                </Badge>
                                <Badge variant="secondary" className="text-xs">
                                  {item.category}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
