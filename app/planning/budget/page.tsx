'use client'
import { TopHeader } from "@/components/top-header"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { showToast, toastMessages } from '@/lib/toast'
import { useState, useEffect, useRef, Suspense, lazy } from "react"
import { Plus, Trash2, Calculator, TrendingUp, <PERSON><PERSON><PERSON> } from "@/lib/icon-imports"

// Lazy load heavy components
const LazyBudgetChart = lazy(() => import('@/components/lazy/LazyBudgetChart'))
const LazyPDFExporter = lazy(() => import('@/components/lazy/LazyPDFExporter'))

const DEFAULT_CATEGORIES = [
  { name: "Venue", percent: 30 },
  { name: "Catering", percent: 25 },
  { name: "Photography", percent: 10 },
  { name: "Decoration", percent: 10 },
  { name: "Dress", percent: 10 },
  { name: "Miscellaneous", percent: 15 },
]

// Chart colors that match the theme
const CHART_COLORS = [
  'hsl(var(--primary))', // maroon
  'hsl(var(--secondary))', // green
  'hsl(var(--accent))', // gold
  'hsl(var(--muted-foreground))', // brown
  'hsl(var(--chart-3))', // dark brown
  'hsl(var(--chart-4))', // light brown
]

const BUDGET_TEMPLATES = {
  small: { name: "Small Wedding (50-100 guests)", amount: 300000 },
  medium: { name: "Medium Wedding (100-300 guests)", amount: 800000 },
  large: { name: "Large Wedding (300+ guests)", amount: 1500000 },
  luxury: { name: "Luxury Wedding", amount: 3000000 }
}

export default function BudgetCalculatorPage() {
  const [mounted, setMounted] = useState(false)
  const pageRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  // Safe number formatting to prevent hydration issues
  const formatCurrency = (amount: number) => {
    if (!mounted) return `₹${amount}`
    return `₹${amount.toLocaleString()}`
  }

  const formatDate = (dateString: string) => {
    if (!mounted) return dateString
    return new Date(dateString).toLocaleDateString()
  }
  const [total, setTotal] = useState(500000)
  const [categories, setCategories] = useState(DEFAULT_CATEGORIES)
  const [newCat, setNewCat] = useState("")
  const [newPercent, setNewPercent] = useState("")
  const [activeView, setActiveView] = useState<'budget' | 'tracker' | 'summary'>('budget')
  const [savedBudgets, setSavedBudgets] = useState<any[]>([])
  const [budgetName, setBudgetName] = useState("")

  // Ensure component is mounted before accessing localStorage
  useEffect(() => {
    setMounted(true)
    const saved = localStorage.getItem('wedding-budgets')
    if (saved) {
      setSavedBudgets(JSON.parse(saved))
    }
  }, [])

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
        <TopHeader />
        <Header />
        <main className="py-12 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
              <p className="mt-4 text-gray-600">Loading...</p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  const handleCategoryChange = (idx: number, field: string, value: string | number) => {
    setCategories(cats => cats.map((cat, i) =>
      i === idx ? { ...cat, [field]: field === "percent" ? Number(value) : value } : cat
    ))
  }

  const handleRemove = (idx: number) => {
    setCategories(cats => cats.filter((_, i) => i !== idx))
  }

  const handleAdd = () => {
    if (!newCat.trim() || !newPercent || isNaN(Number(newPercent))) return
    const newId = Math.max(...categories.map(c => c.id), 0) + 1
    setCategories([...categories, {
      id: newId,
      name: newCat.trim(),
      percent: Number(newPercent),
      actual: 0,
      notes: "",
      priority: "medium"
    }])
    setNewCat("")
    setNewPercent("")
  }

  const saveBudget = () => {
    if (!budgetName.trim()) return
    const budget = {
      id: Date.now(),
      name: budgetName,
      total,
      categories,
      createdAt: new Date().toISOString()
    }
    const updated = [...savedBudgets, budget]
    setSavedBudgets(updated)
    localStorage.setItem('wedding-budgets', JSON.stringify(updated))
    setBudgetName("")
    showToast.success('Budget saved successfully!')
  }

  const loadTemplate = (templateKey: string) => {
    setTotal(BUDGET_TEMPLATES[templateKey as keyof typeof BUDGET_TEMPLATES].amount)
  }

  // PDF download functionality is now handled by the LazyPDFExporter component

  const totalPercent = categories.reduce((sum, c) => sum + Number(c.percent), 0)
  const totalActual = categories.reduce((sum, c) => sum + Number(c.actual), 0)
  const remainingBudget = total - totalActual

  // Prepare data for pie chart
  const chartData = categories.map((cat, index) => ({
    name: cat.name,
    value: cat.percent,
    amount: Math.round((cat.percent / 100) * total),
    color: CHART_COLORS[index % CHART_COLORS.length]
  }))

  // CustomTooltip is now part of the LazyBudgetChart component

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <TopHeader />
      <Header />
      
      <main className="py-12 px-4">
        <div className="max-w-7xl mx-auto">
          {/* PDF Content - This will be captured */}
          <div ref={contentRef}>
            {/* Header Section */}
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-pink-600 rounded-full mb-6 shadow-lg">
                <Calculator className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-4xl font-bold text-foreground mb-4">
                Wedding Budget Planner
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-6">
                Plan and track your wedding expenses with our simple budget calculator
              </p>
            </div>

          {/* Main Content - Two Column Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Calculator */}
            <div className="space-y-8">
              {/* Total Budget Card */}
              <div className="bg-card rounded-xl shadow-sm border p-8">
                <div className="flex flex-col md:flex-row items-center justify-between gap-6">
                  <div>
                    <h2 className="text-2xl font-semibold text-card-foreground mb-2">
                      Total Budget
                    </h2>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-2xl font-bold text-primary">₹</span>
                    <input
                      type="number"
                      min={0}
                      className="text-3xl font-bold text-card-foreground bg-transparent border-b-2 border-primary/30 focus:border-primary outline-none text-right min-w-[200px]"
                      value={total}
                      onChange={e => setTotal(Number(e.target.value))}
                    />
                  </div>
                </div>
              </div>

              {/* Categories Section */}
              <div className="bg-card rounded-xl shadow-sm border p-8">
                <div className="flex items-center gap-3 mb-6">
                  <TrendingUp className="w-6 h-6 text-primary" />
                  <h2 className="text-2xl font-semibold text-card-foreground">
                    Budget Categories
                  </h2>
                </div>

                {/* Categories List */}
                <div className="space-y-4 mb-8">
                  {categories.map((cat, idx) => (
                    <div key={idx} className="flex items-center gap-4 p-4 bg-muted/30 rounded-lg">
                      <div className="flex-1">
                        <input
                          className="w-full bg-transparent border-none text-lg font-medium text-card-foreground focus:outline-none"
                          value={cat.name}
                          onChange={e => handleCategoryChange(idx, "name", e.target.value)}
                          placeholder="Category name"
                        />
                      </div>
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          <input
                            type="number"
                            min={0}
                            max={100}
                            className="w-20 text-center bg-background border border-border rounded-md px-3 py-2 text-lg font-semibold"
                            value={cat.percent}
                            onChange={e => handleCategoryChange(idx, "percent", e.target.value)}
                          />
                          <span className="text-muted-foreground font-medium">%</span>
                        </div>
                        <div className="text-right min-w-[120px]">
                          <div className="text-lg font-bold text-primary">
                            ₹{Math.round((cat.percent / 100) * total).toLocaleString()}
                          </div>
                        </div>
                        <button
                          className="p-2 text-destructive hover:bg-destructive/10 rounded-md transition-colors"
                          onClick={() => handleRemove(idx)}
                          title="Remove category"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Add New Category */}
                <div className="border-t pt-6">
                  <h3 className="text-lg font-semibold text-card-foreground mb-4">
                    Add New Category
                  </h3>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <input
                      type="text"
                      placeholder="Category name"
                      className="flex-1 bg-background border border-border rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-primary/20"
                      value={newCat}
                      onChange={e => setNewCat(e.target.value)}
                    />
                    <input
                      type="number"
                      placeholder="%"
                      min={0}
                      max={100}
                      className="w-24 bg-background border border-border rounded-lg px-4 py-3 text-center focus:outline-none focus:ring-2 focus:ring-primary/20"
                      value={newPercent}
                      onChange={e => setNewPercent(e.target.value)}
                    />
                    <button
                      className="bg-primary text-primary-foreground rounded-lg px-6 py-3 font-semibold hover:bg-primary/90 transition-colors flex items-center gap-2"
                      onClick={handleAdd}
                    >
                      <Plus className="w-4 h-4" />
                      Add
                    </button>
                  </div>
                </div>
              </div>

              {/* Summary Card */}
              <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white rounded-xl p-8">
                <div className="flex flex-col md:flex-row items-center justify-between gap-6">
                  <div>
                    <h2 className="text-2xl font-bold mb-2">Budget Summary</h2>
                    <p className="text-white/90">
                      Total allocation: {totalPercent}%
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold">
                      ₹{Math.round(total).toLocaleString()}
                    </div>
                    <div className="text-white/90">
                      Total Budget
                    </div>
                  </div>
                </div>
                
                {totalPercent !== 100 && (
                  <div className="mt-6 p-4 bg-red-500/20 border border-red-500/30 rounded-lg">
                    <div className="flex items-center gap-2 text-red-100">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <span className="font-medium">
                        {totalPercent < 100 ? 'Under allocated' : 'Over allocated'}: {Math.abs(100 - totalPercent)}%
                      </span>
                    </div>
                    <p className="text-sm text-red-100/80 mt-1">
                      Adjust percentages to reach 100% for optimal budget planning
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Right Column - Pie Chart */}
            <div className="space-y-8">
              {/* Chart Card */}
              <div className="bg-card rounded-xl shadow-sm border p-8 h-fit">
                <div className="flex items-center gap-3 mb-6">
                  <PieChart className="w-6 h-6 text-primary" />
                  <h2 className="text-2xl font-semibold text-card-foreground">
                    Budget Distribution
                  </h2>
                </div>
                
                <Suspense fallback={
                  <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                }>
                  <LazyBudgetChart chartData={chartData} />
                </Suspense>

                {/* Chart Legend Details */}
                <div className="mt-6 space-y-3">
                  <h3 className="font-semibold text-card-foreground">Category Breakdown</h3>
                  <div className="grid grid-cols-1 gap-2">
                    {chartData.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-muted/30 rounded-md">
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: item.color }}
                          />
                          <span className="text-sm font-medium text-card-foreground">
                            {item.name}
                          </span>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-bold text-primary">
                            ₹{item.amount.toLocaleString()}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {item.value}%
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
          </div>

          {/* Download Button - Outside PDF content */}
          <div className="mt-8 text-center">
            <Suspense fallback={
              <div className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Loading...
              </div>
            }>
              <LazyPDFExporter
                contentRef={contentRef}
                filename="wedding-budget"
                buttonText="Download as PDF"
              />
            </Suspense>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
} 