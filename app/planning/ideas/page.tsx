'use client'
import { TopHeader } from "@/components/top-header"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { useState } from "react"
import { Lightbulb, Heart, Star, Bookmark, BookmarkPlus, Sparkles, Palette, Music, Camera, Utensils, Flower, Calendar, MapPin, CheckSquare, BarChart3, Target } from "lucide-react"

interface WeddingIdea {
  id: string
  title: string
  description: string
  category: 'decoration' | 'ceremony' | 'reception' | 'photography' | 'food' | 'music' | 'fashion' | 'timeline'
  difficulty: 'easy' | 'medium' | 'hard'
  cost: 'budget' | 'moderate' | 'luxury'
  saved: boolean
  tags: string[]
  image?: string
}

const WEDDING_IDEAS: WeddingIdea[] = [
  {
    id: '1',
    title: 'Floating Candle Centerpieces',
    description: 'Create elegant floating candles in glass bowls with rose petals for a romantic atmosphere.',
    category: 'decoration',
    difficulty: 'easy',
    cost: 'budget',
    saved: false,
    tags: ['DIY', 'Romantic', 'Budget-friendly'],
    image: '/public/ideas/floating-candle.jpg'
  },
  {
    id: '2',
    title: 'Personalized Vow Exchange',
    description: 'Write your own vows to make the ceremony more personal and meaningful.',
    category: 'ceremony',
    difficulty: 'medium',
    cost: 'budget',
    saved: false,
    tags: ['Personal', 'Emotional', 'Memorable'],
    image: '/public/ideas/vow-exchange.jpg'
  },
  {
    id: '3',
    title: 'Photo Booth with Props',
    description: 'Set up a fun photo booth with themed props for guests to enjoy.',
    category: 'photography',
    difficulty: 'easy',
    cost: 'moderate',
    saved: false,
    tags: ['Fun', 'Interactive', 'Memories'],
    image: '/public/ideas/photo-booth.jpg'
  },
  {
    id: '4',
    title: 'Family Recipe Menu',
    description: 'Include family recipes in your wedding menu for a personal touch.',
    category: 'food',
    difficulty: 'medium',
    cost: 'moderate',
    saved: false,
    tags: ['Personal', 'Traditional', 'Family'],
    image: '/public/ideas/family-recipe.jpg'
  },
  {
    id: '5',
    title: 'Live Acoustic Music',
    description: 'Hire a live acoustic musician for intimate background music during dinner.',
    category: 'music',
    difficulty: 'easy',
    cost: 'moderate',
    saved: false,
    tags: ['Live Music', 'Intimate', 'Elegant'],
    image: '/public/ideas/live-music.jpg'
  },
  {
    id: '6',
    title: 'Seasonal Flower Arrangements',
    description: 'Use seasonal flowers for bouquets and decorations to save costs and ensure freshness.',
    category: 'decoration',
    difficulty: 'medium',
    cost: 'budget',
    saved: false,
    tags: ['Seasonal', 'Budget-friendly', 'Fresh'],
    image: '/public/ideas/seasonal-flowers.jpg'
  },
  {
    id: '7',
    title: 'First Look Photography',
    description: 'Schedule a private first look session before the ceremony for intimate photos.',
    category: 'photography',
    difficulty: 'easy',
    cost: 'budget',
    saved: false,
    tags: ['Intimate', 'Emotional', 'Private'],
    image: '/public/ideas/first-look.jpg'
  },
  {
    id: '8',
    title: 'Signature Cocktail Bar',
    description: 'Create signature cocktails named after you and your partner.',
    category: 'reception',
    difficulty: 'medium',
    cost: 'moderate',
    saved: false,
    tags: ['Personal', 'Fun', 'Interactive'],
    image: '/public/ideas/signature-cocktail.jpg'
  },
  {
    id: '9',
    title: 'Handwritten Place Cards',
    description: 'Write personalized place cards for each guest with a special message.',
    category: 'decoration',
    difficulty: 'easy',
    cost: 'budget',
    saved: false,
    tags: ['Personal', 'DIY', 'Thoughtful'],
    image: '/public/ideas/handwritten-place-cards.jpg'
  },
  {
    id: '10',
    title: 'Sunset Ceremony Timing',
    description: 'Schedule your ceremony during golden hour for beautiful natural lighting.',
    category: 'ceremony',
    difficulty: 'easy',
    cost: 'budget',
    saved: false,
    tags: ['Natural Light', 'Beautiful', 'Timing'],
    image: '/public/ideas/sunset-ceremony.jpg'
  },
  {
    id: '11',
    title: 'Local Food Truck Catering',
    description: 'Hire local food trucks for a unique and casual dining experience.',
    category: 'food',
    difficulty: 'medium',
    cost: 'moderate',
    saved: false,
    tags: ['Local', 'Casual', 'Unique'],
    image: '/public/ideas/local-food-truck.jpg'
  },
  {
    id: '12',
    title: 'Memory Lane Display',
    description: 'Create a timeline display of your relationship journey with photos and mementos.',
    category: 'decoration',
    difficulty: 'medium',
    cost: 'budget',
    saved: false,
    tags: ['Personal', 'Story', 'Memories'],
    image: '/public/ideas/memory-lane.jpg'
  }
]

const CATEGORIES = [
  { id: 'all', name: 'All Ideas', icon: Sparkles },
  { id: 'decoration', name: 'Decoration', icon: Flower },
  { id: 'ceremony', name: 'Ceremony', icon: Heart },
  { id: 'reception', name: 'Reception', icon: Utensils },
  { id: 'photography', name: 'Photography', icon: Camera },
  { id: 'food', name: 'Food & Drinks', icon: Utensils },
  { id: 'music', name: 'Music', icon: Music },
  { id: 'fashion', name: 'Fashion', icon: Palette },
  { id: 'timeline', name: 'Timeline', icon: Calendar }
]

export default function IdeasPage() {
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [savedIdeas, setSavedIdeas] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState('')

  const toggleSaved = (ideaId: string) => {
    setSavedIdeas(prev => 
      prev.includes(ideaId) 
        ? prev.filter(id => id !== ideaId)
        : [...prev, ideaId]
    )
  }

  const filteredIdeas = WEDDING_IDEAS.filter(idea => {
    const matchesCategory = selectedCategory === 'all' || idea.category === selectedCategory
    const matchesSearch = idea.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         idea.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         idea.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    return matchesCategory && matchesSearch
  })

  const ideaStats = {
    totalIdeas: WEDDING_IDEAS.length,
    savedIdeas: savedIdeas.length,
    filteredIdeas: filteredIdeas.length,
    budgetIdeas: WEDDING_IDEAS.filter(idea => idea.cost === 'budget').length,
    moderateIdeas: WEDDING_IDEAS.filter(idea => idea.cost === 'moderate').length,
    luxuryIdeas: WEDDING_IDEAS.filter(idea => idea.cost === 'luxury').length
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-50 border-green-200'
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'hard': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getCostColor = (cost: string) => {
    switch (cost) {
      case 'budget': return 'text-green-600 bg-green-50 border-green-200'
      case 'moderate': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'luxury': return 'text-purple-600 bg-purple-50 border-purple-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getCategoryIcon = (category: string) => {
    const cat = CATEGORIES.find(c => c.id === category)
    return cat ? cat.icon : Sparkles
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/20">
      <TopHeader />
      <Header />
      
      <main className="py-12 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary to-pink-600 rounded-full mb-6 shadow-lg">
              <Lightbulb className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl font-bold text-foreground mb-4">
              Wedding Ideas & Inspiration
            </h1>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
              Discover creative ideas to make your wedding unique and memorable. Save your favorites and get inspired for your special day.
            </p>
          </div>

          {/* Progress Stats */}
          <div className="relative mb-16">
            <div className="flex justify-between items-start">
              {/* Total Ideas - left */}
              <div className="bg-white rounded-lg p-8 shadow-md border border-gray-200 w-1/4 min-w-[180px]">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Ideas</p>
                    <p className="text-4xl font-bold text-primary">{ideaStats.totalIdeas}</p>
                  </div>
                  <div className="w-16 h-16 bg-gradient-to-br from-primary to-pink-600 rounded-lg flex items-center justify-center shadow-lg">
                    <Lightbulb className="w-8 h-8 text-white" />
                  </div>
                </div>
              </div>
              {/* Saved Ideas - center */}
              <div className="absolute left-1/2 -translate-x-1/2 top-0 bg-white rounded-lg p-8 shadow-md border border-gray-200 w-1/4 min-w-[180px] flex flex-col items-center">
                <div className="flex items-center justify-between w-full">
                  <div>
                    <p className="text-sm font-medium text-gray-600 text-center">Saved Ideas</p>
                    <p className="text-4xl font-bold text-primary text-center">{ideaStats.savedIdeas}</p>
                  </div>
                  <div className="w-16 h-16 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center shadow-lg">
                    <Bookmark className="w-8 h-8 text-white" />
                  </div>
                </div>
              </div>
              {/* Budget Ideas - right */}
              <div className="bg-white rounded-lg p-8 shadow-md border border-gray-200 w-1/4 min-w-[180px] ml-auto">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 text-right">Budget Ideas</p>
                    <p className="text-4xl font-bold text-primary text-right">{ideaStats.budgetIdeas}</p>
                  </div>
                  <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center shadow-lg">
                    <Target className="w-8 h-8 text-white" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Search and Filter */}
          <div className="bg-card rounded-xl shadow-sm border p-6 mb-8">
            <h3 className="text-lg font-semibold text-card-foreground mb-4">
              Find Your Perfect Ideas
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <input
                type="text"
                placeholder="Search ideas..."
                className="bg-background border border-border rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-primary/20 text-sm"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <select
                className="bg-background border border-border rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-primary/20 text-sm"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                {CATEGORIES.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Ideas Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredIdeas.length === 0 ? (
              <div className="col-span-full text-center py-12">
                <div className="text-muted-foreground mb-4">
                  <Lightbulb className="w-16 h-16 mx-auto" />
                </div>
                <p className="text-muted-foreground text-lg mb-4">
                  No ideas found matching your criteria
                </p>
                <button
                  onClick={() => {
                    setSearchTerm('')
                    setSelectedCategory('all')
                  }}
                  className="bg-primary text-primary-foreground px-6 py-2 rounded-lg hover:bg-primary/90 transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            ) : (
              filteredIdeas.map((idea) => (
                <div key={idea.id} className="bg-card rounded-xl shadow-sm border p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-lg font-semibold text-card-foreground">
                      {idea.title}
                    </h3>
                    <button
                      onClick={() => toggleSaved(idea.id)}
                      className="flex-shrink-0 p-1 text-muted-foreground hover:text-primary transition-colors"
                      title={savedIdeas.includes(idea.id) ? 'Remove from saved' : 'Save idea'}
                    >
                      {savedIdeas.includes(idea.id) ? (
                        <Bookmark className="w-4 h-4 fill-current" />
                      ) : (
                        <Bookmark className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                  <p className="text-sm text-muted-foreground mb-4">
                    {idea.description}
                  </p>
                  <div className="flex gap-2 text-xs">
                    <span className={`px-2 py-1 rounded-full border ${getDifficultyColor(idea.difficulty)}`}>
                      {idea.difficulty}
                    </span>
                    <span className={`px-2 py-1 rounded-full border ${getCostColor(idea.cost)}`}>
                      {idea.cost}
                    </span>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
} 