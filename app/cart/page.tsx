"use client"

import React, { useState, useEffect } from 'react'
import { TopHeader } from "@/components/top-header"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  ShoppingCart, 
  Minus, 
  Plus, 
  Trash2, 
  Heart, 
  ArrowLeft, 
  ShoppingBag, 
  Loader2,
  Package,
  CreditCard,
  Truck,
  Shield,
  Gift,
  Percent
} from "lucide-react"
import { useAuth } from '@/contexts/AuthContext'
import { CartService, CartItemData, CartSummary } from '@/lib/services/cartService'
import { showToast } from '@/lib/toast'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

export default function CartPage() {
  const { isAuthenticated, user, isLoading: authLoading } = useAuth()
  const router = useRouter()
  const [cartSummary, setCartSummary] = useState<CartSummary>({
    items: [],
    totalItems: 0,
    totalQuantity: 0,
    subtotal: 0,
    totalDiscount: 0,
    total: 0,
    savedForLaterCount: 0
  })
  const [savedItems, setSavedItems] = useState<CartItemData[]>([])
  const [loading, setLoading] = useState(true)
  const [updating, setUpdating] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('cart')
  const [promoCode, setPromoCode] = useState('')

  useEffect(() => {
    if (!authLoading) {
      if (!isAuthenticated) {
        router.push('/login?redirect=/cart')
        return
      }
      loadCartData()
    }
  }, [isAuthenticated, authLoading])

  const loadCartData = async () => {
    try {
      setLoading(true)
      const [summary, savedResult] = await Promise.all([
        CartService.getCartSummary(),
        CartService.getCartItems('SAVED_FOR_LATER')
      ])
      
      setCartSummary(summary)
      setSavedItems(savedResult.success ? savedResult.items : [])
    } catch (error) {
      console.error('Error loading cart data:', error)
      showToast.error('Failed to load cart')
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateQuantity = async (cartItemId: string, newQuantity: number) => {
    setUpdating(cartItemId)
    try {
      const result = await CartService.updateQuantity(cartItemId, newQuantity)
      if (result.success) {
        await loadCartData()
        showToast.success('Quantity updated')
      }
    } catch (error) {
      console.error('Update quantity error:', error)
      showToast.error('Failed to update quantity')
    } finally {
      setUpdating(null)
    }
  }

  const handleRemoveItem = async (cartItemId: string) => {
    setUpdating(cartItemId)
    try {
      const result = await CartService.removeFromCart(cartItemId)
      if (result.success) {
        await loadCartData()
        showToast.success('Item removed from cart')
      }
    } catch (error) {
      console.error('Remove item error:', error)
      showToast.error('Failed to remove item')
    } finally {
      setUpdating(null)
    }
  }

  const handleSaveForLater = async (cartItemId: string) => {
    setUpdating(cartItemId)
    try {
      const result = await CartService.saveForLater(cartItemId)
      if (result.success) {
        await loadCartData()
        showToast.success('Item saved for later')
      }
    } catch (error) {
      console.error('Save for later error:', error)
      showToast.error('Failed to save for later')
    } finally {
      setUpdating(null)
    }
  }

  const handleMoveToCart = async (cartItemId: string) => {
    setUpdating(cartItemId)
    try {
      const result = await CartService.moveToCart(cartItemId)
      if (result.success) {
        await loadCartData()
        showToast.success('Item moved to cart')
      }
    } catch (error) {
      console.error('Move to cart error:', error)
      showToast.error('Failed to move to cart')
    } finally {
      setUpdating(null)
    }
  }

  const handleClearCart = async () => {
    try {
      const result = await CartService.clearCart()
      if (result.success) {
        await loadCartData()
        showToast.success('Cart cleared')
      }
    } catch (error) {
      console.error('Clear cart error:', error)
      showToast.error('Failed to clear cart')
    }
  }

  const getProductImage = (item: CartItemData) => {
    if (item.productImage && item.productImage.trim() !== '' && item.productImage !== 'undefined' && item.productImage !== 'null') {
      return item.productImage
    }
    return "/placeholder.svg"
  }

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen bg-white">
        <TopHeader />
        <Header />
        <div className="flex items-center justify-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />

      {/* Enhanced Hero Section */}
      <section className="bg-gradient-to-r from-white via-primary/5 to-accent/10 border-b border-gray-100 mt-16 md:mt-20">
        <div className="container mx-auto px-4 py-6">
          {/* Main Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* Left Section - Title and Info */}
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-3">
                <div className="p-3 bg-primary/10 rounded-full">
                  <ShoppingCart className="h-7 w-7 text-primary" />
                </div>
                <div>
                  <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-1">
                    Shopping Cart
                  </h1>
                  <div className="flex items-center gap-4 text-gray-600">
                    <span className="flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      {cartSummary.totalQuantity} {cartSummary.totalQuantity === 1 ? 'item' : 'items'}
                    </span>
                    {cartSummary.total > 0 && (
                      <span className="flex items-center gap-2 font-semibold text-primary">
                        <span>Total: ₹{cartSummary.total.toLocaleString()}</span>
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Right Section - Actions */}
            <div className="flex items-center gap-3">
              <Link href="/shop">
                <Button variant="outline" className="flex items-center gap-2 hover:bg-primary/5 hover:border-primary/30">
                  <ArrowLeft className="h-4 w-4" />
                  Continue Shopping
                </Button>
              </Link>
              {cartSummary.totalItems > 0 && (
                <Button
                  variant="outline"
                  onClick={handleClearCart}
                  className="flex items-center gap-2 text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-200"
                >
                  <Trash2 className="h-4 w-4" />
                  Clear Cart
                </Button>
              )}
            </div>
          </div>

          {/* Cart Status Bar */}
          {cartSummary.totalItems > 0 && (
            <div className="mt-6 p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2 text-green-600">
                    <Shield className="h-5 w-5" />
                    <span className="font-medium">Secure Checkout</span>
                  </div>
                  <div className="flex items-center gap-2 text-blue-600">
                    <Truck className="h-5 w-5" />
                    <span className="font-medium">Free Delivery</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-600">Subtotal</p>
                  <p className="text-xl font-bold text-gray-900">₹{cartSummary.subtotal.toLocaleString()}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Main Content */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          {loading ? (
            <div className="flex items-center justify-center py-20">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : cartSummary.totalItems === 0 && savedItems.length === 0 ? (
            <div className="text-center py-20">
              <ShoppingBag className="h-24 w-24 text-gray-300 mx-auto mb-6" />
              <h2 className="text-2xl font-bold text-gray-600 mb-4">Your cart is empty</h2>
              <p className="text-gray-500 mb-8">
                Looks like you haven't added any items to your cart yet.
              </p>
              <div className="flex gap-4 justify-center">
                <Link href="/shop">
                  <Button size="lg">
                    <ShoppingBag className="h-5 w-5 mr-2" />
                    Start Shopping
                  </Button>
                </Link>
                <Link href="/favorites">
                  <Button variant="outline" size="lg">
                    <Heart className="h-5 w-5 mr-2" />
                    View Favorites
                  </Button>
                </Link>
              </div>
            </div>
          ) : (
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-8">
                <TabsTrigger value="cart">
                  Cart ({cartSummary.totalItems})
                </TabsTrigger>
                <TabsTrigger value="saved">
                  Saved for Later ({savedItems.length})
                </TabsTrigger>
              </TabsList>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Cart Items */}
                <div className="lg:col-span-2">
                  <TabsContent value="cart">
                    {cartSummary.items.length === 0 ? (
                      <Card>
                        <CardContent className="text-center py-12">
                          <ShoppingCart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-600 mb-2">No items in cart</h3>
                          <p className="text-gray-500">Add some items to get started</p>
                        </CardContent>
                      </Card>
                    ) : (
                      <div className="space-y-4">
                        {cartSummary.items.map((item) => (
                          <Card key={item.id} className="overflow-hidden">
                            <CardContent className="p-6">
                              <div className="flex gap-4">
                                {/* Product Image */}
                                <div className="flex-shrink-0">
                                  <Link href={`/shop/${item.productId}`}>
                                    <img
                                      src={getProductImage(item)}
                                      alt={item.productName}
                                      className="w-24 h-24 object-cover rounded-lg border"
                                    />
                                  </Link>
                                </div>

                                {/* Product Details */}
                                <div className="flex-1 min-w-0">
                                  <div className="flex justify-between items-start mb-2">
                                    <div>
                                      <Link href={`/shop/${item.productId}`}>
                                        <h3 className="font-semibold text-lg hover:text-primary transition-colors line-clamp-1">
                                          {item.productName}
                                        </h3>
                                      </Link>
                                      {item.productBrand && (
                                        <p className="text-sm text-gray-500">{item.productBrand}</p>
                                      )}
                                      {item.productCategory && (
                                        <Badge variant="secondary" className="mt-1">
                                          {item.productCategory}
                                        </Badge>
                                      )}
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleRemoveItem(item.id)}
                                      disabled={updating === item.id}
                                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                    >
                                      {updating === item.id ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <Trash2 className="h-4 w-4" />
                                      )}
                                    </Button>
                                  </div>

                                  {/* Variants */}
                                  <div className="flex gap-4 text-sm text-gray-600 mb-3">
                                    {item.selectedSize && <span>Size: {item.selectedSize}</span>}
                                    {item.selectedColor && <span>Color: {item.selectedColor}</span>}
                                    {item.selectedVariant && <span>Variant: {item.selectedVariant}</span>}
                                  </div>

                                  {/* Price and Quantity */}
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                      {/* Price */}
                                      <div className="flex items-center gap-2">
                                        <span className="text-xl font-bold text-primary">
                                          ₹{item.productPrice.toLocaleString()}
                                        </span>
                                        {item.originalPrice && item.originalPrice > item.productPrice && (
                                          <>
                                            <span className="text-sm text-gray-500 line-through">
                                              ₹{item.originalPrice.toLocaleString()}
                                            </span>
                                            <Badge variant="destructive" className="text-xs">
                                              {Math.round(((item.originalPrice - item.productPrice) / item.originalPrice) * 100)}% OFF
                                            </Badge>
                                          </>
                                        )}
                                      </div>
                                    </div>

                                    {/* Quantity Controls */}
                                    <div className="flex items-center gap-3">
                                      <div className="flex items-center border rounded-lg">
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                                          disabled={updating === item.id || item.quantity <= 1}
                                          className="h-8 w-8 p-0"
                                        >
                                          <Minus className="h-3 w-3" />
                                        </Button>
                                        <span className="px-3 py-1 text-sm font-medium min-w-[40px] text-center">
                                          {item.quantity}
                                        </span>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                                          disabled={updating === item.id}
                                          className="h-8 w-8 p-0"
                                        >
                                          <Plus className="h-3 w-3" />
                                        </Button>
                                      </div>
                                    </div>
                                  </div>

                                  {/* Actions */}
                                  <div className="flex gap-2 mt-4">
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => handleSaveForLater(item.id)}
                                      disabled={updating === item.id}
                                    >
                                      <Heart className="h-4 w-4 mr-2" />
                                      Save for Later
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="saved">
                    {savedItems.length === 0 ? (
                      <Card>
                        <CardContent className="text-center py-12">
                          <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-600 mb-2">No saved items</h3>
                          <p className="text-gray-500">Items you save for later will appear here</p>
                        </CardContent>
                      </Card>
                    ) : (
                      <div className="space-y-4">
                        {savedItems.map((item) => (
                          <Card key={item.id} className="overflow-hidden">
                            <CardContent className="p-6">
                              <div className="flex gap-4">
                                {/* Product Image */}
                                <div className="flex-shrink-0">
                                  <Link href={`/shop/${item.productId}`}>
                                    <img
                                      src={getProductImage(item)}
                                      alt={item.productName}
                                      className="w-24 h-24 object-cover rounded-lg border"
                                    />
                                  </Link>
                                </div>

                                {/* Product Details */}
                                <div className="flex-1 min-w-0">
                                  <div className="flex justify-between items-start mb-2">
                                    <div>
                                      <Link href={`/shop/${item.productId}`}>
                                        <h3 className="font-semibold text-lg hover:text-primary transition-colors line-clamp-1">
                                          {item.productName}
                                        </h3>
                                      </Link>
                                      {item.productBrand && (
                                        <p className="text-sm text-gray-500">{item.productBrand}</p>
                                      )}
                                    </div>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => handleRemoveItem(item.id)}
                                      disabled={updating === item.id}
                                      className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                    >
                                      {updating === item.id ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <Trash2 className="h-4 w-4" />
                                      )}
                                    </Button>
                                  </div>

                                  {/* Price */}
                                  <div className="flex items-center gap-2 mb-4">
                                    <span className="text-xl font-bold text-primary">
                                      ₹{item.productPrice.toLocaleString()}
                                    </span>
                                    {item.originalPrice && item.originalPrice > item.productPrice && (
                                      <span className="text-sm text-gray-500 line-through">
                                        ₹{item.originalPrice.toLocaleString()}
                                      </span>
                                    )}
                                  </div>

                                  {/* Actions */}
                                  <div className="flex gap-2">
                                    <Button
                                      onClick={() => handleMoveToCart(item.id)}
                                      disabled={updating === item.id}
                                      size="sm"
                                    >
                                      <ShoppingCart className="h-4 w-4 mr-2" />
                                      Move to Cart
                                    </Button>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    )}
                  </TabsContent>
                </div>

                {/* Order Summary */}
                {cartSummary.totalItems > 0 && activeTab === 'cart' && (
                  <div className="lg:col-span-1">
                    <Card className="sticky top-4">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Package className="h-5 w-5" />
                          Order Summary
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {/* Promo Code */}
                        <div>
                          <label className="text-sm font-medium text-gray-700 mb-2 block">
                            Promo Code
                          </label>
                          <div className="flex gap-2">
                            <Input
                              placeholder="Enter promo code"
                              value={promoCode}
                              onChange={(e) => setPromoCode(e.target.value)}
                              className="flex-1"
                            />
                            <Button variant="outline" size="sm">
                              Apply
                            </Button>
                          </div>
                        </div>

                        <Separator />

                        {/* Summary Details */}
                        <div className="space-y-3">
                          <div className="flex justify-between text-sm">
                            <span>Subtotal ({cartSummary.totalQuantity} items)</span>
                            <span>₹{cartSummary.subtotal.toLocaleString()}</span>
                          </div>

                          {cartSummary.totalDiscount > 0 && (
                            <div className="flex justify-between text-sm text-green-600">
                              <span>Discount</span>
                              <span>-₹{cartSummary.totalDiscount.toLocaleString()}</span>
                            </div>
                          )}

                          <div className="flex justify-between text-sm">
                            <span>Shipping</span>
                            <span className="text-green-600">FREE</span>
                          </div>

                          <Separator />

                          <div className="flex justify-between text-lg font-bold">
                            <span>Total</span>
                            <span className="text-primary">₹{cartSummary.total.toLocaleString()}</span>
                          </div>
                        </div>

                        {/* Checkout Button */}
                        <Link href="/checkout">
                          <Button className="w-full" size="lg">
                            <CreditCard className="h-5 w-5 mr-2" />
                            Proceed to Checkout
                          </Button>
                        </Link>

                        {/* Trust Badges */}
                        <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                          <div className="flex items-center gap-2 text-xs text-gray-600">
                            <Shield className="h-4 w-4 text-green-600" />
                            <span>Secure Payment</span>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-gray-600">
                            <Truck className="h-4 w-4 text-blue-600" />
                            <span>Free Shipping</span>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-gray-600">
                            <Gift className="h-4 w-4 text-purple-600" />
                            <span>Gift Wrapping</span>
                          </div>
                          <div className="flex items-center gap-2 text-xs text-gray-600">
                            <Percent className="h-4 w-4 text-orange-600" />
                            <span>Best Prices</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}
              </div>
            </Tabs>
          )}
        </div>
      </section>

      <Footer />
    </div>
  )
}
