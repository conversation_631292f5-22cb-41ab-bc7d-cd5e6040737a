"use client"

import { TopHeader } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import OTPRegistrationForm from "@/components/auth/OTPRegistrationForm"
import Link from "next/link"
import { ArrowLeft, Users, Shield, Heart, CheckCircle } from "lucide-react"

export default function RegisterPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-purple-50">
      <TopHeader />
      <Header />
      
      {/* Hero Section */}
      <div className="relative py-20 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            
            {/* Left Side - Branding & Benefits */}
            <div className="space-y-8">
              <div>
                <Link 
                  href="/" 
                  className="inline-flex items-center text-primary hover:text-primary/80 transition-colors mb-6"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Home
                </Link>
                
                <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                  Join
                  <span className="text-primary block">Thirumanam 360</span>
                </h1>
                
                <p className="text-xl text-gray-600 mb-8">
                  Start planning your dream wedding today. Connect with trusted vendors, explore beautiful venues, and create memories that last a lifetime.
                </p>
              </div>

              {/* Registration Benefits */}
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Verified Vendors</h3>
                    <p className="text-gray-600">Access to 5,000+ verified wedding vendors across India</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Shield className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Secure & Private</h3>
                    <p className="text-gray-600">Your personal information is protected with bank-level security</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Heart className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">Personalized Experience</h3>
                    <p className="text-gray-600">Get recommendations tailored to your style and budget</p>
                  </div>
                </div>
              </div>

              {/* What You Get */}
              <div className="bg-white rounded-lg p-6 border border-gray-200">
                <h3 className="font-semibold text-gray-900 mb-4">What you get with your account:</h3>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-gray-700">Free wedding planning tools</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-gray-700">Direct contact with vendors</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-gray-700">Exclusive deals and discounts</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-gray-700">Wedding inspiration gallery</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-gray-700">Budget tracking tools</span>
                  </div>
                </div>
              </div>

              {/* Trust Indicators */}
              <div className="grid grid-cols-3 gap-6 pt-8 border-t border-gray-200">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">10K+</div>
                  <div className="text-sm text-gray-600">Happy Couples</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">5K+</div>
                  <div className="text-sm text-gray-600">Trusted Vendors</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">50+</div>
                  <div className="text-sm text-gray-600">Cities</div>
                </div>
              </div>
            </div>

            {/* Right Side - Registration Form */}
            <div className="flex justify-center lg:justify-end">
              <div className="w-full max-w-lg">
                <OTPRegistrationForm />
                
                {/* Additional Links */}
                <div className="mt-6 text-center space-y-4">
                  <div className="text-sm text-gray-600">
                    Already have an account?{' '}
                    <Link href="/login" className="text-primary hover:text-primary/80 font-medium">
                      Sign in here
                    </Link>
                  </div>
                  
                  {/* Business Account Sign Up */}
                  <div className="text-sm text-gray-600">
                    Are you a vendor?{' '}
                    <Link href="/vendor-login" className="text-primary hover:text-primary/80 font-medium">
                      Business Sign Up
                    </Link>
                  </div>
                  
                  <div className="flex items-center justify-center space-x-4 text-xs text-gray-500">
                    <Link href="/privacy" className="hover:text-gray-700">Privacy Policy</Link>
                    <span>•</span>
                    <Link href="/terms" className="hover:text-gray-700">Terms of Service</Link>
                    <span>•</span>
                    <Link href="/contact" className="hover:text-gray-700">Support</Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Registration Methods Info */}
      <div className="bg-white border-t border-gray-200 py-12">
        <div className="container mx-auto max-w-4xl px-4">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Quick & Easy Registration</h2>
            <p className="text-gray-600">Choose the method that works best for you</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="text-center p-6 rounded-lg bg-green-50 border border-green-200">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Users className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Email Registration</h3>
              <p className="text-sm text-gray-600">Register with your email address and verify via email OTP</p>
            </div>
            
            <div className="text-center p-6 rounded-lg bg-purple-50 border border-purple-200">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Heart className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Mobile Registration</h3>
              <p className="text-sm text-gray-600">Quick registration with your mobile number and SMS OTP</p>
            </div>
          </div>
          
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500">
              Both methods are secure and take less than 2 minutes to complete
            </p>
          </div>
        </div>
      </div>

      {/* Security & Privacy */}
      <div className="bg-gray-50 py-12">
        <div className="container mx-auto max-w-4xl px-4">
          <div className="text-center mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Your Privacy Matters</h2>
            <p className="text-gray-600">We take your security and privacy seriously</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Shield className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Secure Data</h3>
              <p className="text-sm text-gray-600">All data encrypted with industry-standard security</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">No Spam</h3>
              <p className="text-sm text-gray-600">We never share your information with third parties</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Heart className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">Your Control</h3>
              <p className="text-sm text-gray-600">Manage your privacy settings and data anytime</p>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}
