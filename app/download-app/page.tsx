"use client"

import { <PERSON>Header } from "@/components/top-header"
import { <PERSON><PERSON> } from "@/components/header"
import { <PERSON><PERSON> } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { SimpleSEO } from "@/components/seo/SimpleSEO"
import { 
  Smartphone, 
  Download, 
  Star, 
  Bell, 
  Heart, 
  Search, 
  Calendar,
  MapPin,
  Users,
  Camera,
  ShoppingBag,
  CheckCircle,
  Apple,
  PlayCircle
} from "lucide-react"
import Link from "next/link"
import { useState } from "react"

export default function DownloadAppPage() {
  const [email, setEmail] = useState('')
  const [isSubscribed, setIsSubscribed] = useState(false)

  const handleNotifyMe = (e: React.FormEvent) => {
    e.preventDefault()
    if (email) {
      setIsSubscribed(true)
      // Here you would typically send the email to your backend
      console.log('Notify email:', email)
    }
  }

  const features = [
    {
      icon: Search,
      title: "Smart Search",
      description: "Find vendors, venues, and services with AI-powered search"
    },
    {
      icon: Heart,
      title: "Save Favorites",
      description: "Create wishlists and save your favorite vendors and venues"
    },
    {
      icon: Calendar,
      title: "Wedding Planner",
      description: "Organize your wedding timeline with our built-in planner"
    },
    {
      icon: Bell,
      title: "Real-time Notifications",
      description: "Get instant updates on bookings, offers, and reminders"
    },
    {
      icon: Camera,
      title: "Photo Gallery",
      description: "Browse stunning wedding photos and get inspired"
    },
    {
      icon: ShoppingBag,
      title: "Shop on the Go",
      description: "Purchase wedding essentials directly from the app"
    }
  ]

  const benefits = [
    "Access to 10,000+ verified vendors across India",
    "Exclusive mobile-only deals and discounts",
    "Offline access to your saved vendors and venues",
    "One-tap calling and messaging to vendors",
    "Real-time availability checking",
    "Secure payment gateway integration"
  ]

  return (
    <>
      <SimpleSEO
        title="Download Thirumanam 360 Mobile App - Coming Soon"
        description="Get ready for the ultimate wedding planning experience on your mobile. Download the Thirumanam 360 app to plan your dream wedding anytime, anywhere."
        keywords="thirumanam 360 app, wedding planning app, mobile app, download app, wedding app india, tamil wedding app"
        image="/hero_image_1.webp"
        url="/download-app"
      />
      <div className="min-h-screen bg-background">
        <TopHeader />
        <Header />

        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary via-accent to-secondary text-primary-foreground py-20">
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-4xl mx-auto">
              <Smartphone className="w-20 h-20 mx-auto mb-6 text-primary-foreground/90" />
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-primary-foreground">
                Coming Soon to Your Phone
              </h1>
              <p className="text-xl md:text-2xl mb-8 text-primary-foreground/90">
                The ultimate wedding planning experience is coming to mobile.
                Plan your dream wedding anytime, anywhere.
              </p>
              
              {/* Notify Me Form */}
              <div className="max-w-md mx-auto">
                {!isSubscribed ? (
                  <form onSubmit={handleNotifyMe} className="flex gap-3">
                    <input
                      type="email"
                      placeholder="Enter your email to get notified"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="flex-1 px-4 py-3 rounded-lg text-foreground placeholder-muted-foreground bg-background border border-border"
                      required
                    />
                    <Button type="submit" className="bg-background text-foreground hover:bg-muted px-6">
                      <Bell className="w-4 h-4 mr-2" />
                      Notify Me
                    </Button>
                  </form>
                ) : (
                  <div className="bg-background/20 backdrop-blur-sm rounded-lg p-4 border border-border/20">
                    <CheckCircle className="w-6 h-6 mx-auto mb-2 text-primary-foreground" />
                    <p className="text-lg font-semibold text-primary-foreground">Thank you!</p>
                    <p className="text-primary-foreground/90">We'll notify you when the app is ready to download.</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* App Store Badges Preview */}
        <section className="py-16 bg-muted/50">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-8 text-foreground">Available Soon On</h2>
            <div className="flex justify-center gap-6 mb-8">
              <div className="bg-foreground text-background rounded-lg px-6 py-3 flex items-center gap-3 opacity-50">
                <Apple className="w-8 h-8" />
                <div className="text-left">
                  <div className="text-xs">Download on the</div>
                  <div className="text-lg font-semibold">App Store</div>
                </div>
              </div>
              <div className="bg-foreground text-background rounded-lg px-6 py-3 flex items-center gap-3 opacity-50">
                <PlayCircle className="w-8 h-8" />
                <div className="text-left">
                  <div className="text-xs">Get it on</div>
                  <div className="text-lg font-semibold">Google Play</div>
                </div>
              </div>
            </div>
            <p className="text-muted-foreground">Coming to iOS and Android devices</p>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-20 bg-background">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-foreground">
                Everything You Need for Your Wedding
              </h2>
              <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
                Our mobile app will bring all the power of Thirumanam 360 to your fingertips
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow bg-card border-border">
                  <CardContent className="pt-6">
                    <feature.icon className="w-12 h-12 mx-auto mb-4 text-primary" />
                    <h3 className="text-xl font-semibold mb-3 text-card-foreground">{feature.title}</h3>
                    <p className="text-muted-foreground">{feature.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-20 bg-muted/30">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-16">
                <h2 className="text-3xl md:text-4xl font-bold mb-4 text-foreground">
                  Why Choose Our Mobile App?
                </h2>
                <p className="text-xl text-muted-foreground">
                  Get exclusive benefits and features designed for mobile users
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <CheckCircle className="w-6 h-6 text-secondary mt-1 flex-shrink-0" />
                    <p className="text-lg text-foreground">{benefit}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-primary to-accent text-primary-foreground">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-primary-foreground">
              Be the First to Experience Mobile Wedding Planning
            </h2>
            <p className="text-xl mb-8 max-w-2xl mx-auto text-primary-foreground/90">
              Join thousands of couples who are already planning their dream weddings with Thirumanam 360
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/vendors">
                <Button className="bg-background text-foreground hover:bg-muted px-8 py-3">
                  <Users className="w-5 h-5 mr-2" />
                  Browse Vendors Now
                </Button>
              </Link>
              <Link href="/venues">
                <Button className="bg-background text-foreground hover:bg-muted px-8 py-3">
                  <MapPin className="w-5 h-5 mr-2" />
                  Find Venues
                </Button>
              </Link>
            </div>
          </div>
        </section>

        <Footer />
      </div>
    </>
  )
}
