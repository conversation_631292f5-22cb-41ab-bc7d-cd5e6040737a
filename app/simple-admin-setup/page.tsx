'use client';

import React, { useState, useEffect } from 'react';
import { TopHeader } from '@/components/top-header';
import { Header } from '@/components/header';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Shield, 
  User,
  CheckCircle,
  XCircle,
  AlertCircle,
  Database
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { generateClient } from 'aws-amplify/api';
import { createUserProfile, updateUserProfile } from '@/src/graphql/mutations';
import { userProfilesByUserId } from '@/src/graphql/queries';

const client = generateClient();

export default function SimpleAdminSetupPage() {
  const { user, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(false);
  const [existingProfile, setExistingProfile] = useState<any>(null);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  });

  useEffect(() => {
    if (isAuthenticated && user) {
      checkExistingProfile();
      setFormData(prev => ({
        ...prev,
        email: user.signInDetails?.loginId || ''
      }));
    }
  }, [isAuthenticated, user]);

  const checkExistingProfile = async () => {
    if (!user) return;

    try {
      const result = await client.graphql({
        query: userProfilesByUserId,
        variables: {
          userId: user.userId,
          limit: 1
        }
      });

      const profiles = result.data?.userProfilesByUserId?.items || [];
      if (profiles.length > 0) {
        setExistingProfile(profiles[0]);
        setFormData(prev => ({
          ...prev,
          firstName: profiles[0].firstName || '',
          lastName: profiles[0].lastName || '',
          email: profiles[0].email || '',
          phone: profiles[0].phone || ''
        }));

        setMessage({
          type: 'info',
          text: 'User profile found! You can update it or use it for admin access.'
        });
      }
    } catch (error) {
      console.error('Error checking existing profile:', error);
    }
  };

  const createBasicProfile = async () => {
    if (!user) {
      setMessage({ type: 'error', text: 'Please log in first' });
      return;
    }

    if (!formData.firstName.trim() || !formData.lastName.trim() || !formData.email.trim()) {
      setMessage({ type: 'error', text: 'Please fill in all required fields' });
      return;
    }

    setLoading(true);
    setMessage(null);

    try {
      if (existingProfile) {
        // Update existing profile
        const updateInput = {
          id: existingProfile.id,
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone || null
        };

        const result = await client.graphql({
          query: updateUserProfile,
          variables: {
            input: updateInput
          }
        });

        setMessage({
          type: 'success',
          text: 'Successfully updated your profile! You can now access admin features.'
        });
        
        setExistingProfile(result.data?.updateUserProfile);
      } else {
        // Create new profile with basic fields only
        const createInput = {
          userId: user.userId,
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone || null
        };

        const result = await client.graphql({
          query: createUserProfile,
          variables: {
            input: createInput
          }
        });

        setMessage({
          type: 'success',
          text: 'Successfully created your profile! You can now access admin features.'
        });
        
        setExistingProfile(result.data?.createUserProfile);
      }
    } catch (error: any) {
      console.error('Error creating/updating profile:', error);
      setMessage({
        type: 'error',
        text: error.message || 'Failed to create/update profile'
      });
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Please Log In</h2>
            <p className="text-gray-600 mb-4">You need to be logged in to set up your profile.</p>
            <Button asChild>
              <a href="/login">Go to Login</a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Simple Admin Setup</h1>
          <p className="text-gray-600">Create your basic user profile to access admin features</p>
        </div>

        {/* Status Card */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              Database Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <span className="text-yellow-800 font-medium">Admin Fields Deployment</span>
                <span className="text-yellow-600 text-sm">Pending</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <span className="text-green-800 font-medium">Basic Profile Fields</span>
                <span className="text-green-600 text-sm">Available</span>
              </div>
              <div className="text-sm text-gray-600">
                <p><strong>Current Approach:</strong> Create a basic user profile first. Admin fields can be added later when deployment completes.</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Current User Info */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Current User Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p><strong>User ID:</strong> {user?.userId}</p>
              <p><strong>Login ID:</strong> {user?.signInDetails?.loginId}</p>
              <p><strong>Profile Status:</strong> {existingProfile ? 'Profile exists' : 'No profile yet'}</p>
            </div>
          </CardContent>
        </Card>

        {/* Message */}
        {message && (
          <div className={`mb-6 p-4 rounded-lg ${
            message.type === 'success' 
              ? 'bg-green-50 border border-green-200 text-green-800'
              : message.type === 'error'
              ? 'bg-red-50 border border-red-200 text-red-800'
              : 'bg-blue-50 border border-blue-200 text-blue-800'
          }`}>
            <div className="flex items-center">
              {message.type === 'success' ? (
                <CheckCircle className="h-5 w-5 mr-2" />
              ) : message.type === 'error' ? (
                <XCircle className="h-5 w-5 mr-2" />
              ) : (
                <AlertCircle className="h-5 w-5 mr-2" />
              )}
              {message.text}
            </div>
          </div>
        )}

        {/* Profile Form */}
        <Card>
          <CardHeader>
            <CardTitle>
              {existingProfile ? 'Update Profile' : 'Create Profile'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                  placeholder="Enter your first name"
                />
              </div>
              
              <div>
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                  placeholder="Enter your last name"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="Enter your email"
              />
            </div>
            
            <div>
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="Enter your phone number (optional)"
              />
            </div>

            <Button 
              onClick={createBasicProfile}
              disabled={loading}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              {loading 
                ? 'Processing...' 
                : existingProfile 
                  ? 'Update Profile' 
                  : 'Create Profile'
              }
            </Button>
          </CardContent>
        </Card>

        {/* Success Actions */}
        {message?.type === 'success' && (
          <Card className="mt-6 border-green-200 bg-green-50">
            <CardContent className="p-4">
              <div className="text-green-800">
                <p className="font-semibold mb-2">✅ Profile Created Successfully!</p>
                <p className="text-sm mb-2">You can now access:</p>
                <div className="flex flex-wrap gap-2">
                  <Button size="sm" asChild>
                    <a href="/admin">Admin Dashboard</a>
                  </Button>
                  <Button size="sm" variant="outline" asChild>
                    <a href="/dashboard/admin-reviews">Review Management</a>
                  </Button>
                  <Button size="sm" variant="outline" asChild>
                    <a href="/reviews">Platform Reviews</a>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Next Steps */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Next Steps</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <p><strong>1. Create Profile:</strong> Use the form above to create your basic user profile</p>
              <p><strong>2. Access Admin Features:</strong> Once profile is created, you can access admin dashboards</p>
              <p><strong>3. Deploy Admin Fields:</strong> When ready, deploy the full schema with admin fields</p>
              <p><strong>4. Update Profile:</strong> Add admin privileges to your profile after deployment</p>
              
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-blue-800">
                  <strong>Note:</strong> This creates a basic profile that works with the current database schema. 
                  Admin-specific fields will be added when the schema deployment completes.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
