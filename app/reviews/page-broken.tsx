"use client"

import { useState, useEffect } from 'react'
import { TopHeader } from '@/components/top-header'
import { Header } from '@/components/header'
import { useSafeTranslation } from '@/hooks/use-safe-translation'
import { useStateContext } from '@/contexts/StateContext'
import { useReviews, useReviewStats, useReviewSubmission } from '@/src/hooks/useReviews'
import { useAuth } from '@/contexts/AuthContext'
import { ReviewService } from '@/src/services/reviewService'
import { showToast, toastMessages } from '@/lib/toast'
import ReviewTypeSelector from '@/components/review-type-selector'
import PlatformReviewForm from '@/components/platform-review-form'

interface Review {
  id: string
  name: string
  location: string
  rating: number
  title: string
  review: string
  createdAt: string
  verified: boolean
  weddingDate: string
  category: string
  wouldRecommend: boolean
}

export default function ReviewsPage() {
  const { t } = useSafeTranslation()
  const { selectedState } = useStateContext()
  const { isAuthenticated } = useAuth()
  const [activeTab, setActiveTab] = useState<'read' | 'write'>('read')
  const [reviewStep, setReviewStep] = useState<'select' | 'platform' | 'product'>('select')
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    location: '',
    weddingDate: '',
    category: 'PLATFORM',
    rating: 5,
    title: '',
    review: '',
    wouldRecommend: true
  })

  // Use custom hooks for API operations
  const {
    reviews,
    loading: reviewsLoading,
    error: reviewsError,
    hasMore,
    loadMore,
    refresh
  } = useReviews({ autoLoad: true, limit: 10 })

  const {
    stats: reviewStats,
    loading: statsLoading
  } = useReviewStats()

  const {
    submitReview,
    submitting,
    submitError,
    submitSuccess,
    clearMessages
  } = useReviewSubmission()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked
      setFormData(prev => ({
        ...prev,
        [name]: checked
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }
  }

  const handleRatingChange = (rating: number) => {
    setFormData(prev => ({
      ...prev,
      rating
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!isAuthenticated) {
      showToast.error(toastMessages.auth.authRequired)
      return
    }

    // Client-side validation
    const requiredFields = [
      { field: 'name', label: 'Name' },
      { field: 'email', label: 'Email' },
      { field: 'location', label: 'Location' },
      { field: 'weddingDate', label: 'Wedding Date' },
      { field: 'title', label: 'Review Title' },
      { field: 'review', label: 'Review' }
    ];

    const missingFields = requiredFields.filter(({ field }) =>
      !formData[field] || formData[field].toString().trim() === ''
    );

    if (missingFields.length > 0) {
      showToast.error(`Please fill in the following required fields: ${missingFields.map(f => f.label).join(', ')}`);
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      showToast.error(toastMessages.validation.invalidEmail);
      return;
    }

    // Validate wedding date
    const weddingDate = new Date(formData.weddingDate);
    if (isNaN(weddingDate.getTime())) {
      showToast.error(toastMessages.validation.invalidDate);
      return;
    }

    // Validate rating
    if (formData.rating < 1 || formData.rating > 5) {
      showToast.error(toastMessages.validation.invalidRating);
      return;
    }

    try {
      const result = await submitReview(formData)

      if (result.success) {
        // Reset form on success
        setFormData({
          name: '',
          email: '',
          location: '',
          weddingDate: '',
          category: 'PLATFORM',
          rating: 5,
          title: '',
          review: '',
          wouldRecommend: true
        })
        // Switch to read tab after successful submission
        setTimeout(() => {
          setActiveTab('read')
          refresh() // Refresh reviews list
        }, 2000)
      }
    } catch (error) {
      console.error('Error submitting review:', error)
    }
  }



  // Clear messages when switching tabs
  useEffect(() => {
    clearMessages()
  }, [activeTab, clearMessages])

  // Handle review type selection
  const handleReviewTypeSelected = (type: 'platform' | 'product', data?: any) => {
    if (type === 'platform') {
      setReviewStep('platform')
    } else {
      // For product reviews, redirect to product selection
      setReviewStep('product')
    }
  };

  // Handle going back to type selection
  const handleBackToSelection = () => {
    setReviewStep('select')
  };

  // Handle successful review submission
  const handleReviewSubmitted = () => {
    setActiveTab('read')
    setReviewStep('select')
    refresh() // Refresh reviews list
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <svg
            key={star}
            className={`w-5 h-5 ${star <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />
      
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary to-primary-dark text-white py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {t('reviews.title', 'Platform Reviews')}
            </h1>
            <p className="text-xl opacity-90 max-w-2xl mx-auto">
              {t('reviews.subtitle', 'Read what our happy couples say about their overall wedding planning experience with Thirumanam 360')}
            </p>
            <p className="text-sm opacity-75 mt-2">
              Product-specific reviews are available on individual shop, venue, and vendor pages
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg p-1 shadow-lg">
            <button
              onClick={() => setActiveTab('read')}
              className={`px-6 py-3 rounded-md font-semibold transition-colors ${
                activeTab === 'read'
                  ? 'bg-primary text-white'
                  : 'text-gray-600 hover:text-primary'
              }`}
            >
              {t('reviews.tabs.read', 'Read Reviews')}
            </button>
            <button
              onClick={() => setActiveTab('write')}
              className={`px-6 py-3 rounded-md font-semibold transition-colors ${
                activeTab === 'write'
                  ? 'bg-primary text-white'
                  : 'text-gray-600 hover:text-primary'
              }`}
            >
              {t('reviews.tabs.write', 'Write a Review')}
            </button>

          </div>
        </div>

        {/* Read Reviews Tab */}
        {activeTab === 'read' && (
          <div>
            {/* Reviews Stats */}
            <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
              {statsLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                  <p className="text-gray-600 mt-4">Loading statistics...</p>
                </div>
              ) : (
                <div className="text-center">
                  <div className="flex items-center justify-center mb-4">
                    <div className="text-5xl font-bold text-primary mr-4">
                      {reviewStats.averageRating.toFixed(1)}
                    </div>
                    <div>
                      {renderStars(Math.round(reviewStats.averageRating))}
                      <p className="text-gray-600 mt-1">
                        {t('reviews.stats.based', `Based on ${reviewStats.totalReviews} reviews`)}
                      </p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">{reviewStats.satisfactionRate}%</div>
                      <p className="text-gray-600">{t('reviews.stats.satisfaction', 'Satisfaction Rate')}</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">{reviewStats.totalReviews}</div>
                      <p className="text-gray-600">{t('reviews.stats.total', 'Total Reviews')}</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">{reviewStats.recommendationRate}%</div>
                      <p className="text-gray-600">{t('reviews.stats.recommend', 'Would Recommend')}</p>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-primary">{reviewStats.averageRating.toFixed(1)}</div>
                      <p className="text-gray-600">{t('reviews.stats.average', 'Average Rating')}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Reviews List */}
            {reviewsLoading && reviews.length === 0 ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                <p className="text-gray-600 mt-4">Loading reviews...</p>
              </div>
            ) : reviewsError ? (
              <div className="text-center py-12">
                <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                  <p className="text-red-800">Error loading reviews: {reviewsError}</p>
                  <button
                    onClick={refresh}
                    className="mt-4 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Try Again
                  </button>
                </div>
              </div>
            ) : reviews.length === 0 ? (
              <div className="text-center py-12">
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-8">
                  <p className="text-gray-600 text-lg">No reviews available yet.</p>
                  <p className="text-gray-500 mt-2">Be the first to share your experience!</p>
                  <button
                    onClick={() => setActiveTab('write')}
                    className="mt-4 bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark transition-colors"
                  >
                    Write a Review
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {reviews.map((review) => (
                  <div key={review.id} className="bg-white rounded-2xl shadow-lg p-8">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <div className="flex items-center mb-2">
                          <h3 className="font-semibold text-lg text-gray-900 mr-3">{review.name}</h3>
                          {review.verified && (
                            <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full flex items-center">
                              <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                              </svg>
                              {t('reviews.verified', 'Verified')}
                            </span>
                          )}
                          {review.wouldRecommend && (
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full ml-2">
                              {t('reviews.recommends', 'Recommends')}
                            </span>
                          )}
                        </div>
                        <p className="text-gray-600 text-sm">
                          {review.location} • {t('reviews.weddingDate', 'Wedding')}: {new Date(review.weddingDate).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit'
                          })}
                        </p>
                      </div>
                      <div className="text-right">
                        {renderStars(review.rating)}
                        <p className="text-gray-500 text-sm mt-1">
                          {new Date(review.createdAt).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit'
                          })}
                        </p>
                      </div>
                    </div>
                    <h4 className="font-semibold text-gray-900 mb-3">{review.title}</h4>
                    <p className="text-gray-700 leading-relaxed">{review.review}</p>
                    <div className="mt-4 text-sm text-gray-500">
                      Category: {review.category.charAt(0).toUpperCase() + review.category.slice(1).toLowerCase()}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Load More Button */}
            {hasMore && reviews.length > 0 && (
              <div className="text-center mt-8">
                <button
                  onClick={loadMore}
                  disabled={reviewsLoading}
                  className="bg-primary text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {reviewsLoading ? (
                    <div className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading...
                    </div>
                  ) : (
                    t('reviews.loadMore', 'Load More Reviews')
                  )}
                </button>
              </div>
            )}
          </div>
        )}



        {/* Write Review Tab */}
        {activeTab === 'write' && (
          <div className="max-w-5xl mx-auto">
            {reviewStep === 'select' && (
              <ReviewTypeSelector
                onTypeSelected={handleReviewTypeSelected}
              />
            )}

            {reviewStep === 'platform' && (
              <PlatformReviewForm
                onBack={handleBackToSelection}
                onSubmitted={handleReviewSubmitted}
              />
            )}

            {reviewStep === 'product' && (
              <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Product/Service Review</h2>
                <p className="text-gray-600 mb-6">
                  To review a specific product or service, please visit the product, vendor, or venue page
                  and use the "Write a Review" button there.
                </p>
                <div className="space-x-4">
                  <button
                    onClick={handleBackToSelection}
                    className="px-6 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    Back to Selection
                  </button>
                  <a
                    href="/shop"
                    className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark"
                  >
                    Browse Products
                  </a>
                  <a
                    href="/vendors"
                    className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark"
                  >
                    Browse Vendors
                  </a>
                </div>
              </div>
            )}
              
              {submitSuccess && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <p className="text-green-800">
                      {t('reviews.write.success', 'Thank you for your review! It will be published after verification.')}
                    </p>
                  </div>
                </div>
              )}

              {submitError && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    <p className="text-red-800">
                      {submitError}
                    </p>
                  </div>
                </div>
              )}

              {!isAuthenticated && (
                <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <p className="text-yellow-800">
                      Please log in to submit a review.
                    </p>
                  </div>
                </div>
              )}



              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                      {t('reviews.write.name', 'Your Name')} *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder={t('reviews.write.namePlaceholder', 'Enter your name or couple names')}
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                      {t('reviews.write.email', 'Email Address')} *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder={t('reviews.write.emailPlaceholder', 'Enter your email address')}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                      {t('reviews.write.location', 'Wedding Location')} *
                    </label>
                    <input
                      type="text"
                      id="location"
                      name="location"
                      value={formData.location}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder={t('reviews.write.locationPlaceholder', 'City where your wedding took place')}
                    />
                  </div>

                  <div>
                    <label htmlFor="weddingDate" className="block text-sm font-medium text-gray-700 mb-2">
                      {t('reviews.write.weddingDate', 'Wedding Date')} *
                    </label>
                    <input
                      type="date"
                      id="weddingDate"
                      name="weddingDate"
                      value={formData.weddingDate}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                    {t('reviews.write.category', 'Review Category')}
                  </label>
                  <select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="PLATFORM">{t('reviews.write.platform', 'Platform Experience')}</option>
                    <option value="VENDOR">{t('reviews.write.vendors', 'Vendor Services')}</option>
                    <option value="VENUE">{t('reviews.write.venue', 'Venue Services')}</option>
                    <option value="SHOP">{t('reviews.write.shop', 'Shop Products')}</option>
                    <option value="PRODUCT">{t('reviews.write.product', 'Product Quality')}</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('reviews.write.rating', 'Overall Rating')} *
                  </label>
                  <div className="flex items-center space-x-2">
                    {renderStars(formData.rating, true, handleRatingChange)}
                    <span className="text-gray-600 ml-2">({formData.rating}/5)</span>
                  </div>
                </div>

                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                    {t('reviews.write.title', 'Review Title')} *
                  </label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder={t('reviews.write.titlePlaceholder', 'Give your review a title')}
                  />
                </div>

                <div>
                  <label htmlFor="review" className="block text-sm font-medium text-gray-700 mb-2">
                    {t('reviews.write.review', 'Your Review')} *
                  </label>
                  <textarea
                    id="review"
                    name="review"
                    value={formData.review}
                    onChange={handleInputChange}
                    required
                    rows={6}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder={t('reviews.write.reviewPlaceholder', 'Share your experience with Thirumanam 360...')}
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="wouldRecommend"
                    name="wouldRecommend"
                    checked={formData.wouldRecommend}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  />
                  <label htmlFor="wouldRecommend" className="ml-2 text-sm text-gray-700">
                    {t('reviews.write.recommend', 'I would recommend Thirumanam 360 to other couples')}
                  </label>
                </div>

                <button
                  type="submit"
                  disabled={submitting || !isAuthenticated}
                  className="w-full bg-primary text-white py-3 px-6 rounded-lg font-semibold hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {submitting ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {t('reviews.write.submitting', 'Submitting...')}
                    </div>
                  ) : !isAuthenticated ? (
                    t('reviews.write.loginRequired', 'Login Required')
                  ) : (
                    t('reviews.write.submit', 'Submit Review')
                  )}
                </button>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
