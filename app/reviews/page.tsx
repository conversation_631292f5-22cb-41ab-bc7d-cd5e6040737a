"use client"

import { useState, useEffect } from 'react'
import { TopHeader } from '@/components/top-header'
import { Header } from '@/components/header'
import { useSafeTranslation } from '@/hooks/use-safe-translation'
import { useStateContext } from '@/contexts/StateContext'
import { useReviews, useReviewStats, useReviewSubmission } from '@/src/hooks/useReviews'
import { useAuth } from '@/contexts/AuthContext'
import { ReviewService } from '@/src/services/reviewService'
import { showToast, toastMessages } from '@/lib/toast'
import ReviewTypeSelector from '@/components/review-type-selector'
import PlatformReviewForm from '@/components/platform-review-form'
import { Footer } from '@/components/footer'

interface Review {
  id: string
  name: string
  location: string
  rating: number
  title: string
  review: string
  createdAt: string
  verified: boolean
  weddingDate: string
  category: string
  wouldRecommend: boolean
}

export default function ReviewsPage() {
  const { t } = useSafeTranslation()
  const { selectedState } = useStateContext()
  const { isAuthenticated } = useAuth()
  const [activeTab, setActiveTab] = useState<'read' | 'write'>('read')
  // Remove reviewStep and related state
  // Remove ReviewTypeSelector and product review logic
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    location: '',
    weddingDate: '',
    category: 'PLATFORM',
    rating: 5,
    title: '',
    review: '',
    wouldRecommend: true
  })

  // Use custom hooks for API operations
  const {
    reviews,
    loading: reviewsLoading,
    error: reviewsError,
    hasMore,
    loadMore,
    refresh
  } = useReviews({ autoLoad: true, limit: 10 })

  const {
    stats: reviewStats,
    loading: statsLoading
  } = useReviewStats()

  const {
    submitReview,
    loading: submissionLoading,
    error: submissionError,
    success: submissionSuccess,
    clearMessages
  } = useReviewSubmission()

  // Clear messages when switching tabs
  useEffect(() => {
    clearMessages()
  }, [activeTab, clearMessages])

  // Handle review type selection
  const handleReviewTypeSelected = (type: 'platform' | 'product', data?: any) => {
    if (type === 'platform') {
      // setReviewStep('platform') // This line is removed
    } else {
      // For product reviews, redirect to product selection // This line is removed
      // setReviewStep('product') // This line is removed
    }
  };

  // Handle going back to type selection
  const handleBackToSelection = () => {
    // setReviewStep('select') // This line is removed
  };

  // Handle successful review submission
  const handleReviewSubmitted = () => {
    setActiveTab('read')
    // setReviewStep('select') // This line is removed
    refresh() // Refresh reviews list
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <svg
            key={star}
            className={`w-5 h-5 ${star <= rating ? 'text-yellow-400' : 'text-gray-300'}`}
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary to-primary-dark text-white py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              {t('reviews.title', 'Platform Reviews')}
            </h1>
            <p className="text-xl opacity-90 max-w-2xl mx-auto">
              {t('reviews.subtitle', 'Read what our happy couples say about their overall wedding planning experience with Thirumanam 360')}
            </p>
            <p className="text-sm opacity-75 mt-2">
              Product-specific reviews are available on individual shop, venue, and vendor pages
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg shadow-sm p-1 flex">
            <button
              onClick={() => setActiveTab('read')}
              className={`px-6 py-3 rounded-md font-medium transition-colors ${
                activeTab === 'read'
                  ? 'bg-primary text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {t('reviews.tabs.read', 'Read Reviews')}
            </button>
            <button
              onClick={() => setActiveTab('write')}
              className={`px-6 py-3 rounded-md font-medium transition-colors ${
                activeTab === 'write'
                  ? 'bg-primary text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {t('reviews.tabs.write', 'Write Review')}
            </button>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'read' && (
          <div className="space-y-8">
            {/* Stats Section */}
            {reviewStats && !statsLoading && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white rounded-lg shadow-sm p-6 text-center">
                  <div className="text-3xl font-bold text-primary mb-2">
                    {reviewStats.totalReviews}
                  </div>
                  <div className="text-gray-600">Total Reviews</div>
                </div>
                <div className="bg-white rounded-lg shadow-sm p-6 text-center">
                  <div className="text-3xl font-bold text-primary mb-2">
                    {reviewStats.averageRating.toFixed(1)}
                  </div>
                  <div className="text-gray-600">Average Rating</div>
                </div>
                <div className="bg-white rounded-lg shadow-sm p-6 text-center">
                  <div className="text-3xl font-bold text-primary mb-2">
                    {reviewStats.recommendationRate}%
                  </div>
                  <div className="text-gray-600">Would Recommend</div>
                </div>
                <div className="bg-white rounded-lg shadow-sm p-6 text-center">
                  <div className="text-3xl font-bold text-primary mb-2">
                    {reviewStats.verifiedReviews}
                  </div>
                  <div className="text-gray-600">Verified Reviews</div>
                </div>
              </div>
            )}

            {/* Reviews List */}
            <div className="space-y-6">
              {reviewsLoading && reviews.length === 0 ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading reviews...</p>
                </div>
              ) : reviews.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-gray-600 text-lg">No reviews yet. Be the first to share your experience!</p>
                </div>
              ) : (
                reviews.map((review) => (
                  <div key={review.id} className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="font-semibold text-lg text-gray-900">{review.title}</h3>
                        <div className="flex items-center space-x-2 mt-1">
                          {renderStars(review.rating)}
                          <span className="text-sm text-gray-600">
                            by {review.name} from {review.location}
                          </span>
                          {review.verified && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Verified
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(review.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                    <p className="text-gray-700 mb-4">{review.review}</p>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>Wedding Date: {review.weddingDate}</span>
                      <span>Category: {review.category}</span>
                      {review.wouldRecommend && (
                        <span className="text-green-600 font-medium">✓ Recommends</span>
                      )}
                    </div>
                  </div>
                ))
              )}

              {/* Load More Button */}
              {hasMore && (
                <div className="text-center">
                  <button
                    onClick={loadMore}
                    disabled={reviewsLoading}
                    className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-dark disabled:opacity-50"
                  >
                    {reviewsLoading ? 'Loading...' : 'Load More Reviews'}
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'write' && (
          <div className="max-w-2xl mx-auto">
            <PlatformReviewForm
              formData={formData}
              setFormData={setFormData}
              onSubmitted={handleReviewSubmitted}
              loading={submissionLoading}
              error={submissionError}
              success={submissionSuccess}
            />
          </div>
        )}
      </div>
      <Footer />
    </div>
  )
}