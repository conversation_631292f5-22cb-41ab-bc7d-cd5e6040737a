"use client"

import React, { useState, useEffect } from 'react'
import { TopHeader } from "@/components/top-header"
import { Head<PERSON> } from "@/components/header"
import { Footer } from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Heart, Search, MapPin, Star, Trash2, ExternalLink, Loader2, Filter, ChevronLeft, ChevronRight } from "lucide-react"
import { useAuth } from '@/contexts/AuthContext'
import { FavoritesService, FavoriteItem } from '@/lib/services/favoritesService'
import { showToast } from '@/lib/toast'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

export default function FavoritesPage() {
  const { isAuthenticated, user, isLoading: authLoading } = useAuth()
  const router = useRouter()
  const [favorites, setFavorites] = useState<FavoriteItem[]>([])
  const [filteredFavorites, setFilteredFavorites] = useState<FavoriteItem[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [activeTab, setActiveTab] = useState('all')
  const [counts, setCounts] = useState({ vendors: 0, venues: 0, shopItems: 0, total: 0 })
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(12) // 12 items per page for good grid layout

  useEffect(() => {
    if (!authLoading) {
      if (!isAuthenticated) {
        router.push('/login?redirect=/favorites')
        return
      }
      loadFavorites()
      loadCounts()
    }
  }, [isAuthenticated, authLoading])

  useEffect(() => {
    filterFavorites()
    setCurrentPage(1) // Reset to first page when filters change
  }, [favorites, searchQuery, activeTab])

  const loadFavorites = async () => {
    try {
      setLoading(true)
      const result = await FavoritesService.getUserFavorites()
      if (result.success) {
        setFavorites(result.favorites)
      }
    } catch (error) {
      console.error('Error loading favorites:', error)
      showToast.error('Failed to load favorites')
    } finally {
      setLoading(false)
    }
  }

  const loadCounts = async () => {
    try {
      const counts = await FavoritesService.getFavoritesCount()
      setCounts(counts)
    } catch (error) {
      console.error('Error loading counts:', error)
    }
  }

  const filterFavorites = () => {
    let filtered = favorites

    // Filter by tab
    if (activeTab !== 'all') {
      const typeMap = {
        vendors: 'VENDOR',
        venues: 'VENUE',
        shops: 'SHOP_ITEM'
      }
      filtered = filtered.filter(fav => fav.entityType === typeMap[activeTab])
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(fav =>
        fav.entityName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        fav.entityLocation?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        fav.entityCity?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    setFilteredFavorites(filtered)
  }

  const handleRemoveFavorite = async (favoriteId: string) => {
    try {
      const result = await FavoritesService.removeFromFavorites(favoriteId)
      if (result.success) {
        setFavorites(prev => prev.filter(fav => fav.id !== favoriteId))
        showToast.success('Removed from favorites')
        loadCounts()
      }
    } catch (error) {
      console.error('Error removing favorite:', error)
      showToast.error('Failed to remove from favorites')
    }
  }

  const getEntityUrl = (favorite: FavoriteItem) => {
    switch (favorite.entityType) {
      case 'VENDOR':
        return `/vendors/${favorite.entityId}`
      case 'VENUE':
        return `/venues/${favorite.entityId}`
      case 'SHOP_ITEM':
        return `/shop/${favorite.entityId}`
      default:
        return '#'
    }
  }

  const getEntityTypeLabel = (type: string) => {
    switch (type) {
      case 'VENDOR':
        return 'Vendor'
      case 'VENUE':
        return 'Venue'
      case 'SHOP_ITEM':
        return 'Shop Item'
      default:
        return type
    }
  }

  // Pagination calculations
  const totalPages = Math.ceil(filteredFavorites.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentFavorites = filteredFavorites.slice(startIndex, endIndex)

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top of favorites section
    window.scrollTo({ top: 400, behavior: 'smooth' })
  }

  const generatePageNumbers = () => {
    const pages = []
    const maxVisiblePages = 5

    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i)
      }
    } else {
      if (currentPage <= 3) {
        for (let i = 1; i <= 4; i++) pages.push(i)
        pages.push('...')
        pages.push(totalPages)
      } else if (currentPage >= totalPages - 2) {
        pages.push(1)
        pages.push('...')
        for (let i = totalPages - 3; i <= totalPages; i++) pages.push(i)
      } else {
        pages.push(1)
        pages.push('...')
        for (let i = currentPage - 1; i <= currentPage + 1; i++) pages.push(i)
        pages.push('...')
        pages.push(totalPages)
      }
    }

    return pages
  }

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen bg-white">
        <TopHeader />
        <Header />
        <div className="flex items-center justify-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <TopHeader />
      <Header />

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary/10 to-primary/5 py-12">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <Heart className="h-8 w-8 text-red-500 mr-3" />
              <h1 className="text-4xl font-bold text-gray-900">My Favorites</h1>
            </div>
            <p className="text-lg text-gray-600 mb-6">
              Your saved vendors, venues, and shop items all in one place
            </p>
            <div className="flex items-center justify-center gap-6 text-sm text-gray-600">
              <span>{counts.total} Total Items</span>
              <span>•</span>
              <span>{counts.vendors} Vendors</span>
              <span>•</span>
              <span>{counts.venues} Venues</span>
              <span>•</span>
              <span>{counts.shopItems} Shop Items</span>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          {/* Search and Filter */}
          <div className="mb-8">
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search favorites..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4 mb-8">
              <TabsTrigger value="all">All ({counts.total})</TabsTrigger>
              <TabsTrigger value="vendors">Vendors ({counts.vendors})</TabsTrigger>
              <TabsTrigger value="venues">Venues ({counts.venues})</TabsTrigger>
              <TabsTrigger value="shops">Shop Items ({counts.shopItems})</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab}>
              {loading ? (
                <div className="flex items-center justify-center py-20">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : filteredFavorites.length === 0 ? (
                <div className="text-center py-20">
                  <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-gray-600 mb-2">
                    {searchQuery ? 'No favorites found' : 'No favorites yet'}
                  </h3>
                  <p className="text-gray-500 mb-6">
                    {searchQuery
                      ? 'Try adjusting your search terms'
                      : 'Start exploring and save your favorite vendors, venues, and shop items'
                    }
                  </p>
                  {!searchQuery && (
                    <div className="flex gap-4 justify-center">
                      <Link href="/vendors">
                        <Button>Browse Vendors</Button>
                      </Link>
                      <Link href="/venues">
                        <Button variant="outline">Browse Venues</Button>
                      </Link>
                    </div>
                  )}
                </div>
              ) : (
                <>
                  {/* Results Info */}
                  <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
                    <div className="text-sm text-gray-600">
                      Showing {startIndex + 1}-{Math.min(endIndex, filteredFavorites.length)} of {filteredFavorites.length} favorites
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <span>Items per page:</span>
                        <Select
                          value={itemsPerPage.toString()}
                          onValueChange={(value) => {
                            setItemsPerPage(Number(value))
                            setCurrentPage(1) // Reset to first page
                          }}
                        >
                          <SelectTrigger className="w-20 h-8">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="6">6</SelectItem>
                            <SelectItem value="12">12</SelectItem>
                            <SelectItem value="24">24</SelectItem>
                            <SelectItem value="48">48</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="text-sm text-gray-600">
                        Page {currentPage} of {totalPages}
                      </div>
                    </div>
                  </div>

                  {/* Favorites Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {currentFavorites.map((favorite) => (
                    <Card key={favorite.id} className="group hover:shadow-lg transition-shadow duration-200">
                      <CardContent className="p-0">
                        {/* Image */}
                        <div className="relative h-48 bg-gray-200 rounded-t-lg overflow-hidden">
                          {favorite.entityImage ? (
                            <img
                              src={favorite.entityImage}
                              alt={favorite.entityName}
                              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                            />
                          ) : (
                            <div className="flex items-center justify-center h-full bg-gradient-to-br from-primary/20 to-primary/10">
                              <span className="text-4xl">
                                {favorite.entityType === 'VENDOR' ? '👨‍💼' :
                                 favorite.entityType === 'VENUE' ? '🏛️' : '🛍️'}
                              </span>
                            </div>
                          )}

                          {/* Remove button */}
                          <Button
                            size="icon"
                            variant="destructive"
                            className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                            onClick={() => handleRemoveFavorite(favorite.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>

                          {/* Entity type badge */}
                          <Badge className="absolute top-2 left-2 bg-white/90 text-gray-700">
                            {getEntityTypeLabel(favorite.entityType)}
                          </Badge>
                        </div>

                        {/* Content */}
                        <div className="p-4">
                          <h3 className="font-bold text-lg mb-2 line-clamp-1">{favorite.entityName}</h3>

                          {favorite.entityLocation && (
                            <div className="flex items-center text-gray-500 text-sm mb-2">
                              <MapPin className="h-4 w-4 mr-1" />
                              <span className="line-clamp-1">
                                {favorite.entityLocation}
                                {favorite.entityCity && `, ${favorite.entityCity}`}
                                {favorite.entityState && `, ${favorite.entityState}`}
                              </span>
                            </div>
                          )}

                          {favorite.entityRating && (
                            <div className="flex items-center gap-2 mb-2">
                              <div className="flex items-center">
                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                <span className="text-sm font-semibold ml-1">{favorite.entityRating}</span>
                              </div>
                              {favorite.entityReviewCount && (
                                <span className="text-sm text-gray-500">({favorite.entityReviewCount} reviews)</span>
                              )}
                            </div>
                          )}

                          {favorite.entityPrice && (
                            <div className="text-primary font-semibold mb-3">{favorite.entityPrice}</div>
                          )}

                          {favorite.entityDescription && (
                            <p className="text-gray-600 text-sm mb-4 line-clamp-2">{favorite.entityDescription}</p>
                          )}

                          <div className="flex gap-2">
                            <Link href={getEntityUrl(favorite)} className="flex-1">
                              <Button className="w-full" size="sm">
                                <ExternalLink className="h-4 w-4 mr-2" />
                                View Details
                              </Button>
                            </Link>
                          </div>

                          <div className="text-xs text-gray-400 mt-3">
                            Added {new Date(favorite.dateAdded).toLocaleDateString()}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                  {/* Pagination */}
                  {totalPages > 1 && (
                    <div className="flex flex-col items-center mt-12 space-y-4">
                      {/* Main Pagination Controls */}
                      <div className="flex items-center space-x-2">
                        {/* Previous Button */}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(currentPage - 1)}
                          disabled={currentPage === 1}
                          className="flex items-center gap-2"
                        >
                          <ChevronLeft className="h-4 w-4" />
                          Previous
                        </Button>

                        {/* Page Numbers */}
                        <div className="flex items-center space-x-1">
                          {generatePageNumbers().map((page, index) => (
                            <React.Fragment key={index}>
                              {page === '...' ? (
                                <span className="px-3 py-2 text-gray-500">...</span>
                              ) : (
                                <Button
                                  variant={currentPage === page ? "default" : "outline"}
                                  size="sm"
                                  onClick={() => handlePageChange(page as number)}
                                  className={`min-w-[40px] ${
                                    currentPage === page
                                      ? 'bg-primary text-white'
                                      : 'hover:bg-gray-100'
                                  }`}
                                >
                                  {page}
                                </Button>
                              )}
                            </React.Fragment>
                          ))}
                        </div>

                        {/* Next Button */}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handlePageChange(currentPage + 1)}
                          disabled={currentPage === totalPages}
                          className="flex items-center gap-2"
                        >
                          Next
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </div>

                      {/* Quick Page Jump */}
                      {totalPages > 5 && (
                        <div className="flex items-center gap-2 text-sm text-gray-600">
                          <span>Go to page:</span>
                          <Input
                            type="number"
                            min="1"
                            max={totalPages}
                            value=""
                            placeholder={currentPage.toString()}
                            className="w-16 h-8 text-center"
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                const page = parseInt((e.target as HTMLInputElement).value)
                                if (page >= 1 && page <= totalPages) {
                                  handlePageChange(page)
                                  ;(e.target as HTMLInputElement).value = ''
                                }
                              }
                            }}
                          />
                          <span>of {totalPages}</span>
                        </div>
                      )}
                    </div>
                  )}
                </>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </section>

      <Footer />
    </div>
  )
}