{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "set NODE_OPTIONS=--max-old-space-size=8192 && next build", "build:no-console": "set NODE_ENV=production && npm run build", "build:keep-console": "set KEEP_CONSOLE=true && npm run build", "dev": "set NODE_OPTIONS=--max-old-space-size=8192 && next dev", "dev:turbo": "set NODE_OPTIONS=--max-old-space-size=8192 && next dev --turbo", "dev:fast": "set NODE_OPTIONS=--max-old-space-size=8192 && next dev --turbo --port 3001", "lint": "next lint", "start": "next start", "analyze": "set ANALYZE=true && npm run build", "console-info": "node scripts/setup-console-removal.js"}, "dependencies": {"@aws-amplify/adapter-nextjs": "^1.6.5", "@aws-amplify/api": "^6.3.14", "@aws-amplify/api-graphql": "^4.7.18", "@aws-amplify/ui-react": "^6.11.2", "@aws-sdk/client-cognito-identity-provider": "^3.844.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^3.9.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "autoprefixer": "^10.4.20", "aws-amplify": "^6.15.3", "aws-sdk": "^2.1692.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.6.0", "embla-carousel-react": "8.5.1", "framer-motion": "^12.23.0", "html2canvas": "^1.4.1", "i18next": "^25.3.0", "i18next-http-backend": "^3.0.2", "input-otp": "1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.454.0", "next": "14.2.16", "next-i18next": "^15.4.2", "next-seo": "^6.8.0", "next-themes": "^0.4.4", "nprogress": "^0.2.0", "react": "^18", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.1", "react-hot-toast": "^2.5.2", "react-i18next": "^15.5.3", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "web-vitals": "^5.0.3", "zod": "^3.24.1"}, "devDependencies": {"@next/bundle-analyzer": "^15.4.1", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "critters": "^0.0.25", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}