"use client"

import { useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import '@/lib/i18n'

export function useSafeTranslation() {
  const { t, i18n, ready } = useTranslation()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const safeT = (key: string, fallback?: string) => {
    if (!mounted || !ready) {
      return fallback || key
    }
    return t(key)
  }

  return {
    t: safeT,
    i18n,
    ready: mounted && ready,
    mounted
  }
}
