import { useState, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { 
  newsletterService, 
  NewsletterSubscriptionInput, 
  NewsletterSubscriptionResponse 
} from '@/lib/services/newsletterService';

interface UseNewsletterReturn {
  // State
  loading: boolean;
  error: string | null;
  success: boolean;
  subscription: NewsletterSubscriptionResponse | null;
  
  // Actions
  subscribe: (input: Omit<NewsletterSubscriptionInput, 'userId'>) => Promise<void>;
  unsubscribe: (email: string) => Promise<void>;
  updatePreferences: (email: string, preferences: NewsletterSubscriptionInput['preferences']) => Promise<void>;
  checkSubscription: (email: string) => Promise<void>;
  clearMessages: () => void;
}

export const useNewsletter = (): UseNewsletterReturn => {
  const { user, userProfile } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [subscription, setSubscription] = useState<NewsletterSubscriptionResponse | null>(null);

  const clearMessages = useCallback(() => {
    setError(null);
    setSuccess(false);
  }, []);

  const subscribe = useCallback(async (input: Omit<NewsletterSubscriptionInput, 'userId'>) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // Add user ID if user is logged in
      const subscriptionInput: NewsletterSubscriptionInput = {
        ...input,
        userId: user?.userId || userProfile?.userId
      };

      const result = await newsletterService.subscribe(subscriptionInput);
      setSubscription(result);
      setSuccess(true);
    } catch (err: any) {
      setError(err.message || 'Failed to subscribe to newsletter');
    } finally {
      setLoading(false);
    }
  }, [user]);

  const unsubscribe = useCallback(async (email: string) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const result = await newsletterService.unsubscribe(email);
      setSubscription(result);
      setSuccess(true);
    } catch (err: any) {
      setError(err.message || 'Failed to unsubscribe from newsletter');
    } finally {
      setLoading(false);
    }
  }, []);

  const updatePreferences = useCallback(async (
    email: string, 
    preferences: NewsletterSubscriptionInput['preferences']
  ) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const result = await newsletterService.updatePreferences(email, preferences);
      setSubscription(result);
      setSuccess(true);
    } catch (err: any) {
      setError(err.message || 'Failed to update newsletter preferences');
    } finally {
      setLoading(false);
    }
  }, []);

  const checkSubscription = useCallback(async (email: string) => {
    setLoading(true);
    setError(null);

    try {
      const result = await newsletterService.getByEmail(email);
      setSubscription(result);
    } catch (err: any) {
      setError(err.message || 'Failed to check subscription status');
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    loading,
    error,
    success,
    subscription,
    subscribe,
    unsubscribe,
    updatePreferences,
    checkSubscription,
    clearMessages
  };
};

export default useNewsletter;
