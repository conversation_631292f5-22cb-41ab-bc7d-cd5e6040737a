// This file is now a wrapper around the amplify-singleton
import { configureAmplify, debugAmplifyConfig } from './config/amplifyConfig';
import awsExports from './amplify-singleton';

// Configure Amplify with enhanced mobile OTP support
configureAmplify();

// Debug configuration in development
if (process.env.NODE_ENV === 'development') {
  console.log('🔧 Amplify Configuration Debug:');
  const debugInfo = debugAmplifyConfig();

  if (!debugInfo.isMobileOTPReady) {
    console.warn('⚠️ Mobile OTP not fully configured. Run "amplify push" to update configuration.');
  } else {
    console.log('✅ Mobile OTP authentication is ready!');
  }
}

export default awsExports;
