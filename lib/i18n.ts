import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// Import translations directly to avoid hydration issues
import enCommon from '../public/locales/en/common.json';
import taCommon from '../public/locales/ta/common.json';
import hiCommon from '../public/locales/hi/common.json';
import knCommon from '../public/locales/kn/common.json';
import mlCommon from '../public/locales/ml/common.json';
import teCommon from '../public/locales/te/common.json';
import mrCommon from '../public/locales/mr/common.json';
import guCommon from '../public/locales/gu/common.json';
import paCommon from '../public/locales/pa/common.json';
import bnCommon from '../public/locales/bn/common.json';
import orCommon from '../public/locales/or/common.json';
import asCommon from '../public/locales/as/common.json';
import urCommon from '../public/locales/ur/common.json';
import neCommon from '../public/locales/ne/common.json';

const resources = {
  en: { common: enCommon },
  ta: { common: taCommon },
  hi: { common: hiCommon },
  kn: { common: knCommon },
  ml: { common: mlCommon },
  te: { common: teCommon },
  mr: { common: mrCommon },
  gu: { common: guCommon },
  pa: { common: paCommon },
  bn: { common: bnCommon },
  or: { common: orCommon },
  as: { common: asCommon },
  ur: { common: urCommon },
  ne: { common: neCommon }
};

i18n
  .use(initReactI18next)
  .init({
    resources,
    lng: 'en', // default language
    fallbackLng: 'en',
    debug: false,

    ns: ['common'],
    defaultNS: 'common',

    interpolation: {
      escapeValue: false
    },

    react: {
      useSuspense: false
    }
  });

export default i18n;
