'use client';

/**
 * IndexedDB Cleanup Utility
 * 
 * This utility helps remove IndexedDB databases that might be affecting 
 * Lighthouse performance scores. It targets AWS Amplify and other 
 * framework-created IndexedDB instances that aren't essential for the app.
 */

// List of IndexedDB database names that are likely created by AWS Amplify
const AMPLIFY_DB_PATTERNS = [
  'amplify-datastore',
  'aws-amplify-cache',
  'amplify-storage',
  'aws.cognito.',
  'CognitoIdentityServiceProvider',
  'datastore_Setting',
  'datastore_Items',
  'keyval-store',
  'localforage'
];

/**
 * Clean up all IndexedDB databases that match Amplify patterns
 */
export async function cleanupIndexedDB(): Promise<void> {
  if (typeof window === 'undefined' || !window.indexedDB) {
    return;
  }

  try {
    // Get all databases
    const databases = await window.indexedDB.databases();
    
    // Delete each database that matches our patterns
    for (const db of databases) {
      if (!db.name) continue;
      
      const shouldDelete = AMPLIFY_DB_PATTERNS.some(pattern => 
        db.name.toLowerCase().includes(pattern.toLowerCase())
      );
      
      if (shouldDelete) {
        if (process.env.NODE_ENV === 'development') {
          console.log(`🧹 Cleaning up IndexedDB: ${db.name}`);
        }
        
        try {
          // Delete the database
          const deleteRequest = window.indexedDB.deleteDatabase(db.name);
          
          await new Promise<void>((resolve, reject) => {
            deleteRequest.onsuccess = () => resolve();
            deleteRequest.onerror = () => reject(deleteRequest.error);
            deleteRequest.onblocked = () => {
              if (process.env.NODE_ENV === 'development') {
                console.warn(`IndexedDB deletion blocked: ${db.name}`);
              }
              resolve(); // Continue anyway
            };
          });
        } catch (err) {
          // Ignore errors for individual databases
          if (process.env.NODE_ENV === 'development') {
            console.warn(`Failed to delete IndexedDB: ${db.name}`, err);
          }
        }
      }
    }
    
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ IndexedDB cleanup completed');
    }
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ IndexedDB cleanup failed:', error);
    }
  }
}

/**
 * Get information about existing IndexedDB databases
 */
export async function getIndexedDBInfo(): Promise<{name: string, version: number}[]> {
  if (typeof window === 'undefined' || !window.indexedDB) {
    return [];
  }
  
  try {
    const databases = await window.indexedDB.databases();
    return databases
      .filter(db => db.name) // Filter out undefined names
      .map(db => ({
        name: db.name as string,
        version: db.version || 0
      }));
  } catch (error) {
    console.error('Failed to get IndexedDB info:', error);
    return [];
  }
}

// Auto-cleanup when imported in client components
if (typeof window !== 'undefined') {
  // Run cleanup after a short delay to not block initial render
  setTimeout(() => {
    cleanupIndexedDB();
  }, 1000);
}
