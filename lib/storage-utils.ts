'use client';

// Efficient storage utilities to reduce performance impact
// Uses throttling and batching to minimize storage operations

interface StorageOptions {
  throttleMs?: number;
  maxRetries?: number;
  fallbackToMemory?: boolean;
}

class StorageManager {
  private throttleTimers: Map<string, NodeJS.Timeout> = new Map();
  private memoryFallback: Map<string, any> = new Map();
  private isStorageAvailable: boolean = true;

  constructor() {
    this.checkStorageAvailability();
  }

  private checkStorageAvailability(): void {
    try {
      if (typeof window === 'undefined') {
        this.isStorageAvailable = false;
        return;
      }
      
      const test = '__storage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      this.isStorageAvailable = true;
    } catch {
      this.isStorageAvailable = false;
      console.warn('localStorage not available, falling back to memory storage');
    }
  }

  // Throttled set operation to prevent excessive writes
  setItem(key: string, value: any, options: StorageOptions = {}): void {
    const { throttleMs = 500, fallbackToMemory = true } = options;

    // Clear existing timer for this key
    const existingTimer = this.throttleTimers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set new throttled timer
    const timer = setTimeout(() => {
      this.performSetItem(key, value, fallbackToMemory);
      this.throttleTimers.delete(key);
    }, throttleMs);

    this.throttleTimers.set(key, timer);
  }

  private performSetItem(key: string, value: any, fallbackToMemory: boolean): void {
    try {
      if (this.isStorageAvailable) {
        const serialized = JSON.stringify(value);
        localStorage.setItem(key, serialized);
      } else if (fallbackToMemory) {
        this.memoryFallback.set(key, value);
      }
    } catch (error) {
      console.error(`Failed to store ${key}:`, error);
      if (fallbackToMemory) {
        this.memoryFallback.set(key, value);
      }
    }
  }

  // Immediate get operation (no throttling needed for reads)
  getItem<T>(key: string, defaultValue?: T): T | null {
    try {
      if (this.isStorageAvailable) {
        const item = localStorage.getItem(key);
        if (item === null) return defaultValue || null;
        return JSON.parse(item);
      } else {
        return this.memoryFallback.get(key) || defaultValue || null;
      }
    } catch (error) {
      console.error(`Failed to retrieve ${key}:`, error);
      return this.memoryFallback.get(key) || defaultValue || null;
    }
  }

  // Remove item with throttling
  removeItem(key: string, options: StorageOptions = {}): void {
    const { throttleMs = 100 } = options;

    const existingTimer = this.throttleTimers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    const timer = setTimeout(() => {
      try {
        if (this.isStorageAvailable) {
          localStorage.removeItem(key);
        }
        this.memoryFallback.delete(key);
      } catch (error) {
        console.error(`Failed to remove ${key}:`, error);
      }
      this.throttleTimers.delete(key);
    }, throttleMs);

    this.throttleTimers.set(key, timer);
  }

  // Clear all pending operations for a key
  clearPendingOperations(key: string): void {
    const timer = this.throttleTimers.get(key);
    if (timer) {
      clearTimeout(timer);
      this.throttleTimers.delete(key);
    }
  }

  // Force immediate write of all pending operations
  flush(): void {
    this.throttleTimers.forEach((timer, key) => {
      clearTimeout(timer);
    });
    this.throttleTimers.clear();
  }

  // Get storage usage info
  getStorageInfo(): { used: number; available: boolean; fallbackActive: boolean } {
    let used = 0;
    
    if (this.isStorageAvailable) {
      try {
        for (const key in localStorage) {
          if (localStorage.hasOwnProperty(key)) {
            used += localStorage[key].length;
          }
        }
      } catch {
        used = -1;
      }
    }

    return {
      used,
      available: this.isStorageAvailable,
      fallbackActive: this.memoryFallback.size > 0
    };
  }
}

// Singleton instance
export const storageManager = new StorageManager();

// Convenience functions for common operations
export const setStorageItem = (key: string, value: any, options?: StorageOptions) => 
  storageManager.setItem(key, value, options);

export const getStorageItem = <T>(key: string, defaultValue?: T): T | null => 
  storageManager.getItem(key, defaultValue);

export const removeStorageItem = (key: string, options?: StorageOptions) => 
  storageManager.removeItem(key, options);

// Cart-specific storage utilities
export const CART_STORAGE_KEY = 'thirumanam-cart';

export const saveCart = (cart: any[]) => {
  setStorageItem(CART_STORAGE_KEY, cart, { throttleMs: 1000 });
};

export const loadCart = (): any[] => {
  return getStorageItem(CART_STORAGE_KEY, []);
};

export const clearCart = () => {
  removeStorageItem(CART_STORAGE_KEY);
};
