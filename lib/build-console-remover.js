/**
 * Build-time Console Remover
 * 
 * This module provides utilities to remove console statements during the build process
 * for optimal production performance.
 */

/**
 * Webpack plugin to remove console statements
 */
class RemoveConsoleWebpackPlugin {
  constructor(options = {}) {
    this.options = {
      exclude: ['error'], // Keep these console methods
      include: ['log', 'info', 'debug', 'warn', 'trace', 'table', 'group', 'groupCollapsed', 'groupEnd', 'time', 'timeEnd', 'count', 'assert'],
      ...options
    };
  }

  apply(compiler) {
    const pluginName = 'RemoveConsoleWebpackPlugin';

    compiler.hooks.compilation.tap(pluginName, (compilation) => {
      compilation.hooks.optimizeChunkAssets.tapAsync(pluginName, (chunks, callback) => {
        chunks.forEach((chunk) => {
          chunk.files.forEach((file) => {
            if (file.endsWith('.js')) {
              const asset = compilation.assets[file];
              let source = asset.source();

              // Create regex to match console methods to remove
              const methodsToRemove = this.options.include.filter(
                method => !this.options.exclude.includes(method)
              );

              if (methodsToRemove.length > 0) {
                const consoleRegex = new RegExp(
                  `console\\.(${methodsToRemove.join('|')})\\s*\\([^)]*\\)\\s*;?`,
                  'g'
                );

                source = source.replace(consoleRegex, '');
              }

              // Update the asset
              compilation.assets[file] = {
                source: () => source,
                size: () => source.length
              };
            }
          });
        });
        callback();
      });
    });
  }
}

/**
 * Terser configuration for console removal
 */
function getTerserConfig(options = {}) {
  const defaultOptions = {
    keepErrors: true,
    keepWarnings: false,
    ...options
  };

  const pureFuncs = ['console.log', 'console.info', 'console.debug', 'console.trace', 'console.table', 'console.group', 'console.groupCollapsed', 'console.groupEnd', 'console.time', 'console.timeEnd', 'console.count', 'console.assert'];
  
  if (!defaultOptions.keepWarnings) {
    pureFuncs.push('console.warn');
  }

  return {
    terserOptions: {
      compress: {
        drop_console: !defaultOptions.keepErrors, // Remove all console if keepErrors is false
        pure_funcs: defaultOptions.keepErrors ? pureFuncs : undefined,
      },
      mangle: true,
    },
    extractComments: false,
  };
}

/**
 * Babel plugin configuration for console removal
 */
function getBabelConsoleConfig(options = {}) {
  const defaultOptions = {
    exclude: ['error'],
    ...options
  };

  return [
    'transform-remove-console',
    {
      exclude: defaultOptions.exclude
    }
  ];
}

/**
 * Next.js webpack configuration helper
 */
function configureNextWebpack(config, { dev, isServer }, options = {}) {
  if (!dev && !isServer) {
    const TerserPlugin = require('terser-webpack-plugin');
    
    // Configure Terser
    const terserConfig = getTerserConfig(options);
    
    config.optimization.minimizer = config.optimization.minimizer || [];
    
    const existingTerserPluginIndex = config.optimization.minimizer.findIndex(
      (plugin) => plugin.constructor.name === 'TerserPlugin'
    );
    
    if (existingTerserPluginIndex > -1) {
      config.optimization.minimizer[existingTerserPluginIndex] = new TerserPlugin(terserConfig);
    } else {
      config.optimization.minimizer.push(new TerserPlugin(terserConfig));
    }

    // Optionally add custom webpack plugin
    if (options.useCustomPlugin) {
      config.plugins.push(new RemoveConsoleWebpackPlugin(options));
    }
  }

  return config;
}

/**
 * Environment-based console removal check
 */
function shouldRemoveConsole() {
  return process.env.NODE_ENV === 'production' && 
         process.env.KEEP_CONSOLE !== 'true';
}

/**
 * Build information logger
 */
function logBuildInfo() {
  if (shouldRemoveConsole()) {
    console.log('🔧 Console removal active for production build');
    console.log('   • console.log, console.info, console.debug, console.warn will be removed');
    console.log('   • console.error will be preserved');
    console.log('   • Set KEEP_CONSOLE=true to disable console removal');
  } else {
    console.log('🔧 Console removal disabled (development mode or KEEP_CONSOLE=true)');
  }
}

module.exports = {
  RemoveConsoleWebpackPlugin,
  getTerserConfig,
  getBabelConsoleConfig,
  configureNextWebpack,
  shouldRemoveConsole,
  logBuildInfo
};
