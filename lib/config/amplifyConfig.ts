/**
 * Amplify Configuration Helper
 * Ensures proper configuration for mobile OTP authentication
 */

import { Amplify } from 'aws-amplify';
import awsExports from '../../src/aws-exports';

// Enhanced configuration for mobile OTP support
const amplifyConfig = {
  ...awsExports,

  // Ensure phone number support is enabled
  aws_cognito_username_attributes: ['EMAIL', 'PHONE_NUMBER'],
  aws_cognito_signup_attributes: ['EMAIL', 'PHONE_NUMBER'],
  aws_cognito_verification_mechanisms: ['EMAIL', 'PHONE_NUMBER'],

  // SMS configuration
  aws_cognito_mfa_configuration: 'OPTIONAL',
  aws_cognito_mfa_types: ['SMS'],

  // Auth flow configuration
  authenticationFlowType: 'USER_SRP_AUTH',

  // Custom auth messages
  aws_cognito_sms_authentication_message: 'Your Thirumanam 360 login code is {####}',
  aws_cognito_sms_verification_message: 'Your Thirumanam 360 verification code is {####}',
  aws_cognito_email_verification_subject: 'Thirumanam 360 - Verify your email',
  aws_cognito_email_verification_message: 'Your Thirumanam 360 verification code is {####}',

  // Disable unnecessary storage features for better performance
  Storage: {
    disabled: true // Disable Amplify Storage which can create IndexedDB
  },

  // Disable DataStore (which uses IndexedDB) since we're using AppSync directly
  DataStore: {
    disabled: true
  },

  // Configure API to not use offline capabilities
  API: {
    ...awsExports.API,
    offline: false
  }
};

/**
 * Configure Amplify with enhanced settings and IndexedDB disabled
 */
export const configureAmplify = () => {
  try {
    // Configure Amplify without IndexedDB-creating features
    Amplify.configure({
      ...amplifyConfig,
      // Explicitly disable DataStore to prevent IndexedDB creation
      DataStore: undefined,
      // Disable Storage module
      Storage: undefined
    });

    console.log('✅ Amplify configured successfully with mobile OTP support');

    // Clean up any existing IndexedDB from previous sessions
    if (typeof window !== 'undefined') {
      import('../cleanup-indexeddb').then(({ cleanupIndexedDB }) => {
        cleanupIndexedDB();
      });
    }
  } catch (error) {
    console.error('❌ Failed to configure Amplify:', error);
  }
};

/**
 * Get current Amplify configuration
 */
export const getAmplifyConfig = () => {
  return amplifyConfig;
};

/**
 * Check if mobile OTP is properly configured
 */
export const isMobileOTPConfigured = (): boolean => {
  const config = getAmplifyConfig();
  
  const hasPhoneNumberSupport = 
    config.aws_cognito_username_attributes?.includes('PHONE_NUMBER') &&
    config.aws_cognito_verification_mechanisms?.includes('PHONE_NUMBER');
    
  const hasSMSSupport = 
    config.aws_cognito_mfa_types?.includes('SMS');
    
  return hasPhoneNumberSupport && hasSMSSupport;
};

/**
 * Validate Amplify configuration for mobile OTP
 */
export const validateMobileOTPConfig = (): {
  isValid: boolean;
  issues: string[];
  recommendations: string[];
} => {
  const config = getAmplifyConfig();
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  // Check username attributes
  if (!config.aws_cognito_username_attributes?.includes('PHONE_NUMBER')) {
    issues.push('Phone number not enabled as username attribute');
    recommendations.push('Add PHONE_NUMBER to aws_cognito_username_attributes');
  }
  
  // Check verification mechanisms
  if (!config.aws_cognito_verification_mechanisms?.includes('PHONE_NUMBER')) {
    issues.push('Phone number verification not enabled');
    recommendations.push('Add PHONE_NUMBER to aws_cognito_verification_mechanisms');
  }
  
  // Check SMS MFA
  if (!config.aws_cognito_mfa_types?.includes('SMS')) {
    issues.push('SMS MFA not configured');
    recommendations.push('Add SMS to aws_cognito_mfa_types');
  }
  
  // Check required fields
  if (!config.aws_user_pools_id) {
    issues.push('User Pool ID not configured');
    recommendations.push('Ensure aws_user_pools_id is set');
  }
  
  if (!config.aws_user_pools_web_client_id) {
    issues.push('User Pool Client ID not configured');
    recommendations.push('Ensure aws_user_pools_web_client_id is set');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    recommendations
  };
};

/**
 * Get SMS configuration status
 */
export const getSMSConfigStatus = (): {
  isConfigured: boolean;
  region: string;
  mfaConfiguration: string;
  supportedMfaTypes: string[];
} => {
  const config = getAmplifyConfig();
  
  return {
    isConfigured: config.aws_cognito_mfa_types?.includes('SMS') || false,
    region: config.aws_cognito_region || 'Not configured',
    mfaConfiguration: config.aws_cognito_mfa_configuration || 'OFF',
    supportedMfaTypes: config.aws_cognito_mfa_types || []
  };
};

/**
 * Debug configuration helper
 */
export const debugAmplifyConfig = () => {
  const config = getAmplifyConfig();
  const validation = validateMobileOTPConfig();
  const smsStatus = getSMSConfigStatus();
  
  console.group('🔧 Amplify Configuration Debug');
  
  console.log('📋 Basic Configuration:');
  console.log('- Region:', config.aws_project_region);
  console.log('- User Pool ID:', config.aws_user_pools_id);
  console.log('- Client ID:', config.aws_user_pools_web_client_id);
  
  console.log('\n📱 Mobile OTP Configuration:');
  console.log('- Username Attributes:', config.aws_cognito_username_attributes);
  console.log('- Signup Attributes:', config.aws_cognito_signup_attributes);
  console.log('- Verification Mechanisms:', config.aws_cognito_verification_mechanisms);
  
  console.log('\n📨 SMS Configuration:');
  console.log('- MFA Configuration:', smsStatus.mfaConfiguration);
  console.log('- MFA Types:', smsStatus.supportedMfaTypes);
  console.log('- SMS Configured:', smsStatus.isConfigured);
  
  console.log('\n✅ Validation Results:');
  console.log('- Is Valid:', validation.isValid);
  if (validation.issues.length > 0) {
    console.log('- Issues:', validation.issues);
    console.log('- Recommendations:', validation.recommendations);
  }
  
  console.groupEnd();
  
  return {
    config,
    validation,
    smsStatus,
    isMobileOTPReady: validation.isValid && smsStatus.isConfigured
  };
};

export default amplifyConfig;
