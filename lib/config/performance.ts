import React from 'react'

// Performance configuration for dashboard pages
export const PERFORMANCE_CONFIG = {
  // Page loading timeouts
  PAGE_LOAD_TIMEOUT: 5000,
  API_REQUEST_TIMEOUT: 10000,
  
  // Pagination settings
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // Caching settings
  CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
  
  // Debounce settings
  SEARCH_DEBOUNCE: 300,
  FILTER_DEBOUNCE: 500,
  
  // Loading states
  SKELETON_ITEMS: 6,
  MIN_LOADING_TIME: 200, // Minimum time to show loading state
  
  // Memory optimization
  MAX_CACHED_PAGES: 5,
  CLEANUP_INTERVAL: 10 * 60 * 1000, // 10 minutes
}

// Performance monitoring utilities
export class PerformanceMonitor {
  private static measurements: Map<string, number> = new Map()
  
  static startMeasurement(key: string) {
    this.measurements.set(key, performance.now())
  }
  
  static endMeasurement(key: string): number {
    const start = this.measurements.get(key)
    if (!start) return 0
    
    const duration = performance.now() - start
    this.measurements.delete(key)
    
    // Log slow operations in development
    if (process.env.NODE_ENV === 'development' && duration > 1000) {
      console.warn(`Slow operation detected: ${key} took ${duration.toFixed(2)}ms`)
    }
    
    return duration
  }
  
  static measureAsync<T>(key: string, fn: () => Promise<T>): Promise<T> {
    this.startMeasurement(key)
    return fn().finally(() => {
      this.endMeasurement(key)
    })
  }
}

// Debounce utility for search and filters
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// Memory-efficient data loader
export class DataLoader<T> {
  private cache = new Map<string, { data: T; timestamp: number }>()
  private readonly maxCacheSize: number
  private readonly cacheDuration: number
  
  constructor(maxCacheSize = 10, cacheDuration = PERFORMANCE_CONFIG.CACHE_DURATION) {
    this.maxCacheSize = maxCacheSize
    this.cacheDuration = cacheDuration
  }
  
  async load(key: string, fetcher: () => Promise<T>): Promise<T> {
    // Check cache first
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.cacheDuration) {
      return cached.data
    }
    
    // Fetch new data
    const data = await PerformanceMonitor.measureAsync(`DataLoader:${key}`, fetcher)
    
    // Store in cache
    this.cache.set(key, { data, timestamp: Date.now() })
    
    // Cleanup old entries if cache is full
    if (this.cache.size > this.maxCacheSize) {
      const oldestKey = this.cache.keys().next().value
      this.cache.delete(oldestKey)
    }
    
    return data
  }
  
  clear() {
    this.cache.clear()
  }
  
  delete(key: string) {
    this.cache.delete(key)
  }
}

// Lazy loading utility for heavy components
export function createLazyComponent<T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) {
  const LazyComponent = React.lazy(importFn)
  
  return (props: React.ComponentProps<T>) => (
    <React.Suspense fallback={fallback ? React.createElement(fallback) : <div>Loading...</div>}>
      <LazyComponent {...props} />
    </React.Suspense>
  )
}

export default PERFORMANCE_CONFIG
