/**
 * User Role Management Service
 * Handles updating user roles and permissions
 */

import { profileService } from './profileService';

export type UserRole = 'customer' | 'vendor' | 'admin' | 'super_admin';

export interface RoleUpdateData {
  userId: string;
  newRole: UserRole;
  updatedBy: string;
  reason?: string;
  formData?: UserTypeFormData;
}

export interface UserTypeFormData {
  formType: 'customer' | 'vendor' | 'admin' | 'bulk_import' | 'migration';
  submissionDate: string;
  formVersion: string;
  businessDetails?: BusinessFormDetails;
  personalDetails?: PersonalFormDetails;
  adminDetails?: AdminFormDetails;
  verificationStatus: 'pending' | 'verified' | 'rejected';
  approvalStatus: 'pending' | 'approved' | 'rejected';
  submittedBy: string;
  reviewedBy?: string;
  reviewDate?: string;
  reviewNotes?: string;
}

export interface BusinessFormDetails {
  businessName: string;
  businessType: string;
  businessCategory?: string;
  businessDescription?: string;
  servicesOffered?: string[];
  experienceYears?: number;
  teamSize?: number;
  businessLicense?: string;
  gstNumber?: string;
  panNumber?: string;
  businessAddress?: string;
  businessPhone?: string;
  businessEmail?: string;
  businessWebsite?: string;
  portfolioLinks?: string[];
  socialMediaLinks?: string[];
  operatingHours?: string;
  serviceAreas?: string[];
  priceRange?: string;
  specializations?: string[];
}

export interface PersonalFormDetails {
  firstName: string;
  lastName: string;
  dateOfBirth?: string;
  gender?: string;
  weddingDate?: string;
  partnerName?: string;
  weddingLocation?: string;
  budgetRange?: string;
  weddingStyle?: string;
  guestCount?: number;
  interests?: string[];
  preferences?: string[];
  referralSource?: string;
}

export interface AdminFormDetails {
  adminLevel: string;
  department?: string;
  responsibilities?: string[];
  accessLevel?: string;
  reportingTo?: string;
  startDate?: string;
  employeeId?: string;
  designation?: string;
}

export interface RoleUpdateResult {
  success: boolean;
  message: string;
  previousRole?: UserRole;
  newRole?: UserRole;
}

export class UserRoleService {
  
  /**
   * Update user role from customer to super admin
   */
  static async updateToSuperAdmin(userId: string, updatedBy: string, reason?: string, formData?: UserTypeFormData): Promise<RoleUpdateResult> {
    try {
      // Get current user profile
      const currentProfile = await profileService.getProfile(userId);

      if (!currentProfile) {
        return {
          success: false,
          message: 'User profile not found'
        };
      }

      const previousRole = this.determineCurrentRole(currentProfile);

      // Create form data for super admin promotion
      const userTypeForm = formData || this.createDefaultFormData('admin', updatedBy, {
        adminLevel: 'super_admin',
        designation: 'Super Administrator',
        accessLevel: 'full_system_access',
        responsibilities: ['system_management', 'user_management', 'platform_administration']
      });

      // Update profile with super admin permissions
      const updateData = {
        isSuperAdmin: true,
        isAdmin: true, // Super admin also has admin privileges
        isVendor: currentProfile.isVendor || false, // Preserve vendor status if exists
        role: 'SUPER_ADMIN' as const,
        permissions: this.getSuperAdminPermissions(),
        registrationSource: 'ADMIN_FORM' as const,
        accountType: 'ADMIN_ACCOUNT' as const,
        userTypeForm: userTypeForm,
        lastRoleUpdate: new Date().toISOString(),
        roleUpdatedBy: updatedBy,
        roleUpdateReason: reason || 'Promoted to Super Administrator'
      };

      await profileService.updateProfile(userId, updateData);

      // Log the role change
      await this.logRoleChange({
        userId,
        previousRole,
        newRole: 'super_admin',
        updatedBy,
        reason,
        formData: userTypeForm,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        message: 'User successfully promoted to Super Administrator',
        previousRole,
        newRole: 'super_admin'
      };

    } catch (error) {
      console.error('Error updating user to super admin:', error);
      return {
        success: false,
        message: `Failed to update user role: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Update user role to admin
   */
  static async updateToAdmin(userId: string, updatedBy: string, reason?: string): Promise<RoleUpdateResult> {
    try {
      const currentProfile = await profileService.getProfile(userId);
      
      if (!currentProfile) {
        return {
          success: false,
          message: 'User profile not found'
        };
      }

      const previousRole = this.determineCurrentRole(currentProfile);

      const updateData = {
        isSuperAdmin: false,
        isAdmin: true,
        isVendor: currentProfile.isVendor || false,
        role: 'ADMIN' as const,
        permissions: this.getAdminPermissions(),
        lastRoleUpdate: new Date().toISOString(),
        roleUpdatedBy: updatedBy,
        roleUpdateReason: reason || 'Promoted to Administrator'
      };

      await profileService.updateProfile(userId, updateData);

      await this.logRoleChange({
        userId,
        previousRole,
        newRole: 'admin',
        updatedBy,
        reason,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        message: 'User successfully promoted to Administrator',
        previousRole,
        newRole: 'admin'
      };

    } catch (error) {
      console.error('Error updating user to admin:', error);
      return {
        success: false,
        message: `Failed to update user role: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Update user role to vendor
   */
  static async updateToVendor(userId: string, updatedBy: string, reason?: string): Promise<RoleUpdateResult> {
    try {
      const currentProfile = await profileService.getProfile(userId);
      
      if (!currentProfile) {
        return {
          success: false,
          message: 'User profile not found'
        };
      }

      const previousRole = this.determineCurrentRole(currentProfile);

      const updateData = {
        isSuperAdmin: false,
        isAdmin: false,
        isVendor: true,
        role: 'VENDOR' as const,
        permissions: this.getVendorPermissions(),
        lastRoleUpdate: new Date().toISOString(),
        roleUpdatedBy: updatedBy,
        roleUpdateReason: reason || 'Promoted to Vendor'
      };

      await profileService.updateProfile(userId, updateData);

      await this.logRoleChange({
        userId,
        previousRole,
        newRole: 'vendor',
        updatedBy,
        reason,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        message: 'User successfully promoted to Vendor',
        previousRole,
        newRole: 'vendor'
      };

    } catch (error) {
      console.error('Error updating user to vendor:', error);
      return {
        success: false,
        message: `Failed to update user role: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Demote user back to customer
   */
  static async updateToCustomer(userId: string, updatedBy: string, reason?: string): Promise<RoleUpdateResult> {
    try {
      const currentProfile = await profileService.getProfile(userId);
      
      if (!currentProfile) {
        return {
          success: false,
          message: 'User profile not found'
        };
      }

      const previousRole = this.determineCurrentRole(currentProfile);

      const updateData = {
        isSuperAdmin: false,
        isAdmin: false,
        isVendor: false,
        role: 'CUSTOMER' as const,
        permissions: this.getCustomerPermissions(),
        lastRoleUpdate: new Date().toISOString(),
        roleUpdatedBy: updatedBy,
        roleUpdateReason: reason || 'Demoted to Customer'
      };

      await profileService.updateProfile(userId, updateData);

      await this.logRoleChange({
        userId,
        previousRole,
        newRole: 'customer',
        updatedBy,
        reason,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        message: 'User role updated to Customer',
        previousRole,
        newRole: 'customer'
      };

    } catch (error) {
      console.error('Error updating user to customer:', error);
      return {
        success: false,
        message: `Failed to update user role: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Determine current user role from profile
   */
  private static determineCurrentRole(profile: any): UserRole {
    if (profile.isSuperAdmin || profile.role === 'SUPER_ADMIN') {
      return 'super_admin';
    }
    if (profile.isAdmin || profile.role === 'ADMIN') {
      return 'admin';
    }
    if (profile.isVendor || profile.role === 'VENDOR') {
      return 'vendor';
    }
    return 'customer';
  }

  /**
   * Get super admin permissions
   */
  private static getSuperAdminPermissions(): string[] {
    return [
      'system:manage',
      'users:manage',
      'admins:manage',
      'platform:configure',
      'data:backup',
      'security:manage',
      'integrations:manage',
      'logs:view',
      'analytics:view',
      'content:moderate',
      'vendors:verify',
      'disputes:resolve',
      'reviews:moderate',
      'contacts:manage'
    ];
  }

  /**
   * Get admin permissions
   */
  private static getAdminPermissions(): string[] {
    return [
      'users:manage',
      'content:moderate',
      'vendors:verify',
      'disputes:resolve',
      'reviews:moderate',
      'contacts:manage',
      'analytics:view',
      'reports:generate'
    ];
  }

  /**
   * Get vendor permissions
   */
  private static getVendorPermissions(): string[] {
    return [
      'vendors:manage',
      'venues:manage',
      'shop:manage',
      'inquiries:manage',
      'bookings:manage',
      'reviews:respond',
      'content:create',
      'gallery:manage'
    ];
  }

  /**
   * Get customer permissions
   */
  private static getCustomerPermissions(): string[] {
    return [
      'bookings:create',
      'reviews:write',
      'vendors:save',
      'gallery:create',
      'profile:manage'
    ];
  }

  /**
   * Create default form data for role updates
   */
  private static createDefaultFormData(
    formType: 'customer' | 'vendor' | 'admin' | 'bulk_import' | 'migration',
    submittedBy: string,
    additionalData?: any
  ): UserTypeFormData {
    const baseFormData: UserTypeFormData = {
      formType,
      submissionDate: new Date().toISOString(),
      formVersion: '1.0',
      verificationStatus: 'verified',
      approvalStatus: 'approved',
      submittedBy,
      reviewedBy: submittedBy,
      reviewDate: new Date().toISOString(),
      reviewNotes: `Role updated via ${formType} form`
    };

    switch (formType) {
      case 'admin':
        baseFormData.adminDetails = {
          adminLevel: additionalData?.adminLevel || 'admin',
          designation: additionalData?.designation || 'Administrator',
          accessLevel: additionalData?.accessLevel || 'platform_admin',
          responsibilities: additionalData?.responsibilities || ['user_management', 'content_moderation'],
          ...additionalData
        };
        break;

      case 'vendor':
        baseFormData.businessDetails = {
          businessName: additionalData?.businessName || 'Business Account',
          businessType: additionalData?.businessType || 'general',
          businessCategory: additionalData?.businessCategory || 'wedding_services',
          ...additionalData
        };
        break;

      case 'customer':
        baseFormData.personalDetails = {
          firstName: additionalData?.firstName || 'Customer',
          lastName: additionalData?.lastName || 'Account',
          ...additionalData
        };
        break;
    }

    return baseFormData;
  }

  /**
   * Log role changes for audit trail
   */
  private static async logRoleChange(changeData: {
    userId: string;
    previousRole: UserRole;
    newRole: UserRole;
    updatedBy: string;
    reason?: string;
    formData?: UserTypeFormData;
    timestamp: string;
  }): Promise<void> {
    try {
      // In a real implementation, you would save this to a role_changes table
      // For now, we'll just log it to console
      console.log('Role Change Log:', {
        userId: changeData.userId,
        change: `${changeData.previousRole} → ${changeData.newRole}`,
        updatedBy: changeData.updatedBy,
        reason: changeData.reason,
        formType: changeData.formData?.formType,
        verificationStatus: changeData.formData?.verificationStatus,
        timestamp: changeData.timestamp
      });

      // TODO: Implement actual database logging
      // await roleChangeLogService.create(changeData);
    } catch (error) {
      console.error('Failed to log role change:', error);
    }
  }

  /**
   * Bulk update multiple users (for super admin use)
   */
  static async bulkUpdateRoles(updates: RoleUpdateData[]): Promise<RoleUpdateResult[]> {
    const results: RoleUpdateResult[] = [];

    for (const update of updates) {
      let result: RoleUpdateResult;

      switch (update.newRole) {
        case 'super_admin':
          result = await this.updateToSuperAdmin(update.userId, update.updatedBy, update.reason);
          break;
        case 'admin':
          result = await this.updateToAdmin(update.userId, update.updatedBy, update.reason);
          break;
        case 'vendor':
          result = await this.updateToVendor(update.userId, update.updatedBy, update.reason);
          break;
        case 'customer':
          result = await this.updateToCustomer(update.userId, update.updatedBy, update.reason);
          break;
        default:
          result = {
            success: false,
            message: `Invalid role: ${update.newRole}`
          };
      }

      results.push(result);
    }

    return results;
  }

  /**
   * Check if user has permission to update roles
   */
  static async canUpdateRole(updaterUserId: string, targetRole: UserRole): Promise<boolean> {
    try {
      const updaterProfile = await profileService.getProfile(updaterUserId);
      if (!updaterProfile) return false;

      const updaterRole = this.determineCurrentRole(updaterProfile);

      // Only super admins can create other super admins
      if (targetRole === 'super_admin') {
        return updaterRole === 'super_admin';
      }

      // Super admins and admins can create admins and vendors
      if (targetRole === 'admin' || targetRole === 'vendor') {
        return updaterRole === 'super_admin' || updaterRole === 'admin';
      }

      // Anyone with admin+ privileges can demote to customer
      if (targetRole === 'customer') {
        return updaterRole === 'super_admin' || updaterRole === 'admin';
      }

      return false;
    } catch (error) {
      console.error('Error checking role update permissions:', error);
      return false;
    }
  }
}

export default UserRoleService;
