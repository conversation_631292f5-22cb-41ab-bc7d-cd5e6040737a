"use client"

import { generateClient } from '@aws-amplify/api'
import { createPayment, updatePayment } from '@/src/graphql/mutations'
import { getPayment, listPayments } from '@/src/graphql/queries'
import { getCurrentUser } from 'aws-amplify/auth'
import { OrderService } from './orderService'

const client = generateClient()

export interface RazorpayOptions {
  key: string
  amount: number
  currency: string
  name: string
  description: string
  image?: string
  order_id: string
  handler: (response: RazorpayResponse) => void
  prefill?: {
    name?: string
    email?: string
    contact?: string
  }
  theme?: {
    color?: string
  }
  modal?: {
    ondismiss?: () => void
  }
}

export interface RazorpayResponse {
  razorpay_payment_id: string
  razorpay_order_id: string
  razorpay_signature: string
}

export interface CreatePaymentInput {
  orderId: string
  amount: number
  currency: string
  paymentMethod: 'RAZORPAY' | 'COD' | 'UPI' | 'CREDIT_CARD' | 'DEBIT_CARD' | 'NET_BANKING' | 'WALLET'
  razorpayOrderId?: string
  codAmount?: number
}

declare global {
  interface Window {
    Razorpay: any
  }
}

export class PaymentService {
  private static RAZORPAY_KEY = process.env.NEXT_PUBLIC_RAZORPAY_KEY || 'rzp_test_YourKeyHere'

  /**
   * Create Razorpay order
   */
  static async createRazorpayOrder(amount: number, currency: string = 'INR'): Promise<{ success: boolean; orderId?: string; message: string }> {
    try {
      // In a real implementation, this would call your backend API
      // which would create a Razorpay order using their server-side API
      
      // For now, we'll simulate the order creation
      const orderId = `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      
      return {
        success: true,
        orderId,
        message: 'Razorpay order created successfully'
      }
    } catch (error) {
      console.error('Create Razorpay order error:', error)
      return {
        success: false,
        message: 'Failed to create Razorpay order'
      }
    }
  }

  /**
   * Initialize Razorpay payment
   */
  static async initiateRazorpayPayment(
    orderId: string,
    amount: number,
    customerInfo: { name: string; email: string; phone: string }
  ): Promise<{ success: boolean; message: string }> {
    try {
      if (!window.Razorpay) {
        throw new Error('Razorpay SDK not loaded')
      }

      // Create Razorpay order
      const razorpayOrderResult = await this.createRazorpayOrder(amount * 100) // Convert to paise
      if (!razorpayOrderResult.success) {
        throw new Error(razorpayOrderResult.message)
      }

      // Create payment record
      const paymentResult = await this.createPayment({
        orderId,
        amount,
        currency: 'INR',
        paymentMethod: 'RAZORPAY',
        razorpayOrderId: razorpayOrderResult.orderId
      })

      if (!paymentResult.success) {
        throw new Error(paymentResult.message)
      }

      return new Promise((resolve) => {
        const options: RazorpayOptions = {
          key: this.RAZORPAY_KEY,
          amount: amount * 100, // Amount in paise
          currency: 'INR',
          name: 'Thirumanam360',
          description: 'Wedding Shop Payment',
          image: '/logo.svg',
          order_id: razorpayOrderResult.orderId!,
          handler: async (response: RazorpayResponse) => {
            try {
              // Update payment with Razorpay response
              await this.updatePaymentWithRazorpayResponse(
                paymentResult.payment!.id,
                response
              )

              // Update order status
              await OrderService.updateOrderStatus(orderId, 'CONFIRMED', {
                paymentStatus: 'PAID',
                razorpayPaymentId: response.razorpay_payment_id,
                razorpaySignature: response.razorpay_signature,
                transactionId: response.razorpay_payment_id
              })

              resolve({ success: true, message: 'Payment successful' })
            } catch (error) {
              console.error('Payment handler error:', error)
              resolve({ success: false, message: 'Payment processing failed' })
            }
          },
          prefill: {
            name: customerInfo.name,
            email: customerInfo.email,
            contact: customerInfo.phone
          },
          theme: {
            color: '#610f13'
          },
          modal: {
            ondismiss: () => {
              resolve({ success: false, message: 'Payment cancelled by user' })
            }
          }
        }

        const rzp = new window.Razorpay(options)
        rzp.open()
      })

    } catch (error) {
      console.error('Initiate Razorpay payment error:', error)
      return {
        success: false,
        message: error.message || 'Failed to initiate payment'
      }
    }
  }

  /**
   * Process COD order
   */
  static async processCODOrder(orderId: string, amount: number): Promise<{ success: boolean; message: string }> {
    try {
      // Create payment record for COD
      const paymentResult = await this.createPayment({
        orderId,
        amount,
        currency: 'INR',
        paymentMethod: 'COD',
        codAmount: amount
      })

      if (!paymentResult.success) {
        throw new Error(paymentResult.message)
      }

      // Update order status for COD
      try {
        await OrderService.updateOrderStatus(orderId, 'CONFIRMED', {
          paymentStatus: 'COD_PENDING'
        })
      } catch (updateError) {
        console.log('GraphQL update failed, using simple update:', updateError.message)
        // Fallback to simple update method
        await OrderService.updateOrderStatusSimple(orderId, 'CONFIRMED', {
          paymentStatus: 'COD_PENDING'
        })
      }

      return {
        success: true,
        message: 'COD order processed successfully'
      }

    } catch (error) {
      console.error('Process COD order error:', error)
      return {
        success: false,
        message: error.message || 'Failed to process COD order'
      }
    }
  }

  /**
   * Create payment record
   */
  static async createPayment(paymentData: CreatePaymentInput): Promise<{ success: boolean; payment?: any; message: string }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      const paymentInput = {
        orderId: paymentData.orderId,
        userId: user.userId,
        amount: paymentData.amount,
        currency: paymentData.currency,
        paymentMethod: paymentData.paymentMethod,
        status: 'INITIATED',
        razorpayOrderId: paymentData.razorpayOrderId,
        codAmount: paymentData.codAmount,
        codCollected: false,
        initiatedAt: new Date().toISOString()
      }

      const result = await client.graphql({
        query: createPayment,
        variables: { input: paymentInput },
        authMode: 'userPool'
      })

      return {
        success: true,
        payment: result.data.createPayment,
        message: 'Payment record created successfully'
      }

    } catch (error) {
      console.error('Create payment error:', error)
      throw new Error(`Failed to create payment: ${error.message}`)
    }
  }

  /**
   * Update payment with Razorpay response
   */
  static async updatePaymentWithRazorpayResponse(
    paymentId: string,
    razorpayResponse: RazorpayResponse
  ): Promise<{ success: boolean; payment?: any; message: string }> {
    try {
      const updateInput = {
        id: paymentId,
        status: 'SUCCESS',
        razorpayPaymentId: razorpayResponse.razorpay_payment_id,
        razorpaySignature: razorpayResponse.razorpay_signature,
        transactionId: razorpayResponse.razorpay_payment_id,
        completedAt: new Date().toISOString(),
        gatewayResponse: razorpayResponse
      }

      const result = await client.graphql({
        query: updatePayment,
        variables: { input: updateInput },
        authMode: 'userPool'
      })

      return {
        success: true,
        payment: result.data.updatePayment,
        message: 'Payment updated successfully'
      }

    } catch (error) {
      console.error('Update payment error:', error)
      throw new Error(`Failed to update payment: ${error.message}`)
    }
  }

  /**
   * Mark COD as collected
   */
  static async markCODCollected(paymentId: string): Promise<{ success: boolean; payment?: any; message: string }> {
    try {
      const updateInput = {
        id: paymentId,
        status: 'SUCCESS',
        codCollected: true,
        codCollectedDate: new Date().toISOString(),
        completedAt: new Date().toISOString()
      }

      const result = await client.graphql({
        query: updatePayment,
        variables: { input: updateInput },
        authMode: 'userPool'
      })

      return {
        success: true,
        payment: result.data.updatePayment,
        message: 'COD marked as collected'
      }

    } catch (error) {
      console.error('Mark COD collected error:', error)
      throw new Error(`Failed to mark COD as collected: ${error.message}`)
    }
  }

  /**
   * Get payment by order ID
   */
  static async getPaymentByOrderId(orderId: string): Promise<{ success: boolean; payment?: any; message: string }> {
    try {
      const result = await client.graphql({
        query: listPayments,
        variables: {
          filter: { orderId: { eq: orderId } },
          limit: 1
        },
        authMode: 'userPool'
      })

      const payment = result.data.listPayments.items[0]

      return {
        success: true,
        payment,
        message: payment ? 'Payment retrieved successfully' : 'Payment not found'
      }

    } catch (error) {
      console.error('Get payment error:', error)
      throw new Error(`Failed to get payment: ${error.message}`)
    }
  }
}
