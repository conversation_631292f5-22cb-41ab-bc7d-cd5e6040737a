import { generateClient } from 'aws-amplify/api';
import { getCurrentUser } from 'aws-amplify/auth';
import { createUserProfile, updateUserProfile, deleteUserProfile } from '@/src/graphql/mutations';
import { listUserProfiles, getUserProfile, userProfilesByUserId } from '@/src/graphql/queries';

// Create client with user pool authentication
const client = generateClient({
  authMode: 'userPool'
});

export interface ProfileSocialMedia {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  linkedin?: string;
  youtube?: string;
}

export interface NotificationSettings {
  email?: boolean;
  sms?: boolean;
  push?: boolean;
  marketing?: boolean;
}

export interface PrivacySettings {
  profileVisibility?: 'PUBLIC' | 'PRIVATE' | 'FRIENDS_ONLY';
  contactVisibility?: 'PUBLIC' | 'PRIVATE' | 'VERIFIED_ONLY';
  showOnlineStatus?: boolean;
}

export interface ProfilePreferences {
  language?: string;
  currency?: string;
  timezone?: string;
  notifications?: NotificationSettings;
  privacy?: PrivacySettings;
}

export interface BusinessInfo {
  businessName?: string;
  businessType?: string;
  businessAddress?: string;
  businessPhone?: string;
  businessEmail?: string;
  businessWebsite?: string;
  gstNumber?: string;
  panNumber?: string;
  businessLicense?: string;
}

export interface UserProfileInput {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: 'MALE' | 'FEMALE' | 'OTHER' | 'PREFER_NOT_TO_SAY';
  address?: string;
  city?: string;
  state?: string;
  pincode?: string;
  country?: string;
  profilePhoto?: string;
  bio?: string;
  website?: string;
  socialMedia?: ProfileSocialMedia;
  preferences?: ProfilePreferences;
  businessInfo?: BusinessInfo;
  isVendor?: boolean;
}

export interface UserProfileResponse extends UserProfileInput {
  id: string;
  userId: string;
  isVerified?: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserProfileUpdateInput extends Partial<UserProfileInput> {
  id: string;
}

class ProfileService {
  /**
   * Get current user ID
   */
  private async getCurrentUserId(): Promise<string> {
    try {
      const user = await getCurrentUser();
      return user.userId;
    } catch (error) {
      throw new Error('User not authenticated');
    }
  }

  /**
   * Clean object by removing undefined, null, and empty string values
   */
  private cleanObject(obj: any): any {
    if (obj === null || obj === undefined) return undefined;

    if (typeof obj !== 'object') return obj;

    if (Array.isArray(obj)) {
      return obj.filter(item => item !== null && item !== undefined);
    }

    const cleaned: any = {};
    for (const [key, value] of Object.entries(obj)) {
      if (value === null || value === undefined || value === '') {
        continue; // Skip null, undefined, and empty strings
      }

      if (typeof value === 'object') {
        const cleanedValue = this.cleanObject(value);
        if (cleanedValue !== undefined && Object.keys(cleanedValue).length > 0) {
          cleaned[key] = cleanedValue;
        }
      } else {
        cleaned[key] = value;
      }
    }

    return Object.keys(cleaned).length > 0 ? cleaned : undefined;
  }

  /**
   * Format date of birth for AWSDate (YYYY-MM-DD format)
   */
  private formatDateOfBirth(dateString?: string): string | undefined {
    if (!dateString) return undefined;

    // If it's already in YYYY-MM-DD format, return as is
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
      return dateString;
    }

    // Try to parse and format the date
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return undefined;

      return date.toISOString().split('T')[0]; // Extract YYYY-MM-DD part
    } catch {
      return undefined;
    }
  }

  /**
   * Create user profile automatically from Cognito user data
   */
  async createProfileFromCognitoUser(cognitoUser: any, additionalData?: Partial<UserProfileInput>): Promise<UserProfileResponse> {
    try {
      const userId = cognitoUser.userId || cognitoUser.username;
      const email = cognitoUser.signInDetails?.loginId || cognitoUser.username;
      const name = cognitoUser.attributes?.name || '';

      // Parse name
      const nameParts = name.trim().split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      // Check if this is a business user (name contains parentheses)
      const isBusinessUser = name.includes('(') && name.includes(')');
      let businessInfo = null;
      let actualFirstName = firstName;
      let actualLastName = lastName;

      if (isBusinessUser) {
        const businessMatch = name.match(/^(.+?)\s*\((.+?)\)$/);
        if (businessMatch) {
          const fullName = businessMatch[1].trim();
          const businessName = businessMatch[2].trim();

          const fullNameParts = fullName.split(' ');
          actualFirstName = fullNameParts[0] || '';
          actualLastName = fullNameParts.slice(1).join(' ') || '';

          businessInfo = {
            businessName: businessName,
            businessType: 'Wedding Services',
            businessEmail: email,
            businessPhone: cognitoUser.attributes?.phone_number
          };
        }
      }

      const profileData: UserProfileInput = {
        firstName: actualFirstName,
        lastName: actualLastName,
        email: email,
        phone: cognitoUser.attributes?.phone_number,
        country: 'India',
        isVendor: isBusinessUser,
        businessInfo: businessInfo,
        preferences: {
          language: 'en',
          currency: 'INR',
          timezone: 'Asia/Kolkata',
          notifications: {
            email: true,
            sms: true,
            push: true,
            marketing: false
          },
          privacy: {
            profileVisibility: 'PUBLIC',
            contactVisibility: 'PUBLIC',
            showOnlineStatus: true
          }
        },
        ...additionalData
      };

      return await this.createProfile(profileData);
    } catch (error) {
      console.error('Error creating profile from Cognito user:', error);
      throw new Error('Failed to create profile from Cognito user');
    }
  }

  /**
   * Create user profile
   */
  async createProfile(profileData: UserProfileInput): Promise<UserProfileResponse> {
    try {
      const userId = await this.getCurrentUserId();

      const input = {
        userId,
        firstName: profileData.firstName,
        lastName: profileData.lastName,
        email: profileData.email,
        phone: profileData.phone,
        dateOfBirth: this.formatDateOfBirth(profileData.dateOfBirth),
        gender: profileData.gender,
        address: profileData.address,
        city: profileData.city,
        state: profileData.state,
        pincode: profileData.pincode,
        country: profileData.country || 'India',
        profilePhoto: profileData.profilePhoto,
        bio: profileData.bio,
        website: profileData.website,
        socialMedia: this.cleanObject(profileData.socialMedia),
        preferences: this.cleanObject(profileData.preferences) || {
          language: 'en',
          currency: 'INR',
          timezone: 'Asia/Kolkata',
          notifications: {
            email: true,
            sms: true,
            push: true,
            marketing: false
          },
          privacy: {
            profileVisibility: 'PUBLIC',
            contactVisibility: 'PUBLIC',
            showOnlineStatus: true
          }
        },
        businessInfo: this.cleanObject(profileData.businessInfo),
        isVendor: profileData.isVendor || false,
        isAdmin: false,
        isSuperAdmin: false,
        role: profileData.isVendor ? 'VENDOR' : 'CUSTOMER',
        accountType: profileData.isVendor ? 'BUSINESS' : 'PERSONAL',
        isVerified: false,
        lastLoginAt: new Date().toISOString()
      };

      // Clean the entire input object
      const cleanInput = this.cleanObject(input);

      console.log('Creating profile with input:', JSON.stringify(cleanInput, null, 2));

      const result = await client.graphql({
        query: createUserProfile,
        variables: { input: cleanInput }
      });

      return result.data.createUserProfile;
    } catch (error) {
      console.error('Error creating profile:', error);
      throw new Error('Failed to create profile');
    }
  }

  /**
   * Get user profile by user ID
   */
  async getProfile(userId?: string): Promise<UserProfileResponse | null> {
    try {
      const userIdToUse = userId || await this.getCurrentUserId();

      const result = await client.graphql({
        query: userProfilesByUserId,
        variables: { userId: userIdToUse }
      });

      const profiles = result.data.userProfilesByUserId.items;
      return profiles.length > 0 ? profiles[0] : null;
    } catch (error) {
      console.error('Error fetching profile:', error);
      throw new Error('Failed to fetch profile');
    }
  }

  /**
   * Get current user's profile
   */
  async getCurrentUserProfile(): Promise<UserProfileResponse | null> {
    try {
      const userId = await this.getCurrentUserId();
      return await this.getProfile(userId);
    } catch (error) {
      console.error('Error fetching current user profile:', error);
      throw new Error('Failed to fetch current user profile');
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(profileData: UserProfileUpdateInput): Promise<UserProfileResponse> {
    try {
      const userId = await this.getCurrentUserId();

      // First verify that the profile belongs to the current user
      const existingProfile = await this.getProfile(userId);
      if (!existingProfile || existingProfile.userId !== userId) {
        throw new Error('Unauthorized: You can only update your own profile');
      }

      const input = {
        id: profileData.id,
        ...profileData,
        dateOfBirth: this.formatDateOfBirth(profileData.dateOfBirth),
        socialMedia: this.cleanObject(profileData.socialMedia),
        preferences: this.cleanObject(profileData.preferences),
        businessInfo: this.cleanObject(profileData.businessInfo),
        lastLoginAt: new Date().toISOString()
      };

      // Clean the entire input object
      const cleanInput = this.cleanObject(input);

      console.log('Updating profile with input:', JSON.stringify(cleanInput, null, 2));

      const result = await client.graphql({
        query: updateUserProfile,
        variables: { input: cleanInput }
      });

      return result.data.updateUserProfile;
    } catch (error) {
      console.error('Error updating profile:', error);
      throw new Error('Failed to update profile');
    }
  }

  /**
   * Delete user profile
   */
  async deleteProfile(profileId: string): Promise<boolean> {
    try {
      const userId = await this.getCurrentUserId();

      // First verify that the profile belongs to the current user
      const existingProfile = await this.getProfile(userId);
      if (!existingProfile || existingProfile.userId !== userId) {
        throw new Error('Unauthorized: You can only delete your own profile');
      }

      await client.graphql({
        query: deleteUserProfile,
        variables: { input: { id: profileId } }
      });

      return true;
    } catch (error) {
      console.error('Error deleting profile:', error);
      throw new Error('Failed to delete profile');
    }
  }

  /**
   * Update profile photo
   */
  async updateProfilePhoto(profileId: string, photoUrl: string): Promise<UserProfileResponse> {
    try {
      return await this.updateProfile({
        id: profileId,
        profilePhoto: photoUrl
      });
    } catch (error) {
      console.error('Error updating profile photo:', error);
      throw new Error('Failed to update profile photo');
    }
  }

  /**
   * Update notification preferences
   */
  async updateNotificationPreferences(
    profileId: string, 
    notifications: NotificationSettings
  ): Promise<UserProfileResponse> {
    try {
      const existingProfile = await this.getCurrentUserProfile();
      if (!existingProfile) {
        throw new Error('Profile not found');
      }

      const updatedPreferences = {
        ...existingProfile.preferences,
        notifications
      };

      return await this.updateProfile({
        id: profileId,
        preferences: updatedPreferences
      });
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      throw new Error('Failed to update notification preferences');
    }
  }

  /**
   * Update privacy settings
   */
  async updatePrivacySettings(
    profileId: string, 
    privacy: PrivacySettings
  ): Promise<UserProfileResponse> {
    try {
      const existingProfile = await this.getCurrentUserProfile();
      if (!existingProfile) {
        throw new Error('Profile not found');
      }

      const updatedPreferences = {
        ...existingProfile.preferences,
        privacy
      };

      return await this.updateProfile({
        id: profileId,
        preferences: updatedPreferences
      });
    } catch (error) {
      console.error('Error updating privacy settings:', error);
      throw new Error('Failed to update privacy settings');
    }
  }

  /**
   * Update business information
   */
  async updateBusinessInfo(
    profileId: string, 
    businessInfo: BusinessInfo
  ): Promise<UserProfileResponse> {
    try {
      return await this.updateProfile({
        id: profileId,
        businessInfo
      });
    } catch (error) {
      console.error('Error updating business info:', error);
      throw new Error('Failed to update business information');
    }
  }
}

export const profileService = new ProfileService();
export default profileService;
