/**
 * Hybrid Mobile Authentication Service
 * Works with existing email-based User Pool by using phone numbers as custom attributes
 * and implementing OTP verification through a separate mechanism
 */

import { signUp, confirmSignUp, resendSignUpCode, signIn } from 'aws-amplify/auth';
import AWS from 'aws-sdk';
const sns = new AWS.SNS({ region: 'ap-south-1' });

async function sendSMS(phoneNumber: string, message: string) {
  const params = {
    Message: message,
    PhoneNumber: phoneNumber,
    MessageAttributes: {
      'AWS.SNS.SMS.SMSType': {
        DataType: 'String',
        StringValue: 'Transactional'
      }
    }
  };
  return sns.publish(params).promise();
}

export interface OTPResponse {
  success: boolean;
  message: string;
  session?: string;
  deliveryMedium?: 'SMS' | 'EMAIL';
}

export interface VerifyOTPResponse {
  success: boolean;
  message: string;
  user?: any;
  isNewUser?: boolean;
}

export class HybridMobileAuthService {
  
  // Store OTP sessions temporarily (in production, use Redis or DynamoDB)
  private static otpSessions = new Map<string, {
    otp: string;
    phoneNumber: string;
    countryCode: string;
    expiresAt: number;
    attempts: number;
  }>();

  /**
   * Generate a 6-digit OTP
   */
  private static generateOTP(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  /**
   * Generate session ID
   */
  private static generateSessionId(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }

  /**
   * Send OTP to mobile number
   */
  static async sendMobileOTP(phoneNumber: string, countryCode: string = '+91'): Promise<OTPResponse> {
    try {
      const formattedPhone = `${countryCode}${phoneNumber.replace(/^0+/, '')}`;
      const otp = this.generateOTP();
      const sessionId = this.generateSessionId();
      
      // Store OTP session (expires in 5 minutes)
      this.otpSessions.set(sessionId, {
        otp,
        phoneNumber,
        countryCode,
        expiresAt: Date.now() + (5 * 60 * 1000), // 5 minutes
        attempts: 0
      });

      // Send real SMS via AWS SNS
      await sendSMS(formattedPhone, `Your Thirumanam 360 OTP is: ${otp}`);
      
      return {
        success: true,
        message: 'OTP sent to your mobile number',
        session: sessionId,
        deliveryMedium: 'SMS'
      };
      
    } catch (error: any) {
      console.error('Send mobile OTP error:', error);
      return {
        success: false,
        message: `Failed to send OTP: ${error.message || 'Unknown error'}`
      };
    }
  }

  /**
   * Verify mobile OTP and create/login user
   */
  static async verifyMobileOTP(sessionId: string, otp: string): Promise<VerifyOTPResponse> {
    try {
      const session = this.otpSessions.get(sessionId);
      
      if (!session) {
        return {
          success: false,
          message: 'Invalid or expired session. Please request a new OTP.'
        };
      }

      // Check if session expired
      if (Date.now() > session.expiresAt) {
        this.otpSessions.delete(sessionId);
        return {
          success: false,
          message: 'OTP has expired. Please request a new one.'
        };
      }

      // Check attempts
      session.attempts++;
      if (session.attempts > 3) {
        this.otpSessions.delete(sessionId);
        return {
          success: false,
          message: 'Too many failed attempts. Please request a new OTP.'
        };
      }

      // Verify OTP
      if (session.otp !== otp) {
        return {
          success: false,
          message: 'Invalid OTP. Please try again.'
        };
      }

      // OTP is valid, create/login user
      const formattedPhone = `${session.countryCode}${session.phoneNumber.replace(/^0+/, '')}`;
      const userEmail = `mobile_${session.phoneNumber}@thirumanam360.com`;
      
      try {
        // Try to create user account
        const signUpResult = await signUp({
          username: userEmail,
          password: this.generateTemporaryPassword(),
          options: {
            userAttributes: {
              email: userEmail,
              phone_number: formattedPhone,
              'custom:registration_type': 'mobile_otp',
              'custom:primary_contact': formattedPhone,
              'custom:phone_verified': 'true'
            },
            autoSignIn: {
              enabled: true
            }
          }
        });

        // Auto-confirm the user since we've verified the phone
        await confirmSignUp({
          username: userEmail,
          confirmationCode: '000000' // Use a dummy code since we're auto-confirming
        });

        // Clean up session
        this.otpSessions.delete(sessionId);

        return {
          success: true,
          message: 'Account created and logged in successfully',
          user: signUpResult,
          isNewUser: true
        };

      } catch (signUpError: any) {
        if (signUpError.name === 'UsernameExistsException') {
          // User exists, try to sign them in
          try {
            const signInResult = await signIn({
              username: userEmail,
              password: this.generateTemporaryPassword()
            });

            // Clean up session
            this.otpSessions.delete(sessionId);

            return {
              success: true,
              message: 'Logged in successfully',
              user: signInResult,
              isNewUser: false
            };
          } catch (signInError: any) {
            return {
              success: false,
              message: 'Account exists but login failed. Please try again.'
            };
          }
        }

        return {
          success: false,
          message: `Account creation failed: ${signUpError.message}`
        };
      }

    } catch (error: any) {
      console.error('Verify mobile OTP error:', error);
      return {
        success: false,
        message: `OTP verification failed: ${error.message || 'Unknown error'}`
      };
    }
  }

  /**
   * Resend OTP
   */
  static async resendOTP(sessionId: string): Promise<OTPResponse> {
    try {
      const session = this.otpSessions.get(sessionId);
      
      if (!session) {
        return {
          success: false,
          message: 'Invalid session. Please start over.'
        };
      }

      // Generate new OTP and extend session
      const newOtp = this.generateOTP();
      session.otp = newOtp;
      session.expiresAt = Date.now() + (5 * 60 * 1000); // Extend by 5 minutes
      session.attempts = 0; // Reset attempts

      const formattedPhone = `${session.countryCode}${session.phoneNumber.replace(/^0+/, '')}`;
      
      // Simulate SMS sending
      console.log(`📱 Resent SMS OTP for ${formattedPhone}: ${newOtp}`);
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        message: 'OTP resent to your mobile number',
        session: sessionId,
        deliveryMedium: 'SMS'
      };

    } catch (error: any) {
      console.error('Resend OTP error:', error);
      return {
        success: false,
        message: `Failed to resend OTP: ${error.message || 'Unknown error'}`
      };
    }
  }

  /**
   * Generate temporary password for user accounts
   */
  private static generateTemporaryPassword(): string {
    // Generate a complex password that meets Cognito requirements
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password + 'A1!'; // Ensure it meets complexity requirements
  }

  /**
   * Validate phone number format
   */
  static validatePhoneNumber(phoneNumber: string): boolean {
    // Indian mobile number validation (10 digits)
    const phoneRegex = /^[6-9]\d{9}$/;
    return phoneRegex.test(phoneNumber.replace(/\D/g, ''));
  }

  /**
   * Format phone number for display
   */
  static formatPhoneNumber(phoneNumber: string): string {
    const cleaned = phoneNumber.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `${cleaned.slice(0, 5)} ${cleaned.slice(5)}`;
    }
    return phoneNumber;
  }

  /**
   * Get supported country codes
   */
  static getSupportedCountryCodes(): Array<{code: string, name: string, flag: string}> {
    return [
      { code: '+91', name: 'India', flag: '🇮🇳' },
      { code: '+1', name: 'United States', flag: '🇺🇸' },
      { code: '+44', name: 'United Kingdom', flag: '🇬🇧' },
      { code: '+971', name: 'UAE', flag: '🇦🇪' },
      { code: '+65', name: 'Singapore', flag: '🇸🇬' },
      { code: '+60', name: 'Malaysia', flag: '🇲🇾' },
    ];
  }

  /**
   * Clean up expired sessions (call periodically)
   */
  static cleanupExpiredSessions(): void {
    const now = Date.now();
    for (const [sessionId, session] of this.otpSessions.entries()) {
      if (now > session.expiresAt) {
        this.otpSessions.delete(sessionId);
      }
    }
  }
}

export default HybridMobileAuthService;
