"use client"

import { generateClient } from '@aws-amplify/api'
import { createFavorite, deleteFavorite, updateFavorite } from '@/src/graphql/mutations'
import { listFavorites, getFavorite } from '@/src/graphql/queries'
import { getCurrentUser } from 'aws-amplify/auth'

const client = generateClient()

export interface FavoriteInput {
  entityId: string
  entityType: 'VENDOR' | 'VENUE' | 'SHOP_ITEM'
  entityName: string
  entityImage?: string
  entityPrice?: string
  entityLocation?: string
  entityCity?: string
  entityState?: string
  entityRating?: number
  entityReviewCount?: number
  entityDescription?: string
  notes?: string
}

export interface FavoriteItem {
  id: string
  userId: string
  entityId: string
  entityType: 'VENDOR' | 'VENUE' | 'SHOP_ITEM'
  entityName: string
  entityImage?: string
  entityPrice?: string
  entityLocation?: string
  entityCity?: string
  entityState?: string
  entityRating?: number
  entityReviewCount?: number
  entityDescription?: string
  dateAdded: string
  notes?: string
  createdAt: string
  updatedAt: string
}

export class FavoritesService {
  /**
   * Add item to favorites
   */
  static async addToFavorites(favoriteData: FavoriteInput): Promise<{ success: boolean; favorite?: FavoriteItem; message: string }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      // Check if already favorited
      const existingFavorite = await this.isFavorited(favoriteData.entityId, favoriteData.entityType)
      if (existingFavorite) {
        return {
          success: false,
          message: 'Item is already in favorites'
        }
      }

      const favoriteInput = {
        userId: user.userId,
        entityId: favoriteData.entityId,
        entityType: favoriteData.entityType,
        entityName: favoriteData.entityName,
        entityImage: favoriteData.entityImage || '',
        entityPrice: favoriteData.entityPrice || '',
        entityLocation: favoriteData.entityLocation || '',
        entityCity: favoriteData.entityCity || '',
        entityState: favoriteData.entityState || '',
        entityRating: favoriteData.entityRating || 0,
        entityReviewCount: favoriteData.entityReviewCount || 0,
        entityDescription: favoriteData.entityDescription || '',
        dateAdded: new Date().toISOString(),
        notes: favoriteData.notes || ''
      }

      const result = await client.graphql({
        query: createFavorite,
        variables: { input: favoriteInput },
        authMode: 'userPool'
      })

      return {
        success: true,
        favorite: result.data.createFavorite,
        message: 'Added to favorites successfully'
      }

    } catch (error) {
      console.error('Add to favorites error:', error)
      throw new Error(`Failed to add to favorites: ${error.message}`)
    }
  }

  /**
   * Remove item from favorites
   */
  static async removeFromFavorites(favoriteId: string): Promise<{ success: boolean; message: string }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      await client.graphql({
        query: deleteFavorite,
        variables: { input: { id: favoriteId } },
        authMode: 'userPool'
      })

      return {
        success: true,
        message: 'Removed from favorites successfully'
      }

    } catch (error) {
      console.error('Remove from favorites error:', error)
      throw new Error(`Failed to remove from favorites: ${error.message}`)
    }
  }

  /**
   * Get user's favorites
   */
  static async getUserFavorites(entityType?: 'VENDOR' | 'VENUE' | 'SHOP_ITEM'): Promise<{ success: boolean; favorites: FavoriteItem[]; message: string }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      let filter: any = {
        userId: { eq: user.userId }
      }

      if (entityType) {
        filter.entityType = { eq: entityType }
      }

      const result = await client.graphql({
        query: listFavorites,
        variables: {
          filter: filter,
          limit: 100,
          sortDirection: 'DESC'
        },
        authMode: 'userPool'
      })

      return {
        success: true,
        favorites: result.data.listFavorites.items,
        message: 'Favorites retrieved successfully'
      }

    } catch (error) {
      console.error('Get favorites error:', error)
      throw new Error(`Failed to get favorites: ${error.message}`)
    }
  }

  /**
   * Check if item is favorited
   */
  static async isFavorited(entityId: string, entityType: 'VENDOR' | 'VENUE' | 'SHOP_ITEM'): Promise<FavoriteItem | null> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        return null
      }

      const result = await client.graphql({
        query: listFavorites,
        variables: {
          filter: {
            and: [
              { userId: { eq: user.userId } },
              { entityId: { eq: entityId } },
              { entityType: { eq: entityType } }
            ]
          },
          limit: 1
        },
        authMode: 'userPool'
      })

      return result.data.listFavorites.items.length > 0 ? result.data.listFavorites.items[0] : null

    } catch (error) {
      console.error('Check favorite error:', error)
      return null
    }
  }

  /**
   * Update favorite notes
   */
  static async updateFavoriteNotes(favoriteId: string, notes: string): Promise<{ success: boolean; favorite?: FavoriteItem; message: string }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      const result = await client.graphql({
        query: updateFavorite,
        variables: {
          input: {
            id: favoriteId,
            notes: notes
          }
        },
        authMode: 'userPool'
      })

      return {
        success: true,
        favorite: result.data.updateFavorite,
        message: 'Favorite updated successfully'
      }

    } catch (error) {
      console.error('Update favorite error:', error)
      throw new Error(`Failed to update favorite: ${error.message}`)
    }
  }

  /**
   * Get favorites count by type
   */
  static async getFavoritesCount(): Promise<{ vendors: number; venues: number; shopItems: number; total: number }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        return { vendors: 0, venues: 0, shopItems: 0, total: 0 }
      }

      const result = await client.graphql({
        query: listFavorites,
        variables: {
          filter: { userId: { eq: user.userId } },
          limit: 1000
        },
        authMode: 'userPool'
      })

      const favorites = result.data.listFavorites.items
      const vendors = favorites.filter(f => f.entityType === 'VENDOR').length
      const venues = favorites.filter(f => f.entityType === 'VENUE').length
      const shopItems = favorites.filter(f => f.entityType === 'SHOP_ITEM').length

      return {
        vendors,
        venues,
        shopItems,
        total: favorites.length
      }

    } catch (error) {
      console.error('Get favorites count error:', error)
      return { vendors: 0, venues: 0, shopItems: 0, total: 0 }
    }
  }
}

export default FavoritesService
