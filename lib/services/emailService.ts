/**
 * Email Service for sending welcome emails and notifications
 * This service handles newsletter welcome emails and user login welcome emails
 */

export interface WelcomeEmailData {
  email: string;
  firstName?: string;
  lastName?: string;
  type: 'newsletter' | 'login' | 'signup';
  preferences?: {
    weddingTips?: boolean;
    vendorRecommendations?: boolean;
    specialOffers?: boolean;
    eventUpdates?: boolean;
    blogUpdates?: boolean;
    frequency?: string;
  };
  interests?: string[];
  isVendor?: boolean;
  businessName?: string;
}

export interface EmailTemplate {
  subject: string;
  htmlContent: string;
  textContent: string;
}

class EmailService {
  private apiEndpoint = '/api/send-email'; // We'll create this API endpoint

  /**
   * Send welcome email for newsletter subscription
   */
  async sendNewsletterWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
    try {
      const template = this.generateNewsletterWelcomeTemplate(data);
      
      const emailPayload = {
        to: data.email,
        subject: template.subject,
        html: template.htmlContent,
        text: template.textContent,
        type: 'newsletter_welcome'
      };

      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailPayload)
      });

      if (!response.ok) {
        throw new Error(`Email service responded with status: ${response.status}`);
      }

      console.log('Newsletter welcome email sent successfully to:', data.email);
      return true;
    } catch (error) {
      console.error('Error sending newsletter welcome email:', error);
      return false;
    }
  }

  /**
   * Send welcome email for user login/signup
   */
  async sendUserWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
    try {
      const template = this.generateUserWelcomeTemplate(data);
      
      const emailPayload = {
        to: data.email,
        subject: template.subject,
        html: template.htmlContent,
        text: template.textContent,
        type: data.type === 'signup' ? 'user_signup_welcome' : 'user_login_welcome'
      };

      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailPayload)
      });

      if (!response.ok) {
        throw new Error(`Email service responded with status: ${response.status}`);
      }

      console.log('User welcome email sent successfully to:', data.email);
      return true;
    } catch (error) {
      console.error('Error sending user welcome email:', error);
      return false;
    }
  }

  /**
   * Generate newsletter welcome email template
   */
  private generateNewsletterWelcomeTemplate(data: WelcomeEmailData): EmailTemplate {
    const name = data.firstName ? `${data.firstName}${data.lastName ? ' ' + data.lastName : ''}` : 'there';
    const interestsList = data.interests?.map(interest => 
      interest.toLowerCase().replace('_', ' ')
    ).join(', ') || 'wedding planning';

    const subject = `Welcome to Thirumanam360 Newsletter! 💕`;

    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Thirumanam360 Newsletter</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ec4899, #8b5cf6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: white; padding: 30px; border: 1px solid #e5e7eb; }
        .footer { background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; font-size: 14px; color: #6b7280; }
        .button { display: inline-block; background: #ec4899; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        .highlight { background: #fef3f2; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #ec4899; }
        .interests { background: #f0f9ff; padding: 15px; border-radius: 6px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 Welcome to Thirumanam360!</h1>
            <p>Thank you for subscribing to our newsletter</p>
        </div>
        
        <div class="content">
            <h2>Hello ${name}! 👋</h2>
            
            <p>Welcome to the Thirumanam360 family! We're thrilled to have you join our community of couples planning their perfect wedding.</p>
            
            <div class="highlight">
                <h3>🎊 What to Expect:</h3>
                <ul>
                    <li><strong>Wedding Tips & Advice:</strong> Expert guidance for planning your dream wedding</li>
                    <li><strong>Vendor Recommendations:</strong> Discover top-rated wedding vendors in your city</li>
                    <li><strong>Exclusive Offers:</strong> Special discounts and deals just for our subscribers</li>
                    <li><strong>Latest Trends:</strong> Stay updated with the newest wedding trends and ideas</li>
                </ul>
            </div>

            ${data.interests && data.interests.length > 0 ? `
            <div class="interests">
                <h3>📝 Your Interests:</h3>
                <p>Based on your preferences, we'll send you personalized content about: <strong>${interestsList}</strong></p>
            </div>
            ` : ''}

            <p>You'll receive our newsletter <strong>${data.preferences?.frequency?.toLowerCase() || 'weekly'}</strong> with the latest wedding inspiration, vendor spotlights, and exclusive offers.</p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="https://thirumanam360.com" class="button">Explore Thirumanam360</a>
            </div>
            
            <p>If you have any questions or need help planning your wedding, feel free to reach out to us. We're here to make your wedding journey magical!</p>
            
            <p>Happy Wedding Planning! 💕</p>
            <p><strong>The Thirumanam360 Team</strong></p>
        </div>
        
        <div class="footer">
            <p>You're receiving this email because you subscribed to our newsletter.</p>
            <p><a href="https://thirumanam360.com/newsletter/preferences">Manage Preferences</a> | <a href="https://thirumanam360.com/newsletter/unsubscribe">Unsubscribe</a></p>
            <p>© 2025 Thirumanam360. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;

    const textContent = `
Welcome to Thirumanam360 Newsletter!

Hello ${name}!

Thank you for subscribing to our newsletter. We're excited to have you join our community of couples planning their perfect wedding.

What to Expect:
- Wedding Tips & Advice: Expert guidance for planning your dream wedding
- Vendor Recommendations: Discover top-rated wedding vendors in your city  
- Exclusive Offers: Special discounts and deals just for our subscribers
- Latest Trends: Stay updated with the newest wedding trends and ideas

${data.interests && data.interests.length > 0 ? `Your Interests: ${interestsList}` : ''}

You'll receive our newsletter ${data.preferences?.frequency?.toLowerCase() || 'weekly'} with the latest wedding inspiration, vendor spotlights, and exclusive offers.

Visit us at: https://thirumanam360.com

Happy Wedding Planning!
The Thirumanam360 Team

---
Manage Preferences: https://thirumanam360.com/newsletter/preferences
Unsubscribe: https://thirumanam360.com/newsletter/unsubscribe
`;

    return { subject, htmlContent, textContent };
  }

  /**
   * Generate user welcome email template
   */
  private generateUserWelcomeTemplate(data: WelcomeEmailData): EmailTemplate {
    const name = data.firstName ? `${data.firstName}${data.lastName ? ' ' + data.lastName : ''}` : 'there';
    const isSignup = data.type === 'signup';
    const userType = data.isVendor ? 'vendor' : 'couple';
    
    const subject = isSignup 
      ? `Welcome to Thirumanam360! Your wedding journey starts here 🎊`
      : `Welcome back to Thirumanam360! 👋`;

    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${isSignup ? 'Welcome to' : 'Welcome back to'} Thirumanam360</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #ec4899, #8b5cf6); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: white; padding: 30px; border: 1px solid #e5e7eb; }
        .footer { background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; font-size: 14px; color: #6b7280; }
        .button { display: inline-block; background: #ec4899; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 10px 5px; }
        .feature-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0; }
        .feature { background: #f8fafc; padding: 15px; border-radius: 6px; text-align: center; }
        .highlight { background: #fef3f2; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #ec4899; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${isSignup ? '🎉 Welcome to Thirumanam360!' : '👋 Welcome Back!'}</h1>
            <p>${isSignup ? "Let's make your wedding dreams come true" : "We're glad to see you again"}</p>
        </div>
        
        <div class="content">
            <h2>Hello ${name}! ${isSignup ? '🎊' : '😊'}</h2>
            
            ${isSignup ? `
            <p>Congratulations on taking the first step towards planning your perfect wedding! ${data.isVendor ? 'As a wedding vendor,' : 'As a couple,'} you now have access to India's most comprehensive wedding planning platform.</p>
            ` : `
            <p>Welcome back to Thirumanam360! We're excited to continue helping you ${data.isVendor ? 'grow your wedding business' : 'plan your perfect wedding'}.</p>
            `}

            ${data.isVendor ? `
            <div class="highlight">
                <h3>🏢 For Wedding Vendors:</h3>
                <ul>
                    <li><strong>Showcase Your Services:</strong> Create stunning vendor profiles</li>
                    <li><strong>Connect with Couples:</strong> Get inquiries from engaged couples</li>
                    <li><strong>Manage Bookings:</strong> Streamline your business operations</li>
                    <li><strong>Build Reviews:</strong> Grow your reputation with client testimonials</li>
                </ul>
            </div>
            ` : `
            <div class="highlight">
                <h3>💕 For Couples:</h3>
                <ul>
                    <li><strong>Find Vendors:</strong> Discover top-rated wedding professionals</li>
                    <li><strong>Plan Your Wedding:</strong> Use our comprehensive planning tools</li>
                    <li><strong>Get Inspiration:</strong> Browse thousands of wedding ideas</li>
                    <li><strong>Manage Budget:</strong> Keep track of your wedding expenses</li>
                </ul>
            </div>
            `}

            <div class="feature-grid">
                <div class="feature">
                    <h4>🔍 Search & Discover</h4>
                    <p>Find the perfect vendors for your special day</p>
                </div>
                <div class="feature">
                    <h4>📱 Mobile Friendly</h4>
                    <p>Plan on-the-go with our responsive platform</p>
                </div>
                <div class="feature">
                    <h4>💬 Direct Communication</h4>
                    <p>Connect directly with vendors and couples</p>
                </div>
                <div class="feature">
                    <h4>⭐ Reviews & Ratings</h4>
                    <p>Make informed decisions with real reviews</p>
                </div>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
                ${data.isVendor ? `
                <a href="https://thirumanam360.com/dashboard" class="button">Go to Dashboard</a>
                <a href="https://thirumanam360.com/vendors" class="button">Browse Vendors</a>
                ` : `
                <a href="https://thirumanam360.com/vendors" class="button">Find Vendors</a>
                <a href="https://thirumanam360.com/venues" class="button">Explore Venues</a>
                `}
            </div>
            
            ${isSignup ? `
            <p><strong>Next Steps:</strong></p>
            <ol>
                <li>Complete your profile to get personalized recommendations</li>
                <li>${data.isVendor ? 'Add your services and portfolio' : 'Start exploring vendors in your city'}</li>
                <li>Connect with our community and start planning!</li>
            </ol>
            ` : ''}
            
            <p>If you have any questions or need assistance, our support team is here to help. Feel free to reach out anytime!</p>
            
            <p>Happy ${data.isVendor ? 'Business Building' : 'Wedding Planning'}! 💕</p>
            <p><strong>The Thirumanam360 Team</strong></p>
        </div>
        
        <div class="footer">
            <p>Need help? Contact <NAME_EMAIL></p>
            <p>Follow us: <a href="#">Facebook</a> | <a href="#">Instagram</a> | <a href="#">Twitter</a></p>
            <p>© 2025 Thirumanam360. All rights reserved.</p>
        </div>
    </div>
</body>
</html>`;

    const textContent = `
${isSignup ? 'Welcome to Thirumanam360!' : 'Welcome back to Thirumanam360!'}

Hello ${name}!

${isSignup ? `
Congratulations on joining Thirumanam360! ${data.isVendor ? 'As a wedding vendor,' : 'As a couple,'} you now have access to India's most comprehensive wedding planning platform.
` : `
Welcome back! We're excited to continue helping you ${data.isVendor ? 'grow your wedding business' : 'plan your perfect wedding'}.
`}

${data.isVendor ? `
For Wedding Vendors:
- Showcase Your Services: Create stunning vendor profiles
- Connect with Couples: Get inquiries from engaged couples  
- Manage Bookings: Streamline your business operations
- Build Reviews: Grow your reputation with client testimonials
` : `
For Couples:
- Find Vendors: Discover top-rated wedding professionals
- Plan Your Wedding: Use our comprehensive planning tools
- Get Inspiration: Browse thousands of wedding ideas
- Manage Budget: Keep track of your wedding expenses
`}

Key Features:
- Search & Discover: Find the perfect vendors for your special day
- Mobile Friendly: Plan on-the-go with our responsive platform
- Direct Communication: Connect directly with vendors and couples
- Reviews & Ratings: Make informed decisions with real reviews

Visit us at: https://thirumanam360.com

${isSignup ? `
Next Steps:
1. Complete your profile to get personalized recommendations
2. ${data.isVendor ? 'Add your services and portfolio' : 'Start exploring vendors in your city'}
3. Connect with our community and start planning!
` : ''}

Need help? Contact <NAME_EMAIL>

Happy ${data.isVendor ? 'Business Building' : 'Wedding Planning'}!
The Thirumanam360 Team
`;

    return { subject, htmlContent, textContent };
  }
}

export const emailService = new EmailService();
export default emailService;
