/**
 * Mobile Authentication Service with OTP
 * Supports both email and mobile phone authentication
 */

import { Auth } from 'aws-amplify';
import { signUp, confirmSignUp, resendSignUpCode, signIn } from 'aws-amplify/auth';

export interface OTPResponse {
  success: boolean;
  message: string;
  session?: string;
  deliveryMedium?: 'SMS' | 'EMAIL';
}

export interface VerifyOTPResponse {
  success: boolean;
  message: string;
  user?: any;
  isNewUser?: boolean;
}

export interface PhoneAuthResult {
  success: boolean;
  message: string;
  nextStep?: 'CONFIRM_SIGN_UP' | 'DONE';
  user?: any;
}

export class MobileAuthService {
  
  /**
   * Send OTP to mobile number for login/signup
   */
  static async sendMobileOTP(phoneNumber: string, countryCode: string = '+91'): Promise<OTPResponse> {
    try {
      // Format phone number with country code
      const formattedPhone = `${countryCode}${phoneNumber.replace(/^0+/, '')}`;

      // Create a temporary email for phone-based registration
      // This allows us to use phone numbers with the existing email-based User Pool
      const tempEmail = `${phoneNumber}@mobile.thirumanam360.temp`;

      // Try to sign up the user with temporary email and phone number
      try {
        const signUpResult = await signUp({
          username: tempEmail,
          password: this.generateTemporaryPassword(),
          options: {
            userAttributes: {
              email: tempEmail,
              phone_number: formattedPhone,
              'custom:registration_type': 'mobile',
              'custom:primary_contact': formattedPhone
            }
          }
        });

        return {
          success: true,
          message: 'OTP sent to your mobile number',
          session: signUpResult.userId,
          deliveryMedium: 'SMS'
        };
      } catch (signUpError: any) {
        console.error('Sign up error:', signUpError);

        // If user already exists, try to resend confirmation code
        if (signUpError.name === 'UsernameExistsException') {
          try {
            await resendSignUpCode({ username: tempEmail });

            return {
              success: true,
              message: 'OTP sent to your mobile number',
              deliveryMedium: 'SMS'
            };
          } catch (resendError: any) {
            console.error('Resend error:', resendError);
            return {
              success: false,
              message: `Failed to send OTP: ${resendError.message}`
            };
          }
        }

        return {
          success: false,
          message: `Failed to send OTP: ${signUpError.message}`
        };
      }

    } catch (error: any) {
      console.error('Send mobile OTP error:', error);
      return {
        success: false,
        message: `Failed to send OTP: ${error.message || 'Unknown error'}`
      };
    }
  }

  /**
   * Verify mobile OTP
   */
  static async verifyMobileOTP(phoneNumber: string, otp: string, countryCode: string = '+91'): Promise<VerifyOTPResponse> {
    try {
      const formattedPhone = `${countryCode}${phoneNumber.replace(/^0+/, '')}`;
      const tempEmail = `${phoneNumber}@mobile.thirumanam360.temp`;

      // Try to confirm sign up (this verifies the OTP)
      try {
        const confirmResult = await confirmSignUp({
          username: tempEmail,
          confirmationCode: otp
        });

        if (confirmResult.isSignUpComplete) {
          // Now sign in the user
          try {
            const signInResult = await signIn({
              username: tempEmail,
              password: this.generateTemporaryPassword()
            });

            return {
              success: true,
              message: 'Account verified and logged in successfully',
              user: signInResult,
              isNewUser: true
            };
          } catch (signInError: any) {
            // If sign in fails, the account is still verified
            return {
              success: true,
              message: 'Account verified successfully. Please try logging in.',
              isNewUser: true
            };
          }
        }
      } catch (confirmError: any) {
        console.error('Confirm sign up error:', confirmError);

        // Handle different error types
        if (confirmError.name === 'CodeMismatchException') {
          return {
            success: false,
            message: 'Invalid OTP. Please check and try again.'
          };
        } else if (confirmError.name === 'ExpiredCodeException') {
          return {
            success: false,
            message: 'OTP has expired. Please request a new one.'
          };
        } else if (confirmError.name === 'NotAuthorizedException') {
          return {
            success: false,
            message: 'Account is already verified. Please try logging in.'
          };
        }

        return {
          success: false,
          message: `Verification failed: ${confirmError.message}`
        };
      }

      return {
        success: false,
        message: 'OTP verification failed. Please try again.'
      };

    } catch (error: any) {
      console.error('Verify mobile OTP error:', error);
      return {
        success: false,
        message: `OTP verification failed: ${error.message || 'Invalid OTP'}`
      };
    }
  }

  /**
   * Send OTP to email for login/signup
   */
  static async sendEmailOTP(email: string): Promise<OTPResponse> {
    try {
      // Try to sign up the user (this will send OTP to email)
      try {
        const signUpResult = await signUp({
          username: email,
          password: this.generateTemporaryPassword(),
          options: {
            userAttributes: {
              email: email,
            }
          }
        });

        return {
          success: true,
          message: 'OTP sent to your email',
          session: signUpResult.userId,
          deliveryMedium: 'EMAIL'
        };
      } catch (signUpError: any) {
        console.error('Email sign up error:', signUpError);

        // If user already exists, try to resend confirmation code
        if (signUpError.name === 'UsernameExistsException') {
          try {
            await resendSignUpCode({ username: email });

            return {
              success: true,
              message: 'OTP sent to your email',
              deliveryMedium: 'EMAIL'
            };
          } catch (resendError: any) {
            console.error('Email resend error:', resendError);
            return {
              success: false,
              message: `Failed to send OTP: ${resendError.message}`
            };
          }
        }

        return {
          success: false,
          message: `Failed to send OTP: ${signUpError.message}`
        };
      }

    } catch (error: any) {
      console.error('Send email OTP error:', error);
      return {
        success: false,
        message: `Failed to send OTP: ${error.message || 'Unknown error'}`
      };
    }
  }

  /**
   * Verify email OTP
   */
  static async verifyEmailOTP(email: string, otp: string): Promise<VerifyOTPResponse> {
    try {
      // Try to confirm sign up (this verifies the OTP)
      const confirmResult = await confirmSignUp({
        username: email,
        confirmationCode: otp
      });

      if (confirmResult.isSignUpComplete) {
        // Now sign in the user
        try {
          const signInResult = await signIn({
            username: email,
            password: this.generateTemporaryPassword()
          });

          return {
            success: true,
            message: 'Email verified and logged in successfully',
            user: signInResult,
            isNewUser: true
          };
        } catch (signInError: any) {
          // If sign in fails, the account is still verified
          return {
            success: true,
            message: 'Email verified successfully. Please try logging in.',
            isNewUser: true
          };
        }
      }

      return {
        success: false,
        message: 'Invalid OTP. Please try again.'
      };

    } catch (error: any) {
      console.error('Verify email OTP error:', error);

      // Handle different error types
      if (error.name === 'CodeMismatchException') {
        return {
          success: false,
          message: 'Invalid OTP. Please check and try again.'
        };
      } else if (error.name === 'ExpiredCodeException') {
        return {
          success: false,
          message: 'OTP has expired. Please request a new one.'
        };
      }

      return {
        success: false,
        message: `Email verification failed: ${error.message || 'Invalid OTP'}`
      };
    }
  }

  /**
   * Check if phone number is valid
   */
  static validatePhoneNumber(phoneNumber: string): boolean {
    // Indian mobile number validation (10 digits)
    const phoneRegex = /^[6-9]\d{9}$/;
    return phoneRegex.test(phoneNumber.replace(/\D/g, ''));
  }

  /**
   * Check if email is valid
   */
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Format phone number for display
   */
  static formatPhoneNumber(phoneNumber: string): string {
    const cleaned = phoneNumber.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `${cleaned.slice(0, 5)} ${cleaned.slice(5)}`;
    }
    return phoneNumber;
  }

  /**
   * Generate temporary password for OTP-based auth
   */
  private static generateTemporaryPassword(): string {
    return Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);
  }

  /**
   * Get supported country codes
   */
  static getSupportedCountryCodes(): Array<{code: string, name: string, flag: string}> {
    return [
      { code: '+91', name: 'India', flag: '🇮🇳' },
      { code: '+1', name: 'United States', flag: '🇺🇸' },
      { code: '+44', name: 'United Kingdom', flag: '🇬🇧' },
      { code: '+971', name: 'UAE', flag: '🇦🇪' },
      { code: '+65', name: 'Singapore', flag: '🇸🇬' },
      { code: '+60', name: 'Malaysia', flag: '🇲🇾' },
    ];
  }

  /**
   * Resend OTP
   */
  static async resendOTP(identifier: string, type: 'mobile' | 'email', countryCode?: string): Promise<OTPResponse> {
    try {
      let username: string;

      if (type === 'mobile') {
        // Use temporary email format for mobile numbers
        username = `${identifier}@mobile.thirumanam360.temp`;
      } else {
        username = identifier;
      }

      await resendSignUpCode({ username });

      return {
        success: true,
        message: `OTP resent to your ${type === 'mobile' ? 'mobile number' : 'email'}`,
        deliveryMedium: type === 'mobile' ? 'SMS' : 'EMAIL'
      };
    } catch (error: any) {
      console.error('Resend OTP error:', error);
      return {
        success: false,
        message: `Failed to resend OTP: ${error.message || 'Unknown error'}`
      };
    }
  }
}

export default MobileAuthService;
