/**
 * Vendor Notification Service
 * Handles sending notifications to vendors when inquiries are submitted
 */

export interface VendorNotificationData {
  vendorUserId: string;
  vendorId: string;
  vendorName: string;
  vendorEmail?: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  eventDate?: string;
  message: string;
  inquiryType: string;
  budget?: string;
  guestCount?: string;
  venue?: string;
  inquiryId: string;
  createdAt: string;
}

export interface VendorEmailTemplate {
  subject: string;
  htmlContent: string;
  textContent: string;
}

class VendorNotificationService {
  private apiEndpoint = '/api/send-email';

  /**
   * Send inquiry notification email to vendor
   */
  async sendInquiryNotification(data: VendorNotificationData): Promise<boolean> {
    try {
      // First, try to get vendor email from their profile
      const vendorEmail = await this.getVendorEmail(data.vendorUserId);
      
      if (!vendorEmail) {
        console.warn(`No email found for vendor ${data.vendorUserId}`);
        return false;
      }

      const template = this.generateInquiryNotificationTemplate(data);
      
      const emailPayload = {
        to: vendorEmail,
        subject: template.subject,
        html: template.htmlContent,
        text: template.textContent,
        type: 'vendor_inquiry_notification'
      };

      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(emailPayload)
      });

      if (!response.ok) {
        throw new Error(`Email service responded with status: ${response.status}`);
      }

      const result = await response.json();
      console.log('Vendor notification email sent successfully:', result);
      return true;

    } catch (error) {
      console.error('Error sending vendor notification email:', error);
      return false;
    }
  }

  /**
   * Get vendor email from their profile or vendor record
   */
  private async getVendorEmail(vendorUserId: string): Promise<string | null> {
    try {
      // Import here to avoid circular dependencies
      const { generateClient } = await import('aws-amplify/api');
      const { listUserProfiles } = await import('@/src/graphql/queries');
      
      const client = generateClient();
      
      // Try to get email from UserProfile first
      const profileResult = await client.graphql({
        query: listUserProfiles,
        variables: {
          filter: { userId: { eq: vendorUserId } }
        }
      });

      if (profileResult.data.listUserProfiles.items.length > 0) {
        const profile = profileResult.data.listUserProfiles.items[0];
        if (profile.email) {
          return profile.email;
        }
      }

      // Fallback: try to get email from Vendor record
      const { listVendors } = await import('@/src/graphql/queries');
      const vendorResult = await client.graphql({
        query: listVendors,
        variables: {
          filter: { userId: { eq: vendorUserId } }
        }
      });

      if (vendorResult.data.listVendors.items.length > 0) {
        const vendor = vendorResult.data.listVendors.items[0];
        if (vendor.email) {
          return vendor.email;
        }
      }

      return null;
    } catch (error) {
      console.error('Error fetching vendor email:', error);
      return null;
    }
  }

  /**
   * Generate email template for inquiry notification
   */
  private generateInquiryNotificationTemplate(data: VendorNotificationData): VendorEmailTemplate {
    const dashboardUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'https://thirumanam360.com'}/dashboard/inquiries`;
    const inquiryTypeDisplay = this.formatInquiryType(data.inquiryType);
    
    const subject = `New ${inquiryTypeDisplay} from ${data.customerName} - Thirumanam360`;

    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Inquiry Notification</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
        .inquiry-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea; }
        .detail-row { margin: 10px 0; }
        .label { font-weight: bold; color: #555; }
        .value { color: #333; }
        .cta-button { display: inline-block; background: #667eea; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; font-weight: bold; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .urgent { background: #fff3cd; border-left-color: #ffc107; }
        .message-box { background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎉 New Inquiry Received!</h1>
            <p>You have a new ${inquiryTypeDisplay.toLowerCase()} from a potential client</p>
        </div>
        
        <div class="content">
            <div class="inquiry-details">
                <h3>📋 Inquiry Details</h3>
                
                <div class="detail-row">
                    <span class="label">Customer Name:</span>
                    <span class="value">${data.customerName}</span>
                </div>
                
                <div class="detail-row">
                    <span class="label">Email:</span>
                    <span class="value">${data.customerEmail}</span>
                </div>
                
                ${data.customerPhone ? `
                <div class="detail-row">
                    <span class="label">Phone:</span>
                    <span class="value">${data.customerPhone}</span>
                </div>
                ` : ''}
                
                <div class="detail-row">
                    <span class="label">Inquiry Type:</span>
                    <span class="value">${inquiryTypeDisplay}</span>
                </div>
                
                ${data.eventDate ? `
                <div class="detail-row">
                    <span class="label">Event Date:</span>
                    <span class="value">${new Date(data.eventDate).toLocaleDateString()}</span>
                </div>
                ` : ''}
                
                ${data.guestCount ? `
                <div class="detail-row">
                    <span class="label">Guest Count:</span>
                    <span class="value">${data.guestCount}</span>
                </div>
                ` : ''}
                
                ${data.budget ? `
                <div class="detail-row">
                    <span class="label">Budget:</span>
                    <span class="value">${data.budget}</span>
                </div>
                ` : ''}
                
                ${data.venue ? `
                <div class="detail-row">
                    <span class="label">Venue:</span>
                    <span class="value">${data.venue}</span>
                </div>
                ` : ''}
                
                <div class="message-box">
                    <strong>Message:</strong><br>
                    ${data.message.replace(/\n/g, '<br>')}
                </div>
            </div>
            
            <div style="text-align: center;">
                <a href="${dashboardUrl}" class="cta-button">
                    View & Respond in Dashboard →
                </a>
            </div>
            
            <div style="background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h4 style="margin: 0 0 10px 0; color: #2d5a2d;">💡 Quick Response Tips:</h4>
                <ul style="margin: 0; padding-left: 20px;">
                    <li>Respond within 2-4 hours for best results</li>
                    <li>Ask clarifying questions about their requirements</li>
                    <li>Share your portfolio and pricing information</li>
                    <li>Suggest a call or meeting to discuss details</li>
                </ul>
            </div>
        </div>
        
        <div class="footer">
            <p>This inquiry was submitted on ${new Date(data.createdAt).toLocaleString()}</p>
            <p>Thirumanam360 - India's Favourite Wedding Planning Platform</p>
            <p><a href="${dashboardUrl}">Manage all your inquiries</a> | <a href="mailto:<EMAIL>">Support</a></p>
        </div>
    </div>
</body>
</html>
    `;

    const textContent = `
New ${inquiryTypeDisplay} from ${data.customerName}

Customer Details:
- Name: ${data.customerName}
- Email: ${data.customerEmail}
${data.customerPhone ? `- Phone: ${data.customerPhone}` : ''}

Inquiry Details:
- Type: ${inquiryTypeDisplay}
${data.eventDate ? `- Event Date: ${new Date(data.eventDate).toLocaleDateString()}` : ''}
${data.guestCount ? `- Guest Count: ${data.guestCount}` : ''}
${data.budget ? `- Budget: ${data.budget}` : ''}
${data.venue ? `- Venue: ${data.venue}` : ''}

Message:
${data.message}

View and respond to this inquiry in your dashboard: ${dashboardUrl}

Submitted on: ${new Date(data.createdAt).toLocaleString()}

Best regards,
Thirumanam360 Team
    `;

    return {
      subject,
      htmlContent,
      textContent
    };
  }

  /**
   * Format inquiry type for display
   */
  private formatInquiryType(type: string): string {
    const typeMap: { [key: string]: string } = {
      'VENDOR_INQUIRY': 'Vendor Inquiry',
      'VENUE_INQUIRY': 'Venue Inquiry',
      'SERVICE_QUOTE': 'Service Quote Request',
      'AVAILABILITY_CHECK': 'Availability Check',
      'GENERAL_QUESTION': 'General Question',
      'BOOKING_REQUEST': 'Booking Request'
    };
    
    return typeMap[type] || type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  }
}

export const vendorNotificationService = new VendorNotificationService();
export default vendorNotificationService;
