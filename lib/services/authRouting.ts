/**
 * Authentication Routing Service
 * Handles smart routing based on user type (customer, vendor, admin, super_admin)
 */

export type UserRole = 'customer' | 'vendor' | 'admin' | 'super_admin';

export interface UserTypeInfo {
  userType: UserRole | null;
  isVendor: boolean;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  hasProfile: boolean;
  businessInfo?: any;
}

export class AuthRoutingService {
  /**
   * Get the appropriate dashboard route based on user type
   */
  static getDashboardRoute(userType: UserRole | null, userProfile?: any): string {
    switch (userType) {
      case 'super_admin':
        return '/dashboard/super-admin';
      case 'admin':
        return '/dashboard';
      case 'vendor':
      case 'customer':
        return '/dashboard';
      default:
        // If user type is not determined, check profile for role hierarchy
        if (userProfile?.isSuperAdmin || userProfile?.role === 'super_admin') {
          return '/dashboard/super-admin';
        } else if (userProfile?.isAdmin || userProfile?.role === 'admin') {
          return '/dashboard';
        }
        return '/dashboard'; // Default to main dashboard for both vendors and customers
    }
  }

  /**
   * Get the appropriate login route based on intended user type
   */
  static getLoginRoute(intendedUserType?: UserRole): string {
    switch (intendedUserType) {
      case 'vendor':
        return '/vendor-login';
      case 'admin':
      case 'super_admin':
        return '/admin-login';
      default:
        return '/login';
    }
  }

  /**
   * Determine user type from profile data
   */
  static determineUserType(userProfile: any): UserRole {
    if (!userProfile) return 'customer';

    // Check role hierarchy (highest priority first)
    if (userProfile.isSuperAdmin || userProfile.role === 'super_admin') {
      return 'super_admin';
    }

    if (userProfile.isAdmin || userProfile.role === 'admin') {
      return 'admin';
    }

    // Check multiple indicators for vendor status
    const isVendor = userProfile.isVendor ||
                    (userProfile.businessInfo && userProfile.businessInfo.businessName) ||
                    false;

    return isVendor ? 'vendor' : 'customer';
  }

  /**
   * Check if user has access to vendor features
   */
  static hasVendorAccess(userProfile: any): boolean {
    const userType = this.determineUserType(userProfile);
    return ['vendor', 'admin', 'super_admin'].includes(userType);
  }

  /**
   * Check if user has access to customer features
   */
  static hasCustomerAccess(userProfile: any): boolean {
    // All users have customer access
    return true;
  }

  /**
   * Check if user has admin access
   */
  static hasAdminAccess(userProfile: any): boolean {
    const userType = this.determineUserType(userProfile);
    return ['admin', 'super_admin'].includes(userType);
  }

  /**
   * Check if user has super admin access
   */
  static hasSuperAdminAccess(userProfile: any): boolean {
    return this.determineUserType(userProfile) === 'super_admin';
  }

  /**
   * Get user type display name
   */
  static getUserTypeDisplayName(userType: UserRole | null): string {
    switch (userType) {
      case 'super_admin':
        return 'Super Administrator';
      case 'admin':
        return 'Administrator';
      case 'vendor':
        return 'Business Account';
      case 'customer':
        return 'Customer Account';
      default:
        return 'Account';
    }
  }

  /**
   * Get appropriate navigation items based on user type
   */
  static getNavigationItems(userType: UserRole | null) {
    const commonItems = [
      { label: 'Dashboard', href: '/dashboard', icon: 'Home' },
      { label: 'Profile', href: '/dashboard/profile', icon: 'User' },
      { label: 'Bookings', href: '/dashboard/bookings', icon: 'Calendar' },
      { label: 'Reviews', href: '/dashboard/reviews', icon: 'Star' },
    ];

    const adminItems = [
      { label: 'User Management', href: '/dashboard/admin-users', icon: 'Users' },
      { label: 'Vendor Management', href: '/dashboard/admin-vendors', icon: 'Briefcase' },
      { label: 'Venue Management', href: '/dashboard/admin-venues', icon: 'MapPin' },
      { label: 'Shop Management', href: '/dashboard/admin-shops', icon: 'ShoppingBag' },
      { label: 'Review Management', href: '/dashboard/admin-reviews', icon: 'Star' },
      { label: 'Admin Tools', href: '/dashboard/admin-tools', icon: 'Settings' },
    ];

    const superAdminItems = [
      { label: 'Super Admin Dashboard', href: '/dashboard/super-admin', icon: 'Crown' },
      { label: 'System Management', href: '/dashboard/super-admin/system', icon: 'Server' },
      { label: 'Admin Management', href: '/dashboard/super-admin/admins', icon: 'Shield' },
      { label: 'Platform Analytics', href: '/dashboard/super-admin/analytics', icon: 'TrendingUp' },
      { label: 'System Logs', href: '/dashboard/super-admin/logs', icon: 'FileText' },
      ...adminItems,
    ];

    switch (userType) {
      case 'super_admin':
        return [
          ...superAdminItems,
          ...commonItems,
        ];

      case 'admin':
        return [
          { label: 'Dashboard', href: '/dashboard', icon: 'Home' },
          ...adminItems,
          ...commonItems,
        ];

      case 'vendor':
        return [
          { label: 'Vendor Dashboard', href: '/dashboard/vendor', icon: 'Building2' },
          { label: 'My Vendors', href: '/dashboard/vendors', icon: 'Store' },
          { label: 'My Venues', href: '/dashboard/venues', icon: 'MapPin' },
          { label: 'My Shop', href: '/dashboard/shop', icon: 'ShoppingBag' },
          { label: 'Inquiries', href: '/dashboard/inquiries', icon: 'MessageCircle' },
          ...commonItems,
        ];

      default:
        return [
          ...commonItems,
          { label: 'Gallery', href: '/dashboard/gallery', icon: 'Image' },
          { label: 'Settings', href: '/dashboard/settings', icon: 'Settings' },
        ];
    }
  }

  /**
   * Check if current route is appropriate for user type
   */
  static isRouteAppropriate(currentPath: string, userType: UserRole | null): boolean {
    // Special case: admin-tools is accessible to all authenticated users
    if (currentPath === '/dashboard/admin-tools') {
      return true;
    }

    // Super admin routes (highest privilege)
    const superAdminRoutes = [
      '/dashboard/super-admin'
    ];

    // Admin routes (excluding admin-tools which is accessible to vendors)
    const adminRoutes = [
      '/dashboard/admin-users',
      '/dashboard/admin-vendors',
      '/dashboard/admin-venues',
      '/dashboard/admin-shops',
      '/dashboard/admin-reviews',
      '/dashboard/admin-reports',
      '/dashboard/admin-content',
      '/dashboard/admin-disputes',
      '/admin-login'
    ];

    // Vendor-specific routes
    const vendorRoutes = [
      '/dashboard/vendors',
      '/dashboard/venues',
      '/dashboard/shop',
      '/vendor-login'
    ];

    // Customer-specific routes
    const customerRoutes = [
      '/login'
    ];

    switch (userType) {
      case 'super_admin':
        // Super admins can access everything
        return true;

      case 'admin':
        // Admins can access admin, vendor, and customer routes but not super admin
        return !superAdminRoutes.some(route => currentPath.startsWith(route));

      case 'vendor':
        // Vendors can access vendor and customer routes but not admin routes
        return !adminRoutes.some(route => currentPath.startsWith(route)) &&
               !superAdminRoutes.some(route => currentPath.startsWith(route));

      case 'customer':
        // Customers can only access customer routes
        return !vendorRoutes.some(route => currentPath.startsWith(route)) &&
               !adminRoutes.some(route => currentPath.startsWith(route)) &&
               !superAdminRoutes.some(route => currentPath.startsWith(route));

      default:
        // Allow access if user type is not determined
        return true;
    }
  }

  /**
   * Get redirect URL if current route is not appropriate
   */
  static getRedirectUrl(currentPath: string, userType: UserRole | null): string | null {
    if (!this.isRouteAppropriate(currentPath, userType)) {
      return this.getDashboardRoute(userType);
    }
    return null;
  }

  /**
   * Get welcome message based on user type
   */
  static getWelcomeMessage(userType: UserRole | null, userProfile?: any): string {
    const name = userProfile?.firstName || 'User';

    switch (userType) {
      case 'super_admin':
        return `Welcome back, ${name}! Super Administrator Dashboard`;
      case 'admin':
        return `Welcome back, ${name}! Administrator Dashboard`;
      case 'vendor':
        const businessName = userProfile?.businessInfo?.businessName;
        return businessName
          ? `Welcome back, ${name}! Managing ${businessName}`
          : `Welcome to your business dashboard, ${name}!`;
      case 'customer':
        return `Welcome back, ${name}! Ready to plan your dream wedding?`;
      default:
        return `Welcome, ${name}!`;
    }
  }

  /**
   * Get dashboard stats configuration based on user type
   */
  static getDashboardStats(userType: UserRole | null) {
    switch (userType) {
      case 'super_admin':
        return [
          { label: 'Total Users', key: 'totalUsers', icon: 'Users' },
          { label: 'Active Vendors', key: 'activeVendors', icon: 'Store' },
          { label: 'System Health', key: 'systemHealth', icon: 'Activity' },
          { label: 'Revenue', key: 'revenue', icon: 'DollarSign' },
        ];

      case 'admin':
        return [
          { label: 'Pending Reviews', key: 'pendingReviews', icon: 'Clock' },
          { label: 'User Reports', key: 'userReports', icon: 'AlertTriangle' },
          { label: 'Content Moderation', key: 'contentModeration', icon: 'Shield' },
          { label: 'Platform Activity', key: 'platformActivity', icon: 'Activity' },
        ];

      case 'vendor':
        return [
          { label: 'Total Inquiries', key: 'inquiries', icon: 'MessageCircle' },
          { label: 'Active Listings', key: 'listings', icon: 'Store' },
          { label: 'Reviews Received', key: 'reviews', icon: 'Star' },
          { label: 'Profile Views', key: 'views', icon: 'Eye' },
        ];

      default:
        return [
          { label: 'Saved Vendors', key: 'savedVendors', icon: 'Heart' },
          { label: 'Bookings', key: 'bookings', icon: 'Calendar' },
          { label: 'Reviews Written', key: 'reviews', icon: 'Star' },
          { label: 'Gallery Photos', key: 'photos', icon: 'Image' },
        ];
    }
  }
}

export default AuthRoutingService;
