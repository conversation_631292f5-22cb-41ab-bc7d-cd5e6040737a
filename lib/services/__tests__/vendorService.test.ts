import { vendorService, VendorData } from '../vendorService';

// Mock AWS Amplify
jest.mock('aws-amplify/api', () => ({
  generateClient: jest.fn(() => ({
    graphql: jest.fn()
  }))
}));

jest.mock('aws-amplify/auth', () => ({
  getCurrentUser: jest.fn(() => Promise.resolve({ userId: 'test-user-id' }))
}));

describe('VendorService', () => {
  const mockVendorData: VendorData = {
    name: "Test Photography Studio",
    category: "Wedding Photography",
    description: "Test description",
    contact: "+91 98765 43210",
    email: "<EMAIL>",
    address: "Test Address",
    city: "Chennai",
    state: "Tamil Nadu",
    pincode: "600001",
    website: "www.test.com",
    socialMedia: {
      facebook: "test",
      instagram: "test",
      youtube: "test"
    },
    profilePhoto: "",
    gallery: [],
    services: [
      {
        name: "Wedding Photography",
        price: "₹50,000",
        description: "Full day coverage"
      }
    ],
    experience: "5+ years",
    events: "100+",
    responseTime: "2 hours",
    rating: 4.5,
    reviewCount: 50,
    verified: true,
    featured: false,
    availability: "Available",
    priceRange: "₹25,000 - ₹75,000",
    specializations: ["Candid Photography"],
    awards: ["Best Photographer 2023"],
    languages: ["Tamil", "English"],
    coverage: ["Chennai", "Bangalore"],
    equipment: ["DSLR", "Drone"],
    status: "active"
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createVendor', () => {
    it('should create a vendor successfully', async () => {
      const mockResponse = {
        data: {
          createVendor: {
            id: 'test-vendor-id',
            userId: 'test-user-id',
            ...mockVendorData,
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z'
          }
        }
      };

      const { generateClient } = require('aws-amplify/api');
      const mockClient = generateClient();
      mockClient.graphql.mockResolvedValue(mockResponse);

      const result = await vendorService.createVendor(mockVendorData);

      expect(result).toEqual(mockResponse.data.createVendor);
      expect(mockClient.graphql).toHaveBeenCalledWith({
        query: expect.any(String),
        variables: {
          input: {
            userId: 'test-user-id',
            ...mockVendorData
          }
        }
      });
    });

    it('should handle creation errors', async () => {
      const { generateClient } = require('aws-amplify/api');
      const mockClient = generateClient();
      mockClient.graphql.mockRejectedValue(new Error('Network error'));

      await expect(vendorService.createVendor(mockVendorData))
        .rejects.toThrow('Failed to create vendor');
    });
  });

  describe('getUserVendors', () => {
    it('should fetch user vendors successfully', async () => {
      const mockResponse = {
        data: {
          listVendors: {
            items: [
              {
                id: 'vendor-1',
                userId: 'test-user-id',
                name: 'Vendor 1',
                category: 'Photography'
              },
              {
                id: 'vendor-2',
                userId: 'test-user-id',
                name: 'Vendor 2',
                category: 'Catering'
              }
            ]
          }
        }
      };

      const { generateClient } = require('aws-amplify/api');
      const mockClient = generateClient();
      mockClient.graphql.mockResolvedValue(mockResponse);

      const result = await vendorService.getUserVendors();

      expect(result).toEqual(mockResponse.data.listVendors.items);
      expect(mockClient.graphql).toHaveBeenCalledWith({
        query: expect.any(String),
        variables: {
          filter: {
            userId: { eq: 'test-user-id' }
          }
        }
      });
    });
  });

  describe('updateVendor', () => {
    it('should update vendor successfully', async () => {
      const vendorId = 'test-vendor-id';
      const updateData = { name: 'Updated Name' };

      // Mock getVendor response
      const mockGetResponse = {
        data: {
          getVendor: {
            id: vendorId,
            userId: 'test-user-id',
            name: 'Original Name'
          }
        }
      };

      // Mock updateVendor response
      const mockUpdateResponse = {
        data: {
          updateVendor: {
            id: vendorId,
            userId: 'test-user-id',
            name: 'Updated Name'
          }
        }
      };

      const { generateClient } = require('aws-amplify/api');
      const mockClient = generateClient();
      mockClient.graphql
        .mockResolvedValueOnce(mockGetResponse)
        .mockResolvedValueOnce(mockUpdateResponse);

      const result = await vendorService.updateVendor(vendorId, updateData);

      expect(result).toEqual(mockUpdateResponse.data.updateVendor);
    });

    it('should prevent unauthorized updates', async () => {
      const vendorId = 'test-vendor-id';
      const updateData = { name: 'Updated Name' };

      // Mock getVendor response with different userId
      const mockGetResponse = {
        data: {
          getVendor: {
            id: vendorId,
            userId: 'different-user-id',
            name: 'Original Name'
          }
        }
      };

      const { generateClient } = require('aws-amplify/api');
      const mockClient = generateClient();
      mockClient.graphql.mockResolvedValue(mockGetResponse);

      await expect(vendorService.updateVendor(vendorId, updateData))
        .rejects.toThrow('Unauthorized: You can only update your own vendors');
    });
  });

  describe('deleteVendor', () => {
    it('should delete vendor successfully', async () => {
      const vendorId = 'test-vendor-id';

      // Mock getVendor response
      const mockGetResponse = {
        data: {
          getVendor: {
            id: vendorId,
            userId: 'test-user-id',
            name: 'Test Vendor'
          }
        }
      };

      // Mock deleteVendor response
      const mockDeleteResponse = {
        data: {
          deleteVendor: {
            id: vendorId
          }
        }
      };

      const { generateClient } = require('aws-amplify/api');
      const mockClient = generateClient();
      mockClient.graphql
        .mockResolvedValueOnce(mockGetResponse)
        .mockResolvedValueOnce(mockDeleteResponse);

      const result = await vendorService.deleteVendor(vendorId);

      expect(result).toBe(true);
    });

    it('should prevent unauthorized deletions', async () => {
      const vendorId = 'test-vendor-id';

      // Mock getVendor response with different userId
      const mockGetResponse = {
        data: {
          getVendor: {
            id: vendorId,
            userId: 'different-user-id',
            name: 'Test Vendor'
          }
        }
      };

      const { generateClient } = require('aws-amplify/api');
      const mockClient = generateClient();
      mockClient.graphql.mockResolvedValue(mockGetResponse);

      await expect(vendorService.deleteVendor(vendorId))
        .rejects.toThrow('Unauthorized: You can only delete your own vendors');
    });
  });

  describe('authentication', () => {
    it('should handle unauthenticated users', async () => {
      const { getCurrentUser } = require('aws-amplify/auth');
      getCurrentUser.mockRejectedValue(new Error('Not authenticated'));

      await expect(vendorService.createVendor(mockVendorData))
        .rejects.toThrow('User not authenticated');
    });
  });
});
