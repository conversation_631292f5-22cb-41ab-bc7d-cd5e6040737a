import { generateClient } from 'aws-amplify/api';
import { 
  createNewsletterSubscription,
  updateNewsletterSubscription,
  deleteNewsletterSubscription
} from '@/src/graphql/mutations';
import {
  getNewsletterSubscription,
  listNewsletterSubscriptions,
  newsletterSubscriptionsByEmail,
  newsletterSubscriptionsByUserId
} from '@/src/graphql/queries';
import { onCreateNewsletterSubscription } from '@/src/graphql/subscriptions';
import { emailService } from './emailService';

// Initialize GraphQL client
const client = generateClient();

export interface NewsletterSubscriptionInput {
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  city?: string;
  state?: string;
  weddingDate?: string;
  interests?: string[];
  source: string;
  userId?: string;
  preferences?: {
    weddingTips?: boolean;
    vendorRecommendations?: boolean;
    specialOffers?: boolean;
    eventUpdates?: boolean;
    blogUpdates?: boolean;
    frequency?: string;
  };
}

export interface NewsletterSubscriptionResponse {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  city?: string;
  state?: string;
  weddingDate?: string;
  interests?: string[];
  source: string;
  status: string;
  preferences?: {
    weddingTips?: boolean;
    vendorRecommendations?: boolean;
    specialOffers?: boolean;
    eventUpdates?: boolean;
    blogUpdates?: boolean;
    frequency?: string;
  };
  userId?: string;
  subscribedAt: string;
  unsubscribedAt?: string;
  lastEmailSent?: string;
  emailsSent?: number;
  emailsOpened?: number;
  emailsClicked?: number;
  createdAt: string;
  updatedAt: string;
}

export interface NewsletterStats {
  totalSubscribers: number;
  activeSubscribers: number;
  unsubscribedCount: number;
  recentSubscriptions: number;
  topSources: Array<{ source: string; count: number }>;
  topInterests: Array<{ interest: string; count: number }>;
}

class NewsletterService {
  /**
   * Subscribe to newsletter
   */
  async subscribe(input: NewsletterSubscriptionInput): Promise<NewsletterSubscriptionResponse> {
    try {
      // Check if email already exists
      const existingSubscription = await this.getByEmail(input.email);
      
      if (existingSubscription && existingSubscription.status === 'ACTIVE') {
        throw new Error('Email is already subscribed to our newsletter');
      }

      // If exists but unsubscribed, reactivate
      if (existingSubscription && existingSubscription.status === 'UNSUBSCRIBED') {
        return await this.reactivateSubscription(existingSubscription.id, input);
      }

      const subscriptionData = {
        email: input.email,
        firstName: input.firstName,
        lastName: input.lastName,
        phone: input.phone,
        city: input.city,
        state: input.state,
        weddingDate: input.weddingDate,
        interests: input.interests,
        source: input.source,
        status: 'ACTIVE',
        preferences: input.preferences,
        userId: input.userId,
        subscribedAt: new Date().toISOString(),
        emailsSent: 0,
        emailsOpened: 0,
        emailsClicked: 0
      };

      const result = await client.graphql({
        query: createNewsletterSubscription,
        variables: { input: subscriptionData }
      });

      if (!result.data?.createNewsletterSubscription) {
        throw new Error('Failed to create newsletter subscription');
      }

      const subscription = result.data.createNewsletterSubscription;

      // Send welcome email
      try {
        await emailService.sendNewsletterWelcomeEmail({
          email: subscription.email,
          firstName: subscription.firstName,
          lastName: subscription.lastName,
          type: 'newsletter',
          preferences: subscription.preferences,
          interests: subscription.interests
        });
      } catch (emailError) {
        console.error('Failed to send welcome email:', emailError);
        // Don't fail the subscription if email fails
      }

      return subscription;
    } catch (error: any) {
      console.error('Error subscribing to newsletter:', error);
      throw new Error(error.message || 'Failed to subscribe to newsletter');
    }
  }

  /**
   * Unsubscribe from newsletter
   */
  async unsubscribe(email: string): Promise<NewsletterSubscriptionResponse> {
    try {
      const subscription = await this.getByEmail(email);
      
      if (!subscription) {
        throw new Error('Email not found in our newsletter list');
      }

      if (subscription.status === 'UNSUBSCRIBED') {
        throw new Error('Email is already unsubscribed');
      }

      const result = await client.graphql({
        query: updateNewsletterSubscription,
        variables: {
          input: {
            id: subscription.id,
            status: 'UNSUBSCRIBED',
            unsubscribedAt: new Date().toISOString()
          }
        }
      });

      if (!result.data?.updateNewsletterSubscription) {
        throw new Error('Failed to unsubscribe from newsletter');
      }

      return result.data.updateNewsletterSubscription;
    } catch (error: any) {
      console.error('Error unsubscribing from newsletter:', error);
      throw new Error(error.message || 'Failed to unsubscribe from newsletter');
    }
  }

  /**
   * Reactivate subscription
   */
  async reactivateSubscription(id: string, input: NewsletterSubscriptionInput): Promise<NewsletterSubscriptionResponse> {
    try {
      const updateData = {
        id,
        status: 'ACTIVE',
        subscribedAt: new Date().toISOString(),
        unsubscribedAt: null,
        firstName: input.firstName,
        lastName: input.lastName,
        phone: input.phone,
        city: input.city,
        state: input.state,
        weddingDate: input.weddingDate,
        interests: input.interests,
        preferences: input.preferences,
        userId: input.userId
      };

      const result = await client.graphql({
        query: updateNewsletterSubscription,
        variables: { input: updateData }
      });

      if (!result.data?.updateNewsletterSubscription) {
        throw new Error('Failed to reactivate newsletter subscription');
      }

      return result.data.updateNewsletterSubscription;
    } catch (error: any) {
      console.error('Error reactivating newsletter subscription:', error);
      throw new Error(error.message || 'Failed to reactivate newsletter subscription');
    }
  }

  /**
   * Get subscription by email
   */
  async getByEmail(email: string): Promise<NewsletterSubscriptionResponse | null> {
    try {
      const result = await client.graphql({
        query: newsletterSubscriptionsByEmail,
        variables: { email, limit: 1 }
      });

      const subscriptions = result.data?.newsletterSubscriptionsByEmail?.items || [];
      return subscriptions.length > 0 ? subscriptions[0] : null;
    } catch (error: any) {
      console.error('Error getting newsletter subscription by email:', error);
      return null;
    }
  }

  /**
   * Get subscription by user ID
   */
  async getByUserId(userId: string): Promise<NewsletterSubscriptionResponse | null> {
    try {
      const result = await client.graphql({
        query: newsletterSubscriptionsByUserId,
        variables: { userId, limit: 1 }
      });

      const subscriptions = result.data?.newsletterSubscriptionsByUserId?.items || [];
      return subscriptions.length > 0 ? subscriptions[0] : null;
    } catch (error: any) {
      console.error('Error getting newsletter subscription by user ID:', error);
      return null;
    }
  }

  /**
   * Update subscription preferences
   */
  async updatePreferences(
    email: string, 
    preferences: NewsletterSubscriptionInput['preferences']
  ): Promise<NewsletterSubscriptionResponse> {
    try {
      const subscription = await this.getByEmail(email);
      
      if (!subscription) {
        throw new Error('Email not found in our newsletter list');
      }

      const result = await client.graphql({
        query: updateNewsletterSubscription,
        variables: {
          input: {
            id: subscription.id,
            preferences
          }
        }
      });

      if (!result.data?.updateNewsletterSubscription) {
        throw new Error('Failed to update newsletter preferences');
      }

      return result.data.updateNewsletterSubscription;
    } catch (error: any) {
      console.error('Error updating newsletter preferences:', error);
      throw new Error(error.message || 'Failed to update newsletter preferences');
    }
  }

  /**
   * Get all subscriptions (admin only)
   */
  async getAllSubscriptions(
    limit: number = 50,
    nextToken?: string,
    filter?: any
  ): Promise<{
    items: NewsletterSubscriptionResponse[];
    nextToken?: string;
  }> {
    try {
      const result = await client.graphql({
        query: listNewsletterSubscriptions,
        variables: { limit, nextToken, filter }
      });

      return {
        items: result.data?.listNewsletterSubscriptions?.items || [],
        nextToken: result.data?.listNewsletterSubscriptions?.nextToken
      };
    } catch (error: any) {
      console.error('Error getting newsletter subscriptions:', error);
      throw new Error('Failed to fetch newsletter subscriptions');
    }
  }

  /**
   * Get newsletter statistics (admin only)
   */
  async getStats(): Promise<NewsletterStats> {
    try {
      const allSubscriptions = await this.getAllSubscriptions(1000);
      const subscriptions = allSubscriptions.items;

      const totalSubscribers = subscriptions.length;
      const activeSubscribers = subscriptions.filter(s => s.status === 'ACTIVE').length;
      const unsubscribedCount = subscriptions.filter(s => s.status === 'UNSUBSCRIBED').length;
      
      // Recent subscriptions (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const recentSubscriptions = subscriptions.filter(s => 
        new Date(s.subscribedAt) > thirtyDaysAgo
      ).length;

      // Top sources
      const sourceCounts: { [key: string]: number } = {};
      subscriptions.forEach(s => {
        sourceCounts[s.source] = (sourceCounts[s.source] || 0) + 1;
      });
      const topSources = Object.entries(sourceCounts)
        .map(([source, count]) => ({ source, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      // Top interests
      const interestCounts: { [key: string]: number } = {};
      subscriptions.forEach(s => {
        s.interests?.forEach(interest => {
          interestCounts[interest] = (interestCounts[interest] || 0) + 1;
        });
      });
      const topInterests = Object.entries(interestCounts)
        .map(([interest, count]) => ({ interest, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      return {
        totalSubscribers,
        activeSubscribers,
        unsubscribedCount,
        recentSubscriptions,
        topSources,
        topInterests
      };
    } catch (error: any) {
      console.error('Error getting newsletter stats:', error);
      throw new Error('Failed to fetch newsletter statistics');
    }
  }

  /**
   * Subscribe to newsletter creation events
   */
  subscribeToNewsletterCreation(callback: (subscription: NewsletterSubscriptionResponse) => void) {
    try {
      const subscription = client.graphql({
        query: onCreateNewsletterSubscription
      }).subscribe({
        next: ({ data }) => {
          if (data.onCreateNewsletterSubscription) {
            callback(data.onCreateNewsletterSubscription);
          }
        },
        error: (error) => {
          console.error('Newsletter subscription creation subscription error:', error);
        }
      });

      return subscription;
    } catch (error) {
      console.error('Error setting up newsletter subscription creation subscription:', error);
      throw new Error('Failed to subscribe to newsletter creation');
    }
  }
}

export const newsletterService = new NewsletterService();
export default newsletterService;
