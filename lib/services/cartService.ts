"use client"

import { generateClient } from '@aws-amplify/api'
import { createCartItem, deleteCartItem, updateCartItem } from '@/src/graphql/mutations'
import { listCartItems, getCartItem } from '@/src/graphql/queries'
import { getCurrentUser } from 'aws-amplify/auth'

const client = generateClient()

export interface CartItemInput {
  productId: string
  productName: string
  productImage?: string
  productPrice: number
  originalPrice?: number
  discount?: number
  quantity: number
  selectedVariant?: string
  selectedSize?: string
  selectedColor?: string
  productBrand?: string
  productCategory?: string
  productDescription?: string
  notes?: string
}

export interface CartItemData {
  id: string
  userId: string
  productId: string
  productName: string
  productImage?: string
  productPrice: number
  originalPrice?: number
  discount?: number
  quantity: number
  selectedVariant?: string
  selectedSize?: string
  selectedColor?: string
  productBrand?: string
  productCategory?: string
  productDescription?: string
  status: 'ACTIVE' | 'SAVED_FOR_LATER' | 'REMOVED'
  dateAdded: string
  dateUpdated: string
  notes?: string
  createdAt: string
  updatedAt: string
}

export interface CartSummary {
  items: CartItemData[]
  totalItems: number
  totalQuantity: number
  subtotal: number
  totalDiscount: number
  total: number
  savedForLaterCount: number
}

export class CartService {
  /**
   * Add item to cart
   */
  static async addToCart(cartData: CartItemInput): Promise<{ success: boolean; cartItem?: CartItemData; message: string }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      // Check if item already exists in cart
      const existingItem = await this.getCartItem(cartData.productId)
      if (existingItem) {
        // Update quantity instead of creating new item
        return await this.updateQuantity(existingItem.id, existingItem.quantity + cartData.quantity)
      }

      const cartInput = {
        userId: user.userId,
        productId: cartData.productId,
        productName: cartData.productName,
        productImage: cartData.productImage || '',
        productPrice: cartData.productPrice,
        originalPrice: cartData.originalPrice || cartData.productPrice,
        discount: cartData.discount || 0,
        quantity: cartData.quantity,
        selectedVariant: cartData.selectedVariant || '',
        selectedSize: cartData.selectedSize || '',
        selectedColor: cartData.selectedColor || '',
        productBrand: cartData.productBrand || '',
        productCategory: cartData.productCategory || '',
        productDescription: cartData.productDescription || '',
        status: 'ACTIVE',
        dateAdded: new Date().toISOString(),
        dateUpdated: new Date().toISOString(),
        notes: cartData.notes || ''
      }

      const result = await client.graphql({
        query: createCartItem,
        variables: { input: cartInput },
        authMode: 'userPool'
      })

      // Dispatch cart update event
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('cartUpdated'))
      }

      return {
        success: true,
        cartItem: result.data.createCartItem,
        message: 'Added to cart successfully'
      }

    } catch (error) {
      console.error('Add to cart error:', error)
      throw new Error(`Failed to add to cart: ${error.message}`)
    }
  }

  /**
   * Remove item from cart
   */
  static async removeFromCart(cartItemId: string): Promise<{ success: boolean; message: string }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      await client.graphql({
        query: deleteCartItem,
        variables: { input: { id: cartItemId } },
        authMode: 'userPool'
      })

      // Dispatch cart update event
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('cartUpdated'))
      }

      return {
        success: true,
        message: 'Removed from cart successfully'
      }

    } catch (error) {
      console.error('Remove from cart error:', error)
      throw new Error(`Failed to remove from cart: ${error.message}`)
    }
  }

  /**
   * Update item quantity
   */
  static async updateQuantity(cartItemId: string, quantity: number): Promise<{ success: boolean; cartItem?: CartItemData; message: string }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      if (quantity <= 0) {
        return await this.removeFromCart(cartItemId)
      }

      const result = await client.graphql({
        query: updateCartItem,
        variables: {
          input: {
            id: cartItemId,
            quantity: quantity,
            dateUpdated: new Date().toISOString()
          }
        },
        authMode: 'userPool'
      })

      // Dispatch cart update event
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('cartUpdated'))
      }

      return {
        success: true,
        cartItem: result.data.updateCartItem,
        message: 'Quantity updated successfully'
      }

    } catch (error) {
      console.error('Update quantity error:', error)
      throw new Error(`Failed to update quantity: ${error.message}`)
    }
  }

  /**
   * Move item to saved for later
   */
  static async saveForLater(cartItemId: string): Promise<{ success: boolean; message: string }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      await client.graphql({
        query: updateCartItem,
        variables: {
          input: {
            id: cartItemId,
            status: 'SAVED_FOR_LATER',
            dateUpdated: new Date().toISOString()
          }
        },
        authMode: 'userPool'
      })

      return {
        success: true,
        message: 'Saved for later successfully'
      }

    } catch (error) {
      console.error('Save for later error:', error)
      throw new Error(`Failed to save for later: ${error.message}`)
    }
  }

  /**
   * Move item back to cart from saved for later
   */
  static async moveToCart(cartItemId: string): Promise<{ success: boolean; message: string }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      await client.graphql({
        query: updateCartItem,
        variables: {
          input: {
            id: cartItemId,
            status: 'ACTIVE',
            dateUpdated: new Date().toISOString()
          }
        },
        authMode: 'userPool'
      })

      return {
        success: true,
        message: 'Moved to cart successfully'
      }

    } catch (error) {
      console.error('Move to cart error:', error)
      throw new Error(`Failed to move to cart: ${error.message}`)
    }
  }

  /**
   * Get user's cart items
   */
  static async getCartItems(status: 'ACTIVE' | 'SAVED_FOR_LATER' | 'ALL' = 'ACTIVE'): Promise<{ success: boolean; items: CartItemData[]; message: string }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      let filter: any = {
        userId: { eq: user.userId }
      }

      if (status !== 'ALL') {
        filter.status = { eq: status }
      }

      const result = await client.graphql({
        query: listCartItems,
        variables: {
          filter: filter,
          limit: 100,
          sortDirection: 'DESC'
        },
        authMode: 'userPool'
      })

      return {
        success: true,
        items: result.data.listCartItems.items,
        message: 'Cart items retrieved successfully'
      }

    } catch (error) {
      console.error('Get cart items error:', error)
      throw new Error(`Failed to get cart items: ${error.message}`)
    }
  }

  /**
   * Get specific cart item by product ID
   */
  static async getCartItem(productId: string): Promise<CartItemData | null> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        return null
      }

      const result = await client.graphql({
        query: listCartItems,
        variables: {
          filter: {
            and: [
              { userId: { eq: user.userId } },
              { productId: { eq: productId } },
              { status: { eq: 'ACTIVE' } }
            ]
          },
          limit: 1
        },
        authMode: 'userPool'
      })

      return result.data.listCartItems.items.length > 0 ? result.data.listCartItems.items[0] : null

    } catch (error) {
      console.error('Get cart item error:', error)
      return null
    }
  }

  /**
   * Get cart summary with calculations
   */
  static async getCartSummary(): Promise<CartSummary> {
    try {
      const activeItems = await this.getCartItems('ACTIVE')
      const savedItems = await this.getCartItems('SAVED_FOR_LATER')

      const items = activeItems.success ? activeItems.items : []
      const savedForLaterCount = savedItems.success ? savedItems.items.length : 0

      const totalItems = items.length
      const totalQuantity = items.reduce((sum, item) => sum + item.quantity, 0)
      const subtotal = items.reduce((sum, item) => sum + (item.productPrice * item.quantity), 0)
      const totalDiscount = items.reduce((sum, item) => {
        const originalPrice = item.originalPrice || item.productPrice
        const discountAmount = (originalPrice - item.productPrice) * item.quantity
        return sum + discountAmount
      }, 0)
      const total = subtotal

      return {
        items,
        totalItems,
        totalQuantity,
        subtotal,
        totalDiscount,
        total,
        savedForLaterCount
      }

    } catch (error) {
      console.error('Get cart summary error:', error)
      return {
        items: [],
        totalItems: 0,
        totalQuantity: 0,
        subtotal: 0,
        totalDiscount: 0,
        total: 0,
        savedForLaterCount: 0
      }
    }
  }

  /**
   * Clear entire cart
   */
  static async clearCart(): Promise<{ success: boolean; message: string }> {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      const cartItems = await this.getCartItems('ACTIVE')
      if (!cartItems.success || cartItems.items.length === 0) {
        return {
          success: true,
          message: 'Cart is already empty'
        }
      }

      // Delete all active cart items
      await Promise.all(
        cartItems.items.map(item => this.removeFromCart(item.id))
      )

      // Dispatch cart update event (removeFromCart already dispatches, but let's be explicit)
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('cartUpdated'))
      }

      return {
        success: true,
        message: 'Cart cleared successfully'
      }

    } catch (error) {
      console.error('Clear cart error:', error)
      throw new Error(`Failed to clear cart: ${error.message}`)
    }
  }

  /**
   * Get cart count (for header display)
   */
  static async getCartCount(): Promise<number> {
    try {
      const summary = await this.getCartSummary()
      return summary.totalQuantity
    } catch (error) {
      console.error('Get cart count error:', error)
      return 0
    }
  }
}

export default CartService
