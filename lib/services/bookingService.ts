"use client"

import { generateClient } from '@aws-amplify/api'
import { createBooking, updateBooking } from '@/src/graphql/mutations'
import { listBookings, getBooking, getUserProfile } from '@/src/graphql/queries'
import { getCurrentUser } from 'aws-amplify/auth'

const client = generateClient()

// Helper function to get user profile and determine if user is a vendor
async function getUserProfileInfo(userId: string) {
  try {
    const result = await client.graphql({
      query: getUserProfile,
      variables: { id: userId },
      authMode: 'userPool'
    })
    return result.data.getUserProfile
  } catch (error) {
    console.warn('Could not fetch user profile:', error)
    return null
  }
}

export interface BookingInput {
  entityId: string
  entityType: 'VENDOR' | 'VENUE'
  entityName: string
  customerName?: string
  customerPhone?: string
  vendorId?: string
  eventDate: string
  eventTime: string
  guestCount: number
  eventType: string
  duration?: string
  specialRequests?: string
  budget?: string
  contactPreference: 'PHONE' | 'EMAIL' | 'WHATSAPP'
}

export interface BookingUpdateInput {
  bookingId: string
  status?: string
  vendorNotes?: string
  estimatedCost?: string
  priority?: string
  notes?: string
}

export interface BookingFilters {
  customerId?: string
  vendorId?: string
  status?: string
  entityType?: string
  limit?: number
}

export class BookingService {
  static version = '1.0.1' // Version for debugging

  /**
   * Create a new booking
   */
  static async createBooking(bookingData: BookingInput) {
    try {
      // Get current user
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      // Get user attributes
      const userAttributes = user.signInDetails?.loginId || user.username

      // Determine vendor ID based on entity type
      // For vendors, we'll store both the entityId and use userId as vendorId for easier querying
      let vendorId = null
      if (bookingData.entityType === 'VENDOR') {
        // Use the provided vendorId if available, otherwise use entityId
        vendorId = bookingData.vendorId || bookingData.entityId
      } else if (bookingData.entityType === 'VENUE') {
        vendorId = bookingData.vendorId || null
      }

      console.log('🔍 Debug - Booking creation:', {
        entityType: bookingData.entityType,
        entityId: bookingData.entityId,
        providedVendorId: bookingData.vendorId,
        finalVendorId: vendorId,
        customerId: user.userId
      })

      // Create booking input
      const bookingInput = {
        customerId: user.userId,
        customerName: bookingData.customerName || userAttributes,
        customerEmail: userAttributes,
        customerPhone: bookingData.customerPhone || '',
        entityId: bookingData.entityId,
        entityType: bookingData.entityType,
        entityName: bookingData.entityName,
        vendorId: vendorId,
        eventDate: bookingData.eventDate,
        eventTime: bookingData.eventTime,
        guestCount: parseInt(bookingData.guestCount.toString()),
        eventType: bookingData.eventType,
        duration: bookingData.duration || '',
        specialRequests: bookingData.specialRequests || '',
        budget: bookingData.budget || '',
        contactPreference: bookingData.contactPreference,
        status: 'PENDING',
        priority: 'MEDIUM',
        notes: '',
        vendorNotes: '',
        paymentStatus: 'PENDING',
        contractSigned: false,
        reminderSent: false,
        communicationLog: [],
        attachments: [],
        metadata: JSON.stringify({
          source: 'web_booking_form',
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        })
      }

      // Create booking in database
      const result = await client.graphql({
        query: createBooking,
        variables: { input: bookingInput },
        authMode: 'userPool'
      })

      return {
        success: true,
        booking: result.data.createBooking,
        message: 'Booking created successfully'
      }

    } catch (error) {
      console.error('Booking creation error:', error)
      throw new Error(`Failed to create booking: ${error.message}`)
    }
  }

  /**
   * Get bookings with filters
   */
  static async getBookings(filters: BookingFilters = {}) {
    try {
      console.log('🔍 Debug - BookingService version:', BookingService.version)

      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      console.log('🔍 Debug - User info:', {
        userId: user.userId,
        username: user.username,
        signInDetails: user.signInDetails
      })

      // Build filter based on user role and parameters
      let filter: any = {}

      console.log('🔍 Debug - BookingService.getBookings called')
      console.log('🔍 Debug - Filters received:', filters)
      console.log('🔍 Debug - Filter checks:', {
        hasCustomerId: !!filters.customerId,
        customerIdValue: filters.customerId,
        customerIdTrimmed: filters.customerId?.trim(),
        hasVendorId: !!filters.vendorId,
        vendorIdValue: filters.vendorId,
        vendorIdTrimmed: filters.vendorId?.trim(),
        filterKeys: Object.keys(filters)
      })

      // Force log the exact condition checks
      console.log('🔍 Debug - Condition 1 (customerId):', filters.customerId && filters.customerId.trim())
      console.log('🔍 Debug - Condition 2 (vendorId):', filters.vendorId && filters.vendorId.trim())

      if (filters.customerId && filters.customerId.trim()) {
        filter.customerId = { eq: filters.customerId }
        console.log('🔍 Debug - USING customerId filter:', filters.customerId)
      } else if (filters.vendorId && filters.vendorId.trim()) {
        // For vendor queries, we need to check both vendorId and entityId
        // because vendors might be stored as either
        filter.or = [
          { vendorId: { eq: filters.vendorId } },
          { entityId: { eq: filters.vendorId } }
        ]
        console.log('🔍 Debug - USING vendorId (OR entityId) filter:', filters.vendorId)
      } else {
        // Default to user's own bookings as customer
        filter.customerId = { eq: user.userId }
        console.log('🔍 Debug - USING default customer filter (no specific filter provided):', user.userId)
        console.log('🔍 Debug - Why default? customerId check:', !filters.customerId || !filters.customerId.trim())
        console.log('🔍 Debug - Why default? vendorId check:', !filters.vendorId || !filters.vendorId.trim())
      }

      if (filters.status) {
        filter.status = { eq: filters.status.toUpperCase() }
      }

      if (filters.entityType) {
        filter.entityType = { eq: filters.entityType.toUpperCase() }
      }

      console.log('🔍 Debug - Final filter:', filter)

      const result = await client.graphql({
        query: listBookings,
        variables: {
          filter: filter,
          limit: filters.limit || 20,
          sortDirection: 'DESC'
        },
        authMode: 'userPool'
      })

      console.log('🔍 Debug - Query result:', {
        itemCount: result.data.listBookings.items.length,
        items: result.data.listBookings.items.map(item => ({
          id: item.id,
          customerId: item.customerId,
          vendorId: item.vendorId,
          entityId: item.entityId,
          entityName: item.entityName
        }))
      })

      return {
        success: true,
        bookings: result.data.listBookings.items,
        nextToken: result.data.listBookings.nextToken
      }

    } catch (error) {
      console.error('Get bookings error:', error)
      throw new Error(`Failed to fetch bookings: ${error.message}`)
    }
  }

  /**
   * Update booking status and details
   */
  static async updateBooking(updateData: BookingUpdateInput) {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      const { bookingId, status, vendorNotes, estimatedCost, notes, priority } = updateData

      if (!bookingId) {
        throw new Error('Booking ID is required')
      }

      // Get existing booking to verify permissions
      const existingBooking = await client.graphql({
        query: getBooking,
        variables: { id: bookingId },
        authMode: 'userPool'
      })

      const booking = existingBooking.data.getBooking
      if (!booking) {
        throw new Error('Booking not found')
      }

      // Check permissions
      const isCustomer = booking.customerId === user.userId
      const isVendor = booking.vendorId === user.userId
      
      if (!isCustomer && !isVendor) {
        throw new Error('Unauthorized to update this booking')
      }

      // Prepare update input
      const updateInput: any = {
        id: bookingId
      }

      if (status) {
        updateInput.status = status.toUpperCase()
      }

      if (vendorNotes && isVendor) {
        updateInput.vendorNotes = vendorNotes
      }

      if (estimatedCost && isVendor) {
        updateInput.estimatedCost = estimatedCost
      }

      if (notes) {
        updateInput.notes = notes
      }

      if (priority && isVendor) {
        updateInput.priority = priority.toUpperCase()
      }

      // Add communication log entry
      const communicationEntry = {
        timestamp: new Date().toISOString(),
        type: 'SYSTEM',
        from: user.userId,
        to: isCustomer ? booking.vendorId : booking.customerId,
        message: `Booking status updated to ${status || 'updated'}`,
        status: 'SENT'
      }

      updateInput.communicationLog = [
        ...(booking.communicationLog || []),
        communicationEntry
      ]

      // Update booking
      const result = await client.graphql({
        query: updateBooking,
        variables: { input: updateInput },
        authMode: 'userPool'
      })

      return {
        success: true,
        booking: result.data.updateBooking,
        message: 'Booking updated successfully'
      }

    } catch (error) {
      console.error('Booking update error:', error)
      throw new Error(`Failed to update booking: ${error.message}`)
    }
  }

  /**
   * Get a single booking by ID
   */
  static async getBookingById(bookingId: string) {
    try {
      const user = await getCurrentUser()
      if (!user) {
        throw new Error('Authentication required')
      }

      const result = await client.graphql({
        query: getBooking,
        variables: { id: bookingId },
        authMode: 'userPool'
      })

      const booking = result.data.getBooking
      if (!booking) {
        throw new Error('Booking not found')
      }

      // Check permissions
      const isCustomer = booking.customerId === user.userId
      const isVendor = booking.vendorId === user.userId
      
      if (!isCustomer && !isVendor) {
        throw new Error('Unauthorized to view this booking')
      }

      return {
        success: true,
        booking: booking
      }

    } catch (error) {
      console.error('Get booking error:', error)
      throw new Error(`Failed to fetch booking: ${error.message}`)
    }
  }

  /**
   * Send booking notification emails
   */
  static async sendBookingNotifications(booking: any) {
    try {
      // This would typically call an email service
      // For now, we'll just log the notification
      console.log('Booking notification sent for:', booking.id)
      
      // You can implement actual email sending here
      // using services like AWS SES, SendGrid, etc.
      
      return {
        success: true,
        message: 'Notifications sent successfully'
      }
    } catch (error) {
      console.error('Notification error:', error)
      return {
        success: false,
        message: 'Failed to send notifications'
      }
    }
  }
}

export default BookingService
