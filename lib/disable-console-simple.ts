/**
 * Simple Console Disabler
 * 
 * Import this file early in your application to disable console logs globally.
 * This is a lightweight version that immediately disables console in production.
 */

// Check environment and disable console if in production
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  // Store original console methods for potential restoration
  const originalConsole = {
    log: console.log,
    warn: console.warn,
    error: console.error,
    info: console.info,
    debug: console.debug,
    trace: console.trace,
    table: console.table,
    group: console.group,
    groupCollapsed: console.groupCollapsed,
    groupEnd: console.groupEnd,
    time: console.time,
    timeEnd: console.timeEnd,
    count: console.count,
    assert: console.assert,
  }

  // Create no-op function
  const noop = () => {}

  // Disable all console methods except error (keep errors for debugging)
  console.log = noop
  console.warn = noop
  console.info = noop
  console.debug = noop
  console.trace = noop
  console.table = noop
  console.group = noop
  console.groupCollapsed = noop
  console.groupEnd = noop
  console.time = noop
  console.timeEnd = noop
  console.count = noop
  console.assert = noop

  // Keep console.error for critical issues
  // console.error = noop // Uncomment this line to disable errors too

  // Add a way to restore console if needed (for debugging)
  ;(window as any).__restoreConsole = () => {
    Object.assign(console, originalConsole)
    console.log('Console restored!')
  }

  // Add a way to completely disable console including errors
  ;(window as any).__disableAllConsole = () => {
    Object.keys(originalConsole).forEach(key => {
      ;(console as any)[key] = noop
    })
  }
}

// Export for manual control if needed
export const disableConsole = () => {
  const noop = () => {}
  console.log = noop
  console.warn = noop
  console.error = noop
  console.info = noop
  console.debug = noop
  console.trace = noop
  console.table = noop
  console.group = noop
  console.groupCollapsed = noop
  console.groupEnd = noop
  console.time = noop
  console.timeEnd = noop
  console.count = noop
  console.assert = noop
}

export const disableConsoleExceptErrors = () => {
  const noop = () => {}
  console.log = noop
  console.warn = noop
  console.info = noop
  console.debug = noop
  console.trace = noop
  console.table = noop
  console.group = noop
  console.groupCollapsed = noop
  console.groupEnd = noop
  console.time = noop
  console.timeEnd = noop
  console.count = noop
  console.assert = noop
  // Keep console.error
}
