/**
 * Test function to submit a real inquiry to the database
 * This will help verify that the inquiry system works with actual data
 */

import { generateClient } from 'aws-amplify/api'
import { createInquiry } from '@/src/graphql/mutations'

const client = generateClient()

export interface TestInquiryInput {
  vendorUserId: string
  vendorId: string
  vendorName: string
  customerName: string
  customerEmail: string
  customerPhone?: string
  eventDate?: string
  message: string
  inquiryType: string
  budget?: string
  guestCount?: string
  venue?: string
}

/**
 * Submit a test inquiry to the database
 */
export async function submitTestInquiry(testData: TestInquiryInput): Promise<any> {
  try {
    console.log('🧪 Submitting test inquiry to database...')
    console.log('Test data:', testData)

    const inquiryInput = {
      vendorUserId: testData.vendorUserId,
      vendorId: testData.vendorId,
      vendorName: testData.vendorName,
      customerUserId: 'test-customer-' + Date.now(),
      customerName: testData.customerName,
      customerEmail: testData.customerEmail,
      customerPhone: testData.customerPhone,
      eventDate: testData.eventDate,
      message: testData.message,
      inquiryType: testData.inquiryType,
      status: 'NEW',
      priority: 'MEDIUM',
      budget: testData.budget,
      guestCount: testData.guestCount,
      venue: testData.venue
    }

    // Try with API key first (public access)
    const result = await client.graphql({
      query: createInquiry,
      variables: { input: inquiryInput },
      authMode: 'apiKey'
    })

    const createdInquiry = result.data.createInquiry
    console.log('✅ Test inquiry created successfully!')
    console.log('Inquiry ID:', createdInquiry.id)
    console.log('Created at:', createdInquiry.createdAt)
    
    return {
      success: true,
      inquiry: createdInquiry,
      message: 'Test inquiry submitted successfully to database'
    }

  } catch (error) {
    console.error('❌ Failed to submit test inquiry:', error)
    
    return {
      success: false,
      error: error.message,
      message: 'Failed to submit test inquiry to database'
    }
  }
}

/**
 * Create sample test data for different vendor types
 */
export const createSampleTestData = (vendorUserId: string, vendorType: 'photographer' | 'venue' | 'decorator' | 'caterer' = 'photographer'): TestInquiryInput => {
  const baseData = {
    vendorUserId: vendorUserId,
    customerName: 'Test Customer',
    customerEmail: '<EMAIL>',
    customerPhone: '+91 9876543210',
    eventDate: '2024-12-31',
    message: 'This is a test inquiry submitted to verify the database connection and inquiry system functionality.'
  }

  switch (vendorType) {
    case 'photographer':
      return {
        ...baseData,
        vendorId: 'test-photographer-' + Date.now(),
        vendorName: 'Test Photography Studio',
        inquiryType: 'VENDOR_INQUIRY',
        budget: '₹50,000 - ₹75,000',
        guestCount: '150',
        venue: 'Grand Palace Hotel',
        message: 'Hi, I am interested in your photography services for my wedding on December 31st. Could you please share your packages and pricing for 150 guests?'
      }

    case 'venue':
      return {
        ...baseData,
        vendorId: 'test-venue-' + Date.now(),
        vendorName: 'Test Wedding Venue',
        inquiryType: 'VENUE_INQUIRY',
        budget: '₹2,00,000 - ₹3,00,000',
        guestCount: '300',
        venue: 'Test Wedding Venue',
        message: 'We are looking for a venue for our wedding reception on December 31st. Can you check availability and share pricing for 300 guests?'
      }

    case 'decorator':
      return {
        ...baseData,
        vendorId: 'test-decorator-' + Date.now(),
        vendorName: 'Test Decoration Services',
        inquiryType: 'SERVICE_QUOTE',
        budget: '₹75,000 - ₹1,00,000',
        guestCount: '200',
        venue: 'Grand Palace Hotel',
        message: 'Need decoration services for wedding ceremony. Please provide quote for floral decorations and stage setup for 200 guests.'
      }

    case 'caterer':
      return {
        ...baseData,
        vendorId: 'test-caterer-' + Date.now(),
        vendorName: 'Test Catering Services',
        inquiryType: 'BOOKING_REQUEST',
        budget: '₹1,50,000 - ₹2,00,000',
        guestCount: '250',
        venue: 'Grand Palace Hotel',
        message: 'Looking for catering services for wedding reception. Need both vegetarian and non-vegetarian menu for 250 guests.'
      }

    default:
      return {
        ...baseData,
        vendorId: 'test-vendor-' + Date.now(),
        vendorName: 'Test Vendor',
        inquiryType: 'GENERAL_QUESTION',
        budget: '₹25,000 - ₹50,000',
        guestCount: '100'
      }
  }
}

/**
 * Test multiple inquiries for a vendor
 */
export async function submitMultipleTestInquiries(vendorUserId: string, count: number = 3): Promise<any[]> {
  const results = []
  const vendorTypes: ('photographer' | 'venue' | 'decorator' | 'caterer')[] = ['photographer', 'venue', 'decorator', 'caterer']
  
  for (let i = 0; i < count; i++) {
    const vendorType = vendorTypes[i % vendorTypes.length]
    const testData = createSampleTestData(vendorUserId, vendorType)
    
    // Add some variation to the data
    testData.customerName = `Test Customer ${i + 1}`
    testData.customerEmail = `test.customer${i + 1}@example.com`
    
    const result = await submitTestInquiry(testData)
    results.push(result)
    
    // Small delay between submissions
    await new Promise(resolve => setTimeout(resolve, 500))
  }
  
  return results
}

// Export for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testInquirySubmission = {
    submitTestInquiry,
    createSampleTestData,
    submitMultipleTestInquiries
  }
  
  console.log('🧪 Test inquiry functions available in window.testInquirySubmission')
  console.log('Usage examples:')
  console.log('- window.testInquirySubmission.submitTestInquiry(testData)')
  console.log('- window.testInquirySubmission.createSampleTestData("your-user-id", "photographer")')
  console.log('- window.testInquirySubmission.submitMultipleTestInquiries("your-user-id", 3)')
}
