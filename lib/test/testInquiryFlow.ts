/**
 * Test script to verify inquiry flow works end-to-end
 * This can be used to test inquiry submission and vendor notification
 */

import { generateClient } from 'aws-amplify/api'
import { createInquiry } from '@/src/graphql/mutations'
import { listInquiries } from '@/src/graphql/queries'

const client = generateClient()

export interface TestInquiryData {
  vendorUserId: string
  vendorId: string
  vendorName: string
  customerName: string
  customerEmail: string
  customerPhone?: string
  eventDate?: string
  message: string
  inquiryType: string
  budget?: string
  guestCount?: string
  venue?: string
}

/**
 * Test inquiry submission
 */
export async function testInquirySubmission(testData: TestInquiryData): Promise<boolean> {
  try {
    console.log('Testing inquiry submission with data:', testData)

    // Create inquiry
    const inquiryInput = {
      vendorUserId: testData.vendorUserId,
      vendorId: testData.vendorId,
      vendorName: testData.vendorName,
      customerUserId: 'test-customer-' + Date.now(),
      customerName: testData.customerName,
      customerEmail: testData.customerEmail,
      customerPhone: testData.customerPhone,
      eventDate: testData.eventDate,
      message: testData.message,
      inquiryType: testData.inquiryType,
      status: 'NEW',
      priority: 'MEDIUM',
      budget: testData.budget,
      guestCount: testData.guestCount,
      venue: testData.venue
    }

    const result = await client.graphql({
      query: createInquiry,
      variables: { input: inquiryInput }
    })

    const createdInquiry = result.data.createInquiry
    console.log('✅ Inquiry created successfully:', createdInquiry.id)

    // Verify inquiry was saved
    const listResult = await client.graphql({
      query: listInquiries,
      variables: {
        filter: {
          vendorUserId: { eq: testData.vendorUserId }
        }
      }
    })

    const inquiries = listResult.data.listInquiries.items
    const foundInquiry = inquiries.find(i => i.id === createdInquiry.id)

    if (foundInquiry) {
      console.log('✅ Inquiry verified in database')
      return true
    } else {
      console.log('❌ Inquiry not found in database')
      return false
    }

  } catch (error) {
    console.error('❌ Test failed:', error)
    return false
  }
}

/**
 * Test vendor inquiry retrieval
 */
export async function testVendorInquiryRetrieval(vendorUserId: string): Promise<boolean> {
  try {
    console.log('Testing vendor inquiry retrieval for:', vendorUserId)

    const result = await client.graphql({
      query: listInquiries,
      variables: {
        filter: {
          vendorUserId: { eq: vendorUserId }
        }
      }
    })

    const inquiries = result.data.listInquiries.items
    console.log(`✅ Retrieved ${inquiries.length} inquiries for vendor`)

    // Log inquiry details
    inquiries.forEach((inquiry, index) => {
      console.log(`Inquiry ${index + 1}:`, {
        id: inquiry.id,
        customer: inquiry.customerName,
        status: inquiry.status,
        type: inquiry.inquiryType,
        createdAt: inquiry.createdAt
      })
    })

    return true

  } catch (error) {
    console.error('❌ Retrieval test failed:', error)
    return false
  }
}

/**
 * Run complete inquiry flow test
 */
export async function runInquiryFlowTest(vendorUserId: string): Promise<void> {
  console.log('🧪 Starting Inquiry Flow Test')
  console.log('================================')

  const testData: TestInquiryData = {
    vendorUserId: vendorUserId,
    vendorId: 'test-vendor-' + Date.now(),
    vendorName: 'Test Vendor Business',
    customerName: 'Test Customer',
    customerEmail: '<EMAIL>',
    customerPhone: '+91 9876543210',
    eventDate: '2024-12-31',
    message: 'This is a test inquiry to verify the inquiry system is working correctly.',
    inquiryType: 'VENDOR_INQUIRY',
    budget: '₹50,000 - ₹75,000',
    guestCount: '100',
    venue: 'Test Venue'
  }

  // Test 1: Submit inquiry
  console.log('\n📝 Test 1: Submitting inquiry...')
  const submissionSuccess = await testInquirySubmission(testData)

  // Test 2: Retrieve vendor inquiries
  console.log('\n📋 Test 2: Retrieving vendor inquiries...')
  const retrievalSuccess = await testVendorInquiryRetrieval(vendorUserId)

  // Summary
  console.log('\n📊 Test Summary')
  console.log('===============')
  console.log(`Inquiry Submission: ${submissionSuccess ? '✅ PASS' : '❌ FAIL'}`)
  console.log(`Inquiry Retrieval: ${retrievalSuccess ? '✅ PASS' : '❌ FAIL'}`)

  if (submissionSuccess && retrievalSuccess) {
    console.log('\n🎉 All tests passed! Inquiry system is working correctly.')
  } else {
    console.log('\n⚠️  Some tests failed. Check the logs above for details.')
  }
}

/**
 * Sample test data for different inquiry types
 */
export const sampleTestData = {
  vendorInquiry: {
    vendorUserId: 'test-vendor-user',
    vendorId: 'vendor-123',
    vendorName: 'Amazing Photography Studio',
    customerName: 'Priya Sharma',
    customerEmail: '<EMAIL>',
    customerPhone: '+91 9876543210',
    eventDate: '2024-12-25',
    message: 'Hi, I am interested in your photography services for my wedding on December 25th. Could you please share your packages and pricing?',
    inquiryType: 'VENDOR_INQUIRY',
    budget: '₹50,000 - ₹75,000',
    guestCount: '150',
    venue: 'Grand Palace Hotel'
  },
  venueInquiry: {
    vendorUserId: 'test-venue-user',
    vendorId: 'venue-456',
    vendorName: 'Grand Palace Hotel',
    customerName: 'Rahul Kumar',
    customerEmail: '<EMAIL>',
    customerPhone: '+91 9876543211',
    eventDate: '2024-11-15',
    message: 'We are looking for a venue for our wedding reception. Can you check availability for November 15th and share the pricing details?',
    inquiryType: 'VENUE_INQUIRY',
    budget: '₹2,00,000 - ₹3,00,000',
    guestCount: '300',
    venue: 'Grand Palace Hotel'
  },
  serviceQuote: {
    vendorUserId: 'test-service-user',
    vendorId: 'service-789',
    vendorName: 'Elegant Decorators',
    customerName: 'Anita Patel',
    customerEmail: '<EMAIL>',
    eventDate: '2025-01-20',
    message: 'Need decoration services for engagement ceremony. Please provide quote for floral decorations and stage setup.',
    inquiryType: 'SERVICE_QUOTE',
    budget: '₹25,000 - ₹40,000',
    guestCount: '100'
  }
}

// Export for use in browser console or testing
if (typeof window !== 'undefined') {
  (window as any).testInquiryFlow = {
    runInquiryFlowTest,
    testInquirySubmission,
    testVendorInquiryRetrieval,
    sampleTestData
  }
}
