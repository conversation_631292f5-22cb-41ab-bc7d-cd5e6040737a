"use client"

import { generateClient } from '@aws-amplify/api'
import { listBookings, listVendors, getUserProfile } from '@/src/graphql/queries'
import { getCurrentUser } from 'aws-amplify/auth'

const client = generateClient()

export class BookingDebug {
  /**
   * Debug function to check all bookings and vendor relationships
   */
  static async debugBookingVendorRelationships() {
    try {
      const user = await getCurrentUser()
      if (!user) {
        console.log('❌ No authenticated user')
        return
      }

      console.group('🔍 Booking Debug - Vendor Relationships')
      
      // 1. Check current user info
      console.log('👤 Current User:', {
        userId: user.userId,
        username: user.username,
        signInDetails: user.signInDetails
      })

      // 2. Check user profile
      try {
        const userProfileResult = await client.graphql({
          query: getUserProfile,
          variables: { id: user.userId },
          authMode: 'userPool'
        })
        console.log('📋 User Profile:', userProfileResult.data.getUserProfile)
      } catch (error) {
        console.log('❌ Could not fetch user profile:', error)
      }

      // 3. Check if user has any vendor records
      try {
        const vendorResult = await client.graphql({
          query: listVendors,
          variables: {
            filter: {
              userId: { eq: user.userId }
            }
          },
          authMode: 'userPool'
        })
        console.log('🏢 User\'s Vendor Records:', vendorResult.data.listVendors.items)
      } catch (error) {
        console.log('❌ Could not fetch vendor records:', error)
      }

      // 4. Check all bookings in the system
      try {
        const allBookingsResult = await client.graphql({
          query: listBookings,
          variables: {
            limit: 100
          },
          authMode: 'userPool'
        })
        
        const bookings = allBookingsResult.data.listBookings.items
        console.log('📅 All Bookings in System:', bookings.length)
        
        bookings.forEach((booking, index) => {
          console.log(`📅 Booking ${index + 1}:`, {
            id: booking.id,
            customerId: booking.customerId,
            vendorId: booking.vendorId,
            entityId: booking.entityId,
            entityType: booking.entityType,
            entityName: booking.entityName,
            status: booking.status
          })
        })

        // 5. Check bookings where current user is customer
        const customerBookings = bookings.filter(b => b.customerId === user.userId)
        console.log('👤 Bookings as Customer:', customerBookings.length)

        // 6. Check bookings where current user is vendor (by vendorId)
        const vendorBookingsByVendorId = bookings.filter(b => b.vendorId === user.userId)
        console.log('🏢 Bookings as Vendor (by vendorId):', vendorBookingsByVendorId.length)

        // 7. Check bookings where current user is vendor (by entityId)
        const vendorBookingsByEntityId = bookings.filter(b => b.entityId === user.userId)
        console.log('🏢 Bookings as Vendor (by entityId):', vendorBookingsByEntityId.length)

      } catch (error) {
        console.log('❌ Could not fetch bookings:', error)
      }

      console.groupEnd()

    } catch (error) {
      console.error('❌ Debug error:', error)
    }
  }

  /**
   * Debug function to check vendor dashboard query
   */
  static async debugVendorDashboardQuery(userId: string) {
    try {
      console.group('🔍 Vendor Dashboard Query Debug')
      
      // Test the exact query used by vendor dashboard
      const filter = {
        or: [
          { vendorId: { eq: userId } },
          { entityId: { eq: userId } }
        ]
      }

      console.log('🔍 Query Filter:', filter)

      const result = await client.graphql({
        query: listBookings,
        variables: {
          filter: filter,
          limit: 50,
          sortDirection: 'DESC'
        },
        authMode: 'userPool'
      })

      console.log('📊 Query Results:', {
        itemCount: result.data.listBookings.items.length,
        items: result.data.listBookings.items
      })

      console.groupEnd()

    } catch (error) {
      console.error('❌ Vendor dashboard query error:', error)
    }
  }

  /**
   * Debug function to create a test booking
   */
  static async createTestBooking() {
    try {
      const user = await getCurrentUser()
      if (!user) {
        console.log('❌ No authenticated user')
        return
      }

      console.group('🔍 Test Booking Creation')
      
      // Import booking service
      const { BookingService } = await import('@/lib/services/bookingService')
      
      const testBookingData = {
        entityId: user.userId, // Use current user as the vendor
        entityType: 'VENDOR' as 'VENDOR' | 'VENUE',
        entityName: 'Test Vendor Service',
        customerName: 'Test Customer',
        customerPhone: '+91 9876543210',
        vendorId: user.userId, // Set vendor ID to current user
        eventDate: '2024-04-15',
        eventTime: '10:00 AM',
        guestCount: 100,
        eventType: 'Wedding',
        duration: '4-6 hours',
        specialRequests: 'Test booking for debugging',
        budget: '₹50,000 - ₹1,00,000',
        contactPreference: 'PHONE' as 'PHONE' | 'EMAIL' | 'WHATSAPP'
      }

      console.log('📝 Creating test booking:', testBookingData)

      const result = await BookingService.createBooking(testBookingData)
      
      console.log('✅ Test booking created:', result)
      
      console.groupEnd()

      return result

    } catch (error) {
      console.error('❌ Test booking creation error:', error)
    }
  }
}

// Make it available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).BookingDebug = BookingDebug
}

export default BookingDebug
