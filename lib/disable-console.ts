/**
 * Console Log Disabler
 * 
 * This script globally disables console logging methods in production
 * while preserving them in development for debugging purposes.
 */

interface ConsoleBackup {
  log: typeof console.log
  warn: typeof console.warn
  error: typeof console.error
  info: typeof console.info
  debug: typeof console.debug
  trace: typeof console.trace
  table: typeof console.table
  group: typeof console.group
  groupCollapsed: typeof console.groupCollapsed
  groupEnd: typeof console.groupEnd
  time: typeof console.time
  timeEnd: typeof console.timeEnd
  count: typeof console.count
  assert: typeof console.assert
}

class ConsoleManager {
  private static instance: ConsoleManager
  private originalConsole: ConsoleBackup | null = null
  private isDisabled = false

  private constructor() {}

  static getInstance(): ConsoleManager {
    if (!ConsoleManager.instance) {
      ConsoleManager.instance = new ConsoleManager()
    }
    return ConsoleManager.instance
  }

  /**
   * Backup original console methods
   */
  private backupConsole(): void {
    if (this.originalConsole) return

    this.originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
      debug: console.debug,
      trace: console.trace,
      table: console.table,
      group: console.group,
      groupCollapsed: console.groupCollapsed,
      groupEnd: console.groupEnd,
      time: console.time,
      timeEnd: console.timeEnd,
      count: console.count,
      assert: console.assert,
    }
  }

  /**
   * Disable all console methods
   */
  disable(): void {
    if (this.isDisabled) return

    this.backupConsole()

    // Create no-op functions
    const noop = () => {}

    // Override console methods
    console.log = noop
    console.warn = noop
    console.error = noop
    console.info = noop
    console.debug = noop
    console.trace = noop
    console.table = noop
    console.group = noop
    console.groupCollapsed = noop
    console.groupEnd = noop
    console.time = noop
    console.timeEnd = noop
    console.count = noop
    console.assert = noop

    this.isDisabled = true
  }

  /**
   * Disable only specific console methods
   */
  disableSpecific(methods: (keyof ConsoleBackup)[]): void {
    this.backupConsole()

    const noop = () => {}

    methods.forEach(method => {
      if (this.originalConsole && method in this.originalConsole) {
        ;(console as any)[method] = noop
      }
    })
  }

  /**
   * Restore original console methods
   */
  restore(): void {
    if (!this.originalConsole || !this.isDisabled) return

    console.log = this.originalConsole.log
    console.warn = this.originalConsole.warn
    console.error = this.originalConsole.error
    console.info = this.originalConsole.info
    console.debug = this.originalConsole.debug
    console.trace = this.originalConsole.trace
    console.table = this.originalConsole.table
    console.group = this.originalConsole.group
    console.groupCollapsed = this.originalConsole.groupCollapsed
    console.groupEnd = this.originalConsole.groupEnd
    console.time = this.originalConsole.time
    console.timeEnd = this.originalConsole.timeEnd
    console.count = this.originalConsole.count
    console.assert = this.originalConsole.assert

    this.isDisabled = false
  }

  /**
   * Check if console is currently disabled
   */
  getStatus(): boolean {
    return this.isDisabled
  }

  /**
   * Enable console only for errors (useful for production debugging)
   */
  enableErrorsOnly(): void {
    this.disable()
    
    if (this.originalConsole) {
      console.error = this.originalConsole.error
      console.warn = this.originalConsole.warn
    }
  }
}

// Export singleton instance
export const consoleManager = ConsoleManager.getInstance()

/**
 * Auto-disable console in production
 */
export function autoDisableConsole(): void {
  // Check if we're in production environment
  const isProduction = process.env.NODE_ENV === 'production'
  const isClient = typeof window !== 'undefined'
  
  if (isProduction && isClient) {
    consoleManager.disable()
  }
}

/**
 * Conditional console logging
 * Use this instead of console.log for important logs that should work even when console is disabled
 */
export const safeConsole = {
  log: (...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(...args)
    }
  },
  warn: (...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.warn(...args)
    }
  },
  error: (...args: any[]) => {
    // Always log errors, even in production
    console.error(...args)
  },
  info: (...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.info(...args)
    }
  },
  debug: (...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(...args)
    }
  }
}

/**
 * Development-only console wrapper
 */
export const devConsole = {
  log: (...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 [DEV]', ...args)
    }
  },
  warn: (...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.warn('⚠️ [DEV]', ...args)
    }
  },
  error: (...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ [DEV]', ...args)
    }
  }
}

// Auto-initialize
if (typeof window !== 'undefined') {
  autoDisableConsole()
}

export default consoleManager
