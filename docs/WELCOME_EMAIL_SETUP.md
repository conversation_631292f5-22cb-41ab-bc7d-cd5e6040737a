# Welcome Email System Setup

## Overview

The welcome email system automatically sends welcome emails when:
1. **Newsletter Subscription**: Users subscribe to the newsletter
2. **User Signup**: New users create accounts and log in for the first time
3. **User Login**: Returning users log in (once per session)

## Features

### 📧 **Newsletter Welcome Email**
- Sent immediately when users subscribe to newsletter
- Personalized with user's name and interests
- Includes subscription preferences and frequency
- Links to manage preferences and unsubscribe

### 👋 **User Welcome Emails**
- **Signup Welcome**: Sent when new users create accounts
- **Login Welcome**: Sent to returning users (once per session)
- Different content for customers vs vendors
- Includes platform features and next steps

## Email Providers Supported

### 1. **AWS SES (Recommended for Production)**
```env
AWS_REGION=ap-south-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
FROM_EMAIL=<EMAIL>
```

### 2. **SendGrid**
```env
SENDGRID_API_KEY=your_sendgrid_api_key
FROM_EMAIL=<EMAIL>
```

### 3. **SMTP (Generic)**
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
FROM_EMAIL=<EMAIL>
```

## Setup Instructions

### Step 1: Choose Email Provider

#### Option A: AWS SES Setup
1. Go to AWS SES Console
2. Verify your domain or email address
3. Create IAM user with SES permissions
4. Add environment variables to `.env.local`

#### Option B: SendGrid Setup
1. Create SendGrid account
2. Generate API key with Mail Send permissions
3. Verify sender identity
4. Add environment variables to `.env.local`

#### Option C: SMTP Setup
1. Use any SMTP provider (Gmail, Outlook, etc.)
2. Generate app password if using Gmail
3. Add environment variables to `.env.local`

### Step 2: Environment Variables

Create `.env.local` file in your project root:

```env
# Choose ONE email provider

# AWS SES (Recommended)
AWS_REGION=ap-south-1
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here

# OR SendGrid
# SENDGRID_API_KEY=your_sendgrid_api_key_here

# OR SMTP
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_app_password

# Required for all providers
FROM_EMAIL=<EMAIL>
```

### Step 3: Install Dependencies

```bash
# For AWS SES
npm install @aws-sdk/client-ses

# For SendGrid
npm install @sendgrid/mail

# For SMTP
npm install nodemailer
npm install @types/nodemailer --save-dev
```

### Step 4: Test the Setup

1. Visit `/test-welcome-emails` to test email functionality
2. Try different email types (newsletter, signup, login)
3. Check console logs in development mode
4. Verify emails are received in production

## How It Works

### Automatic Triggers

#### Newsletter Subscription
```typescript
// In newsletterService.ts
const subscription = await createNewsletterSubscription(data);

// Automatically sends welcome email
await emailService.sendNewsletterWelcomeEmail({
  email: subscription.email,
  firstName: subscription.firstName,
  lastName: subscription.lastName,
  type: 'newsletter',
  preferences: subscription.preferences,
  interests: subscription.interests
});
```

#### User Signup
```typescript
// In AuthContext.tsx - when new profile is created
const newProfile = await profileService.createProfileFromCognitoUser(user);

// Automatically sends signup welcome email
await emailService.sendUserWelcomeEmail({
  email: newProfile.email,
  firstName: newProfile.firstName,
  lastName: newProfile.lastName,
  type: 'signup',
  isVendor: newProfile.isVendor
});
```

#### User Login
```typescript
// In AuthContext.tsx - when existing user logs in
if (!hasWelcomedThisSession && profile.email) {
  await emailService.sendUserWelcomeEmail({
    email: profile.email,
    firstName: profile.firstName,
    lastName: profile.lastName,
    type: 'login',
    isVendor: profile.isVendor
  });
  sessionStorage.setItem('welcomeEmailSent', 'true');
}
```

### Manual Usage

```typescript
import { emailService } from '@/lib/services/emailService';

// Send newsletter welcome
await emailService.sendNewsletterWelcomeEmail({
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  type: 'newsletter',
  interests: ['PHOTOGRAPHY', 'VENUES'],
  preferences: {
    weddingTips: true,
    vendorRecommendations: true,
    specialOffers: false,
    frequency: 'WEEKLY'
  }
});

// Send user welcome
await emailService.sendUserWelcomeEmail({
  email: '<EMAIL>',
  firstName: 'Jane',
  lastName: 'Smith',
  type: 'signup',
  isVendor: true,
  businessName: 'Smith Photography'
});
```

## Email Templates

### Newsletter Welcome Email Features
- 🎉 Welcome message with personalized greeting
- 📝 List of what to expect (tips, recommendations, offers)
- 🎯 Personalized interests section
- 📅 Email frequency information
- 🔗 Links to manage preferences and unsubscribe
- 💕 Wedding-themed design with gradients

### User Welcome Email Features
- 👋 Different messages for signup vs login
- 🏢 Vendor-specific content vs couple content
- 📱 Platform features overview
- 🎯 Next steps and action items
- 🔗 Direct links to relevant sections
- 📞 Support contact information

## Development Mode

In development (when no email provider is configured):
- Emails are logged to console instead of being sent
- Full email content is displayed for testing
- No actual emails are sent to avoid spam during development

## Production Considerations

### Email Deliverability
1. **Domain Verification**: Verify your sending domain
2. **SPF/DKIM Records**: Set up proper DNS records
3. **Reputation Management**: Monitor bounce and complaint rates
4. **Unsubscribe Links**: Always include unsubscribe options

### Rate Limiting
- AWS SES: Default 200 emails/day (can be increased)
- SendGrid: Varies by plan
- SMTP: Depends on provider limits

### Error Handling
- Email failures don't block user signup/login
- Errors are logged for monitoring
- Graceful fallbacks between providers

## Monitoring & Analytics

### Logs to Monitor
- Email send success/failure rates
- Bounce and complaint rates
- User engagement with welcome emails
- Provider-specific metrics

### Recommended Monitoring
```typescript
// Add to your monitoring service
console.log('Email sent:', {
  type: 'newsletter_welcome',
  recipient: email,
  success: true,
  provider: 'AWS SES',
  timestamp: new Date().toISOString()
});
```

## Troubleshooting

### Common Issues

1. **Emails not sending**
   - Check environment variables
   - Verify email provider credentials
   - Check console for error messages

2. **Emails going to spam**
   - Verify domain authentication
   - Check email content for spam triggers
   - Monitor sender reputation

3. **Rate limiting**
   - Implement queue system for high volume
   - Spread out email sending
   - Upgrade provider plan if needed

### Debug Steps
1. Test with `/test-welcome-emails` page
2. Check browser console for errors
3. Verify environment variables are loaded
4. Test with different email providers
5. Check email provider dashboards for delivery status

## Future Enhancements

### Planned Features
- Email templates management UI
- A/B testing for email content
- Advanced personalization
- Email analytics dashboard
- Automated email sequences
- Multi-language support

### Integration Ideas
- CRM integration for lead tracking
- Marketing automation workflows
- Event-triggered email campaigns
- User behavior-based emails
