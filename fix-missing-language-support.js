const fs = require('fs');
const path = require('path');

// Complete translations for missing languages
const completeTranslations = {
  pa: {
    // Punjabi translations
    whyChooseUs: {
      title: "ਤਿਰੁਮਣਮ 360 ਕਿਉਂ ਚੁਣੋ?",
      subtitle: "ਭਾਰਤ ਦੇ ਸਭ ਤੋਂ ਭਰੋਸੇਮੰਦ ਵਿਆਹ ਯੋਜਨਾ ਪਲੇਟਫਾਰਮ ਨਾਲ ਫਰਕ ਦਾ ਅਨੁਭਵ ਕਰੋ",
      verifiedVendors: {
        title: "ਪ੍ਰਮਾਣਿਤ ਵਿਕਰੇਤਾ",
        description: "ਸਾਡੇ ਸਾਰੇ ਵਿਕਰੇਤਾ ਅਸਲ ਸਮੀਖਿਆਵਾਂ ਅਤੇ ਗੁਣਵੱਤਾ ਦੀ ਗਾਰੰਟੀ ਨਾਲ ਪੂਰੀ ਤਰ੍ਹਾਂ ਪ੍ਰਮਾਣਿਤ ਹਨ"
      },
      bestPrices: {
        title: "ਸਭ ਤੋਂ ਵਧੀਆ ਕੀਮਤਾਂ",
        description: "ਚੋਟੀ ਦੇ ਵਿਆਹ ਵਿਕਰੇਤਾਵਾਂ ਤੋਂ ਪ੍ਰਤੀਯੋਗੀ ਕੀਮਤਾਂ ਅਤੇ ਵਿਸ਼ੇਸ਼ ਸੌਦੇ ਪ੍ਰਾਪਤ ਕਰੋ"
      },
      support: {
        title: "24/7 ਸਹਾਇਤਾ",
        description: "ਸਹਾਇਤਾ ਲਈ ਚੌਵੀ ਘੰਟੇ ਉਪਲਬਧ ਸਮਰਪਿਤ ਵਿਆਹ ਯੋਜਨਾ ਸਹਾਇਤਾ ਟੀਮ"
      }
    },
    destinations: {
      title: "ਪ੍ਰਸਿੱਧ ਵਿਆਹ ਮੰਜ਼ਿਲਾਂ",
      subtitle: "{stateName} ਭਰ ਵਿੱਚ ਸਭ ਤੋਂ ਵੱਧ ਮੰਗ ਵਾਲੀਆਂ ਵਿਆਹ ਮੰਜ਼ਿਲਾਂ ਦੀ ਖੋਜ ਕਰੋ",
      exploreAll: "ਸਾਰੇ ਸ਼ਹਿਰਾਂ ਦੀ ਖੋਜ ਕਰੋ",
      cities: {
        // Tamil Nadu cities in Punjabi
        chennai: { name: "ਚੇਨਈ", venues: "2,500+ ਸਥਾਨ", tagline: "ਮਰੀਨਾ ਬੀਚ ਸਿਟੀ" },
        coimbatore: { name: "ਕੋਇੰਬਟੂਰ", venues: "1,200+ ਸਥਾਨ", tagline: "ਟੈਕਸਟਾਇਲ ਰਾਜਧਾਨੀ" },
        madurai: { name: "ਮਦੁਰਈ", venues: "800+ ਸਥਾਨ", tagline: "ਮੰਦਿਰ ਸ਼ਹਿਰ" },
        tirunelveli: { name: "ਤਿਰੁਨੇਲਵੇਲੀ", venues: "600+ ਸਥਾਨ", tagline: "ਚਾਵਲ ਬਾਊਲ ਸਿਟੀ" },
        
        // Punjab cities in Punjabi
        chandigarh: { name: "ਚੰਡੀਗੜ੍ਹ", venues: "1,500+ ਸਥਾਨ", tagline: "ਸੁੰਦਰ ਸ਼ਹਿਰ" },
        ludhiana: { name: "ਲੁਧਿਆਣਾ", venues: "1,200+ ਸਥਾਨ", tagline: "ਉਦਯੋਗਿਕ ਰਾਜਧਾਨੀ" },
        amritsar: { name: "ਅੰਮ੍ਰਿਤਸਰ", venues: "1,000+ ਸਥਾਨ", tagline: "ਪਵਿੱਤਰ ਸ਼ਹਿਰ" },
        jalandhar: { name: "ਜਲੰਧਰ", venues: "800+ ਸਥਾਨ", tagline: "ਖੇਡ ਰਾਜਧਾਨੀ" },
        
        // Other major cities
        mumbai: { name: "ਮੁੰਬਈ", venues: "4,000+ ਸਥਾਨ", tagline: "ਸੁਪਨਿਆਂ ਦਾ ਸ਼ਹਿਰ" },
        delhi: { name: "ਦਿੱਲੀ", venues: "3,500+ ਸਥਾਨ", tagline: "ਰਾਜਧਾਨੀ ਸ਼ਹਿਰ" },
        bangalore: { name: "ਬੈਂਗਲੋਰ", venues: "3,000+ ਸਥਾਨ", tagline: "ਗਾਰਡਨ ਸਿਟੀ" },
        hyderabad: { name: "ਹੈਦਰਾਬਾਦ", venues: "2,800+ ਸਥਾਨ", tagline: "ਮੋਤੀਆਂ ਦਾ ਸ਼ਹਿਰ" }
      }
    }
  },
  
  bn: {
    // Bengali translations
    whyChooseUs: {
      title: "কেন তিরুমণম 360 বেছে নেবেন?",
      subtitle: "ভারতের সবচেয়ে বিশ্বস্ত বিবাহ পরিকল্পনা প্ল্যাটফর্মের সাথে পার্থক্য অনুভব করুন",
      verifiedVendors: {
        title: "যাচাইকৃত বিক্রেতা",
        description: "আমাদের সমস্ত বিক্রেতা প্রকৃত পর্যালোচনা এবং গুণমান নিশ্চয়তা সহ সম্পূর্ণভাবে যাচাই করা হয়েছে"
      },
      bestPrices: {
        title: "সেরা দাম",
        description: "শীর্ষ বিবাহ বিক্রেতাদের কাছ থেকে প্রতিযোগিতামূলক মূল্য এবং বিশেষ ডিল পান"
      },
      support: {
        title: "24/7 সহায়তা",
        description: "সহায়তার জন্য চব্বিশ ঘন্টা উপলব্ধ নিবেদিত বিবাহ পরিকল্পনা সহায়তা দল"
      }
    },
    destinations: {
      title: "জনপ্রিয় বিবাহের গন্তব্য",
      subtitle: "{stateName} জুড়ে সবচেয়ে চাহিদাসম্পন্ন বিবাহের গন্তব্যগুলি আবিষ্কার করুন",
      exploreAll: "সমস্ত শহর অন্বেষণ করুন",
      cities: {
        // West Bengal cities in Bengali
        kolkata: { name: "কলকাতা", venues: "3,500+ স্থান", tagline: "আনন্দের শহর" },
        siliguri: { name: "শিলিগুড়ি", venues: "800+ স্থান", tagline: "উত্তরপূর্বের প্রবেশদ্বার" },
        durgapur: { name: "দুর্গাপুর", venues: "600+ স্থান", tagline: "ইস্পাত শহর" },
        asansol: { name: "আসানসোল", venues: "500+ স্থান", tagline: "কয়লা রাজধানী" },
        
        // Other major cities in Bengali
        chennai: { name: "চেন্নাই", venues: "2,500+ স্থান", tagline: "মেরিনা বিচ সিটি" },
        mumbai: { name: "মুম্বাই", venues: "4,000+ স্থান", tagline: "স্বপ্নের শহর" },
        delhi: { name: "দিল্লি", venues: "3,500+ স্থান", tagline: "রাজধানী শহর" },
        bangalore: { name: "ব্যাঙ্গালোর", venues: "3,000+ স্থান", tagline: "গার্ডেন সিটি" },
        hyderabad: { name: "হায়দরাবাদ", venues: "2,800+ স্থান", tagline: "মুক্তার শহর" },
        pune: { name: "পুনে", venues: "2,200+ স্থান", tagline: "পূর্বের অক্সফোর্ড" }
      }
    }
  }
};

// Enhanced city translations for existing languages
const enhancedCityTranslations = {
  ml: {
    // Malayalam city translations
    cities: {
      // Kerala cities in Malayalam
      kochi: { name: "കൊച്ചി", venues: "1,500+ വേദികൾ", tagline: "അറബിക്കടലിന്റെ രാണി" },
      thiruvananthapuram: { name: "തിരുവനന്തപുരം", venues: "1,000+ വേദികൾ", tagline: "സദാഹരിത നഗരം" },
      kozhikode: { name: "കോഴിക്കോട്", venues: "700+ വേദികൾ", tagline: "മസാലകളുടെ നഗരം" },
      thrissur: { name: "തൃശ്ശൂർ", venues: "600+ വേദികൾ", tagline: "സാംസ്കാരിക തലസ്ഥാനം" },
      
      // Other cities in Malayalam
      chennai: { name: "ചെന്നൈ", venues: "2,500+ വേദികൾ", tagline: "മെരീന ബീച്ച് സിറ്റി" },
      coimbatore: { name: "കോയമ്പത്തൂർ", venues: "1,200+ വേദികൾ", tagline: "ടെക്സ്റ്റൈൽ തലസ്ഥാനം" },
      madurai: { name: "മധുര", venues: "800+ വേദികൾ", tagline: "ക്ഷേത്ര നഗരം" },
      tirunelveli: { name: "തിരുനെൽവേലി", venues: "600+ വേദികൾ", tagline: "റൈസ് ബൗൾ സിറ്റി" },
      mumbai: { name: "മുംബൈ", venues: "4,000+ വേദികൾ", tagline: "സ്വപ്നങ്ങളുടെ നഗരം" },
      bangalore: { name: "ബാംഗ്ലൂർ", venues: "3,000+ വേദികൾ", tagline: "ഗാർഡൻ സിറ്റി" },
      hyderabad: { name: "ഹൈദരാബാദ്", venues: "2,800+ വേദികൾ", tagline: "മുത്തുകളുടെ നഗരം" }
    }
  },
  
  te: {
    // Telugu city translations
    cities: {
      // Andhra Pradesh & Telangana cities in Telugu
      hyderabad: { name: "హైదరాబాద్", venues: "2,800+ వేదికలు", tagline: "ముత్యాల నగరం" },
      visakhapatnam: { name: "విశాఖపట్టణం", venues: "1,200+ వేదికలు", tagline: "విధి నగరం" },
      vijayawada: { name: "విజయవాడ", venues: "900+ వేదికలు", tagline: "వ్యాపార రాజధాని" },
      warangal: { name: "వరంగల్", venues: "600+ వేదికలు", tagline: "వారసత్వ నగరం" },
      guntur: { name: "గుంటూరు", venues: "600+ వేదికలు", tagline: "మిరపకాయ నగరం" },
      tirupati: { name: "తిరుపతి", venues: "500+ వేదికలు", tagline: "ఆధ్యాత్మిక రాజధాని" },
      
      // Other cities in Telugu
      chennai: { name: "చెన్నై", venues: "2,500+ వేదికలు", tagline: "మెరీనా బీచ్ సిటీ" },
      bangalore: { name: "బెంగళూరు", venues: "3,000+ వేదికలు", tagline: "గార్డెన్ సిటీ" },
      mumbai: { name: "ముంబై", venues: "4,000+ వేదికలు", tagline: "కలల నగరం" },
      pune: { name: "పూణే", venues: "2,200+ వేదికలు", tagline: "తూర్పు ఆక్స్‌ఫర్డ్" }
    }
  },
  
  mr: {
    // Marathi city translations
    cities: {
      // Maharashtra cities in Marathi
      mumbai: { name: "मुंबई", venues: "4,000+ ठिकाणे", tagline: "स्वप्नांचे शहर" },
      pune: { name: "पुणे", venues: "2,200+ ठिकाणे", tagline: "पूर्वेचे ऑक्सफर्ड" },
      nagpur: { name: "नागपूर", venues: "1,000+ ठिकाणे", tagline: "संत्रा शहर" },
      nashik: { name: "नाशिक", venues: "800+ ठिकाणे", tagline: "वाइन राजधानी" },
      
      // Other cities in Marathi
      chennai: { name: "चेन्नई", venues: "2,500+ ठिकाणे", tagline: "मरीना बीच सिटी" },
      bangalore: { name: "बेंगळूरू", venues: "3,000+ ठिकाणे", tagline: "गार्डन सिटी" },
      hyderabad: { name: "हैदराबाद", venues: "2,800+ ठिकाणे", tagline: "मोत्यांचे शहर" },
      delhi: { name: "दिल्ली", venues: "3,500+ ठिकाणे", tagline: "राजधानी शहर" }
    }
  }
};

// Languages to update
const languages = ['pa', 'bn', 'ml', 'te', 'mr'];

// Function to update a language file
function updateLanguageFile(lang) {
  const filePath = path.join(__dirname, 'public', 'locales', lang, 'common.json');
  
  try {
    // Read existing file
    const data = fs.readFileSync(filePath, 'utf8');
    const translations = JSON.parse(data);
    
    // Update with complete translations if available
    if (completeTranslations[lang]) {
      const langData = completeTranslations[lang];
      
      // Update whyChooseUs section
      if (langData.whyChooseUs) {
        translations.whyChooseUs = langData.whyChooseUs;
      }
      
      // Update destinations section
      if (langData.destinations) {
        translations.destinations = { ...translations.destinations, ...langData.destinations };
      }
    }
    
    // Update with enhanced city translations
    if (enhancedCityTranslations[lang]) {
      const cityData = enhancedCityTranslations[lang];
      
      if (cityData.cities) {
        if (!translations.destinations) translations.destinations = {};
        if (!translations.destinations.cities) translations.destinations.cities = {};
        
        // Update city translations
        Object.keys(cityData.cities).forEach(cityKey => {
          translations.destinations.cities[cityKey] = cityData.cities[cityKey];
        });
      }
    }
    
    // Write back to file
    fs.writeFileSync(filePath, JSON.stringify(translations, null, 2), 'utf8');
    console.log(`✅ Updated ${lang}/common.json with missing language support`);
    
  } catch (error) {
    console.error(`❌ Error updating ${lang}/common.json:`, error.message);
  }
}

// Update all language files
languages.forEach(updateLanguageFile);

console.log('\n🎉 All missing language support updated successfully!');
console.log('📍 Updated languages: Punjabi (pa), Bengali (bn), Malayalam (ml), Telugu (te), Marathi (mr)');
console.log('📝 Updated sections: Why Choose Us, Popular Wedding Destinations with city translations');
