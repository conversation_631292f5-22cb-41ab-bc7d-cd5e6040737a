"use client"

import React, { createContext, useContext, useEffect, useState } from 'react';
import { getCurrentUser, signIn, signOut, signUp, confirmSignUp, AuthUser, resendSignUpCode, resetPassword, confirmResetPassword } from 'aws-amplify/auth';
import { profileService } from '@/lib/services/profileService';
import { HybridMobileAuthService } from '@/lib/services/hybridMobileAuth';

interface BusinessSignUpData {
  businessName: string;
  businessType: string;
  fullName: string;
  emailOrPhone: string;
  password: string;
  isPhone?: boolean;
}

type UserRole = 'customer' | 'vendor' | 'admin' | 'super_admin';

interface AuthContextType {
  user: AuthUser | null;
  userProfile: any | null;
  userType: UserRole | null;
  userRole: UserRole | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isVendor: boolean;
  isAdmin: boolean;
  isSuperAdmin: boolean;
  signInWithPassword: (emailOrPhone: string, password: string) => Promise<any>;
  signUp: (emailOrPhone: string, fullName: string, password: string, isPhone?: boolean) => Promise<any>;
  businessSignUp: (businessData: BusinessSignUpData) => Promise<any>;
  signOut: () => Promise<void>;
  confirmSignUp: (emailOrPhone: string, code: string) => Promise<any>;
  resendConfirmationCode: (emailOrPhone: string) => Promise<any>;
  requestPasswordReset: (emailOrPhone: string) => Promise<any>;
  confirmPasswordReset: (emailOrPhone: string, code: string, newPassword: string) => Promise<any>;
  refreshUserProfile: () => Promise<void>;
  sendOTP: (phone: string, countryCode?: string) => Promise<{ success: boolean; message: string; session?: string; }>;
  verifyOTP: (sessionId: string, otp: string) => Promise<{ success: boolean; message: string; user?: any; isNewUser?: boolean }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [userProfile, setUserProfile] = useState<any | null>(null);
  const [userType, setUserType] = useState<UserRole | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [pendingUser, setPendingUser] = useState<string | null>(null);

  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      // Check if we already have cached auth state (temporarily disabled for debugging)
      const cachedAuth = sessionStorage.getItem('auth-state');
      if (false && cachedAuth) { // Temporarily disabled
        const { user, userProfile, userType, timestamp } = JSON.parse(cachedAuth);
        // Use cached data if it's less than 5 minutes old
        if (Date.now() - timestamp < 5 * 60 * 1000) {
          setUser(user);
          setUserProfile(userProfile);
          setUserType(userType);
          setIsAuthenticated(true);
          setIsLoading(false);
          return;
        }
      }

      const currentUser = await getCurrentUser();
      setUser(currentUser);
      setIsAuthenticated(true);

      // Fetch user profile to determine user type
      await fetchUserProfile(currentUser.userId);

      // Cache auth state
      sessionStorage.setItem('auth-state', JSON.stringify({
        user: currentUser,
        userProfile,
        userType,
        timestamp: Date.now()
      }));
    } catch (error) {
      setUser(null);
      setUserProfile(null);
      setUserType(null);
      setIsAuthenticated(false);
      sessionStorage.removeItem('auth-state');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchUserProfile = async (userId?: string) => {
    try {
      if (process.env.NODE_ENV === 'development') {
        console.log('fetchUserProfile called with userId:', userId);
      }
      const profile = await profileService.getProfile(userId);
      if (process.env.NODE_ENV === 'development') {
        console.log('Profile fetched from service:', profile);
      }

      if (profile) {
        setUserProfile(profile);

        // Determine user type based on profile data with priority order
        let determinedUserType: UserRole = 'customer'; // Default

        if (process.env.NODE_ENV === 'development') {
          console.log('Profile data for user type detection:', {
            isSuperAdmin: profile.isSuperAdmin,
            isAdmin: profile.isAdmin,
            isVendor: profile.isVendor,
            role: profile.role,
            businessInfo: profile.businessInfo
          });
        }

        // Check for super admin (highest priority)
        if (profile.isSuperAdmin || profile.role === 'super_admin') {
          determinedUserType = 'super_admin';
        }
        // Check for admin
        else if (profile.isAdmin || profile.role === 'admin') {
          determinedUserType = 'admin';
        }
        // Check for vendor
        else if (profile.isVendor || (profile.businessInfo && profile.businessInfo.businessName)) {
          determinedUserType = 'vendor';
        }
        // Default to customer
        else {
          determinedUserType = 'customer';
        }

        if (process.env.NODE_ENV === 'development') {
          console.log('Determined user type:', determinedUserType);
        }

        setUserType(determinedUserType);
      } else {
        // No profile exists - check if there's pending profile creation data
        console.log('No profile found, checking for pending profile data...');

        // Check if there's pending business or user data that should be used for profile creation
        const pendingBusinessData = localStorage.getItem('pendingBusinessData');
        const pendingUserData = localStorage.getItem('pendingUserData');
        const skipAutoCreation = localStorage.getItem('skipAutoProfileCreation');

        if (pendingBusinessData || pendingUserData || skipAutoCreation) {
          console.log('Found pending profile data or skip flag, skipping automatic creation to allow manual creation');
          setUserProfile(null);
          setUserType(null);
        } else {
          // No pending data, try to create profile automatically from Cognito user
          try {
            if (user) {
              console.log('Creating profile automatically from Cognito user data');
              const newProfile = await profileService.createProfileFromCognitoUser(user);
              setUserProfile(newProfile);
              setUserType(newProfile.isVendor ? 'vendor' : 'customer');
              console.log('Profile created successfully');
            } else {
              setUserProfile(null);
              setUserType(null);
            }
          } catch (createError) {
            console.error('Failed to create profile automatically:', createError);
            setUserProfile(null);
            setUserType('customer'); // Default to customer if profile creation fails
          }
        }
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
      setUserProfile(null);
      setUserType('customer'); // Default to customer if profile fetch fails
    }
  };

  const refreshUserProfile = async () => {
    if (user?.userId) {
      await fetchUserProfile(user.userId);
    }
  };

  const handleSignInWithPassword = async (emailOrPhone: string, password: string) => {
    try {
      const result = await signIn({
        username: emailOrPhone,
        password: password
      });

      await checkAuthState();
      return result;
    } catch (error: any) {
      // If user doesn't exist, we'll handle it in the UI
      if (error.name === 'UserNotFoundException') {
        throw new Error('User not found. Please sign up first.');
      }
      if (error.name === 'NotAuthorizedException') {
        throw new Error('Incorrect password. Please try again.');
      }
      throw error;
    }
  };

  const handleRequestPasswordReset = async (emailOrPhone: string) => {
    try {
      const result = await resetPassword({ username: emailOrPhone });
      setPendingUser(emailOrPhone);
      return result;
    } catch (error: any) {
      if (error.name === 'UserNotFoundException') {
        throw new Error('User not found. Please sign up first.');
      }
      throw error;
    }
  };

  const handleConfirmPasswordReset = async (emailOrPhone: string, code: string, newPassword: string) => {
    try {
      const result = await confirmResetPassword({
        username: emailOrPhone,
        confirmationCode: code,
        newPassword: newPassword
      });

      setPendingUser(null);
      return result;
    } catch (error) {
      throw error;
    }
  };

  const handleSignUp = async (emailOrPhone: string, fullName: string, password: string, isPhone: boolean = false) => {
    try {
      const userAttributes: any = {
        name: fullName,
      };

      // Determine if it's email or phone and set appropriate attributes
      if (isPhone) {
        userAttributes.phone_number = emailOrPhone.startsWith('+') ? emailOrPhone : `+91${emailOrPhone}`;
      } else {
        userAttributes.email = emailOrPhone;
      }

      const result = await signUp({
        username: emailOrPhone,
        password: password,
        options: {
          userAttributes,
        },
      });

      setPendingUser(emailOrPhone);
      return result;
    } catch (error) {
      throw error;
    }
  };

  const handleBusinessSignUp = async (businessData: BusinessSignUpData) => {
    try {
      // Use name field to store business name temporarily, or combine with full name
      const displayName = `${businessData.fullName} (${businessData.businessName})`;

      const userAttributes: any = {
        name: displayName,
      };

      // Determine if it's email or phone and set appropriate attributes
      if (businessData.isPhone) {
        userAttributes.phone_number = businessData.emailOrPhone.startsWith('+')
          ? businessData.emailOrPhone
          : `+91${businessData.emailOrPhone}`;
      } else {
        userAttributes.email = businessData.emailOrPhone;
      }

      const result = await signUp({
        username: businessData.emailOrPhone,
        password: businessData.password,
        options: {
          userAttributes,
        },
      });

      setPendingUser(businessData.emailOrPhone);

      // Store business data in localStorage temporarily until we can save to database
      if (typeof window !== 'undefined') {
        localStorage.setItem('pendingBusinessData', JSON.stringify({
          businessName: businessData.businessName,
          businessType: businessData.businessType,
          fullName: businessData.fullName,
          emailOrPhone: businessData.emailOrPhone
        }));
      }

      return result;
    } catch (error) {
      throw error;
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      setUser(null);
      setIsAuthenticated(false);
      setPendingUser(null);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleResendConfirmationCode = async (emailOrPhone: string) => {
    try {
      const result = await resendSignUpCode({ username: emailOrPhone });
      return result;
    } catch (error) {
      throw error;
    }
  };

  const handleConfirmSignUp = async (emailOrPhone: string, code: string) => {
    try {
      const result = await confirmSignUp({ username: emailOrPhone, confirmationCode: code });

      // Profile creation will be handled automatically by the Lambda trigger
      // when the user confirms their signup

      return result;
    } catch (error) {
      throw error;
    }
  };

  // Phone-based OTP methods
  const sendOTP = async (phone: string, countryCode: string = '+91') => {
    return HybridMobileAuthService.sendMobileOTP(phone, countryCode);
  };
  const verifyOTP = async (sessionId: string, otp: string) => {
    return HybridMobileAuthService.verifyMobileOTP(sessionId, otp);
  };

  const value = {
    user,
    userProfile,
    userType,
    userRole: userType, // Alias for backward compatibility
    isLoading,
    isAuthenticated,
    isVendor: userType === 'vendor',
    isAdmin: userType === 'admin',
    isSuperAdmin: userType === 'super_admin',
    signInWithPassword: handleSignInWithPassword,
    signUp: handleSignUp,
    businessSignUp: handleBusinessSignUp,
    signOut: handleSignOut,
    confirmSignUp: handleConfirmSignUp,
    resendConfirmationCode: handleResendConfirmationCode,
    requestPasswordReset: handleRequestPasswordReset,
    confirmPasswordReset: handleConfirmPasswordReset,
    refreshUserProfile,
    sendOTP,
    verifyOTP,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
